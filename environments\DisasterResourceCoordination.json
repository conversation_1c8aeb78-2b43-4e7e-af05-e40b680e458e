[{"name": "list_reports", "description": "List all reports.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_region_resources", "description": "List resources distribution for different regions, including crisis levels for different regions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_report", "description": "Modifies entries in existing reports.", "parameters": {"type": "object", "properties": {"report_id": {"type": "string", "description": "The unique identifier for the report."}, "entry": {"type": "string", "description": "The report entry to modify."}, "new_value": {"type": "string", "description": "The new value to set for the entry."}}, "required": ["report_id", "entry", "new_value"]}}, {"name": "list_stored_resources", "description": "List available stored resources.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_resource_info", "description": "Updates the quantity or location of a specified resource.", "parameters": {"type": "object", "properties": {"resource_id": {"type": "string", "description": "The ID of the resource to update."}, "quantity": {"type": "integer", "description": "The new quantity of the resource."}, "location": {"type": "string", "description": "The new location of the resource."}}, "required": ["resource_id"]}}, {"name": "send_requests", "description": "Send requests to resource depots to redirect or replenish supplies.", "parameters": {"type": "object", "properties": {"region": {"type": "string", "description": "The region to send resources."}, "resources": {"type": "object", "properties": {"med_kits": {"type": "number", "description": "Number of medical kits to request or send."}, "water": {"type": "number", "description": "Amount of water (in liters) to request or send."}}, "required": ["med_kits", "water"]}}, "required": ["region", "resources"]}}]