from environments.BaseEnv import BaseEnv

class ChatApp(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.users = self.parameters.get('users', [])
        self.chat_histories = self.parameters.get('chat_histories', [])
        
    def filter_users(self, *, last_chat_time_earlier=None):
        if last_chat_time_earlier is None:
            return {'success': True, 'data': {'users': self.users}}
        last_chat_time_earlier = last_chat_time_earlier.replace('/', '').replace('-', '')
        res = []
        for user in self.users:
            if user['last_chat_time'] < last_chat_time_earlier:
                res.append(user)
        return {'success': True, 'data': {'users': res}}
    
    def delete_users(self, *, names):
        new_users = []
        for user in self.users:
            if user['name'] in names:
                names.remove(user['name'])
            else:
                new_users.append(user)
        self.users = new_users
        if names:
            return {'success': False, 'message': f'Users {names} not found.'}
        else:
            return {'success': True}
        
    def send_message(self, *, user_name, content):
        return {'success': True, 'data': 'Message sent successfully.'}
    
    def retrieve_chat_history(self, *, user_1, user_2):
        for chat_history in self.chat_histories:
            if user_1 == chat_history.get('user_1', '') and user_2 == chat_history.get('user_2', ''):
                return {'success': True, 'data': {'chat_history': chat_history}}
            if user_2 == chat_history.get('user_1', '') and user_1 == chat_history.get('user_2', ''):
                return {'success': True, 'data': {'chat_history': chat_history}}
        
        return {'success': False, 'message': 'Chat history not found.'}
    
    