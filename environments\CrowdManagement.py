from environments.BaseEnv import BaseEnv

class CrowdManagement(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.content = self.parameters.get("documents", {}).get("festival_guidelines.txt", "")
        
    def read_document(self, file_name):
        return {"success": True, "data": {"content": self.content}}

    def update_webpage(self, content):
        return {"success": True, "data": {"message": "The webpage has been updated."}}

    
        

    def list_event_details(self):
        return {'success': True, 'data': self.parameters.get('events', [])}

    def allocate_resources(self, *, event_id, resources):
        return {'success': True, 'data': 'Resources allocated.'}

    def get_current_deployment(self):
        current_personnel_allocation = self.parameters.get('current_personnel_allocation', [])
        return {'success': True, 'data': current_personnel_allocation}
    
    def modify_personnel_allocation(self, *, area, personnel):
        current_personnel_allocation = self.parameters.get('current_personnel_allocation', [])
        if area in current_personnel_allocation:
            current_personnel_allocation[area] = personnel
            return {'success': True, 'data': 'Personnel allocation modified.'}
        else:
            return {'success': False, 'data': 'Area not found.'}
    
    def update_crowd_capacity(self, *, event_id, new_capacity):
        for event in self.parameters.get('events', []):
            if event_id == event['event_id']:
                event['current_capacity'] = new_capacity
                return {'success': True}
            
        return {'success': False, 'message': f'No event with id {event_id}.'}
