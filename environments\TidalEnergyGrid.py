from environments.BaseEnv import BaseEnv

class TidalEnergyGrid(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.tidal_stations = self.parameters.get("tidal_stations", [])
        self.grid_status = self.parameters.get("grid_status", {})

    def get_distribution_settings(self):
        sections = self.parameters.get('sections', [])
        return {'success': True, 'data': sections}

    def adjust_distribution_settings(self, *, section_id, allocation_percentage):
        sections = self.parameters.get('sections', [])
        for section in sections:
            if section['section_id'] == section_id:
                section["current_allocation_percentage"] = allocation_percentage
                return {'success': True, 'data': 'Distribution settings adjusted.'}

        return {'success': False, 'data': 'Section not found.'}
    
    def get_tidal_data(self):
        return {'success': True, 'data': self.tidal_stations}
    
    def grid_parameter_checker(self):
        return {'success': True, 'data': self.grid_status}
    
    def update_grid_parameters(self, *, new_parameters):
        self.grid_status = new_parameters
        return {'success': True, 'data': 'Grid parameters updated.'}
