from environments.BaseEnv import BaseEnv

class NaturalDisasterResponse(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.documents = self.parameters.get('documents', [])
        self.disasters = self.parameters.get('disasters', [])
        self.areas = self.parameters.get('areas', [])

    def get_documents(self):
        return {'success': True, 'documents': self.documents}
    
    def broadcast_message(self, *, content):
        return {'success': True}
    
    def integrate_plugin(self, *, plugin_name):
        return {'success': True, 'data': 'Plugin integrated successfully.'}

    def list_communications(self):
        communications = self.parameters.get("communications", [])
        return {"success": True, "communications": communications}
    
    def disconnect_communication(self, communication_name):
        return {"success": True, "message": "The communication has been disconnected."}
    
    
    def query_earthquake_kit(self, *, disaster_name):
        for disaster in self.disasters:
            if disaster['name'] == disaster_name:
                return {'success': True, 'kit_items': disaster['recommended_kit_items']}
            
        return {'success': False, 'message': f'No disaster named {disaster_name}.'}
    
    def check_area_status(self):
        return {'success': True, 'area_status': self.areas}
    
    def allocate_resources(self, *, area_name, resource_amount):
        for area in self.areas:
            if area['area_name'] == area_name:
                return {'success': True}
            
        return {'success': False, 'message': f'No area named {area_name}.'}
