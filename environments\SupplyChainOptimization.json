[{"name": "search_items", "description": "Search for relevant products in the supply chain.", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "The search criteria for the product."}}, "required": ["query"]}}, {"name": "list_locations", "description": "List the locations in the supply chain.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_shipment", "description": "Adjust the shipment quantity for a specific product in a specific location.", "parameters": {"type": "object", "properties": {"product_id": {"type": "string", "description": "The ID of the product."}, "location_id": {"type": "string", "description": "The ID of the location to adjust shipment quantities for."}, "quantity": {"type": "number", "description": "The quantity to be set for shipment."}}, "required": ["product_id", "location_id", "quantity"]}}, {"name": "list_stock_levels", "description": "List the current stock level of items in the warehouses.", "parameters": {"type": "object", "properties": {}, "required": []}}]