from environments.BaseEnv import BaseEnv


class MemoryAugmentationSystem(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.users = self.parameters.get("users", [])
        self.files = self.parameters.get("files", [])
        self.user_notes = self.parameters.get("user_notes", {})
        self.memories = self.parameters.get('memories', [])
        self.patient_diagnostics = self.parameters.get('patient_diagnostics', [])
        self.nodes = self.parameters.get('nodes', [])

    def delete_documents(self, document_ids):
        return {"success": True ,"message": "The documents have been deleted."}

    def list_users(self):
        return {"success": True, "data": self.users}

    def list_files(self):
        return {"success": True, "data": self.files}

    def fetch_user_notes(self, *, user):
        if user in self.user_notes:
            return {"success": True, "data": self.user_notes[user]}
        return {"success": False, "message": "User not found."}
    
    def list_documents(self):
        docs = self.parameters.get("documents", [])
        return {"success": True, "docs": docs}
    
    def retrieve_memories(self):
        return {"success": True, "memories": self.memories}
    
    def update_log(self, *, title, amount, date):
        self.memories.append({"title": title, "amount": amount, "tags": []})
        return {"success": True}

    def send_info(self, *, content):
        return {"success": True}
    
    def get_patient_diagnostics(self):
        return {"success": True, "patient_diagnostics": self.patient_diagnostics}
    
    def list_nodes(self):
        return {"success": True, "nodes": self.nodes}
    
    def update_memory_allocation(self, *, node_id, memory):
        for node in self.nodes:
            if node['node_id'] == node_id:
                node['current_memory'] = memory
                return {'success': True}
            
        return {'success': False, 'message': f'No node with id {node_id}.'}