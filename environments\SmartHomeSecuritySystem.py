from environments.BaseEnv import BaseEnv

class SmartHomeSecuritySystem(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        
    def search_sensors(self):
        sensors = self.parameters.get("sensors", [])
        return {"success": True, "sensors": sensors}
    
    def update_sensor_settings(self, sensor_id, alert_setting):
        return {"success": True, "message": "The setting has been updated."}

    def list_firmwares(self):
        firmwares = self.parameters.get('firmwares', [])
        current_version = self.parameters.get('current_version', '1.0.0')
        return {'success': True, 'data': {'firmwares': firmwares, 'current_version': current_version}}

    def install_firmware(self, *, firmware_id):
        return {'success': True, 'data': 'Firmware installed.'}
    