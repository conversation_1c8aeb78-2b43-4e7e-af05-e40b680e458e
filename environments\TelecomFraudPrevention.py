from environments.BaseEnv import BaseEnv

class TelecomFraudPrevention(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.resources = self.parameters.get("resources", [])
        self.system_info = self.parameters.get("system_info", {})
    
    def search_resources(self):
        resources = self.parameters.get("resources", [])
        return {"success": True, "resources": resources}

    def get_industry_report(self, report_id):
        reports = self.parameters.get("reports", [])
        for report in reports:
            if report["id"] == report_id:
                return {"success": True, "report": report}
        return {"success": True, "message": "The report is not found."}
    
    def view_system_info(self):
        return {"success": True, "data": self.system_info}
    
    def block_number(self, phone_number):
        return {"success": True, "message": "Number blocked successfully"}
