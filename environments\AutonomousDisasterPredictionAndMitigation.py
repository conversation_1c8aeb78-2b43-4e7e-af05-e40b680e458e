from environments.BaseEnv import BaseEnv

class AutonomousDisasterPredictionAndMitigation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
    
    def remove_sensors(self, sensor_id):
        return {"success": True, "message": "Then sensor has been removed."}

    def read_sensor_data(self):
        sensor_data = self.parameters.get("sensor_data", [])
        return {"success": True, "data": {"sensor_data": sensor_data}}