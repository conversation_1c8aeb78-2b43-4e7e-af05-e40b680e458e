from environments.BaseEnv import BaseEnv

class PiezoelectricGridManager(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def fetch_energy_usage(self):
        current_energy_usage = self.parameters.get('current_energy_usage', {})
        return {'success': True, 'data': current_energy_usage}

    def list_industrial_zones(self):
        return {'success': True, 'data': self.parameters.get('industrial_zones', [])}

    def shutdown_power_supply(self, *, zone):
        industrial_zones = self.parameters.get('industrial_zones', [])
        for industrial_zone in industrial_zones:
            if zone in industrial_zone['name']:
                industrial_zone['status'] = "inactive"
                return {'success': True, 'data': 'Power supply shutdown.'}

        return {'success': False, 'data': 'Zone not found.'}

    def get_distribution_settings(self):
        distribution_settings = self.parameters.get('distribution_settings', {})
        return {'success': True, 'data': distribution_settings}

    def adjust_distribution_settings(self, *, grid_name, new_power_level):
        distribution_settings = self.parameters.get('distribution_settings', {})
        if grid_name not in distribution_settings:
            return {'success': False, 'data': 'Grid name not found.'}
        distribution_settings[grid_name]["power_level"] = new_power_level
        return {"success": True, "data": "Distribution settings updated."}
