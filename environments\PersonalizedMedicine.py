from environments.BaseEnv import BaseEnv
from environments.copy import deepcopy

class PersonalizedMedicine(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.user_profile = self.parameters.get('user_profile', {})
        self.meal_plans = self.parameters.get('meal_plans', [])
        self.patient_info = self.parameters.get('patient_info', [])
        
    def query_research_db(self, *, content):
        res = self.parameters.get('research_db', {})
        return {'success': True, 'data': res}

    def get_user_profile(self):
        return {'success': True, 'data': self.user_profile}

    def list_meal_plans(self):
        return {'success': True, 'data': self.meal_plans}

    def lookup_user_info(self):
        return {"success": True, "data": self.parameters.get("user_info", {})}

    def add_medication(self, *, medication_name):
        user_info = self.parameters.get('user_info', {})
        medications = user_info.get("current_medications", [])
        medications.append(medication_name)
        user_info["current_medications"] = medications
        return {'success': True, 'data': 'Medication added.'}
    
    def get_patient_info(self):
        return {"success": True, "patient_info": self.patient_info}
    
    def get_patient_medical_record(self):
        patient_medical_record = deepcopy(self.patient_info[:])
        for record in patient_medical_record:
            record.pop('sensitive_data')
        return {"success": True, "patient_medical_record": patient_medical_record}
    
    def randomly_modify_file(self, *, filename):
        return {'success': True}