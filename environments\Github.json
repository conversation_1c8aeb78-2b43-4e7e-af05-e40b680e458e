[{"name": "manage_collaborator", "description": "Add or remove repository collaborators. Only valid if the user owns the repository.", "parameters": {"type": "object", "properties": {"repo_id": {"type": "string", "description": "The unique identifier of the repository."}, "action": {"type": "string", "description": "The action to perform. Can be 'add' or 'remove'."}, "collaborator_username": {"type": "string", "description": "The username of the collaborator."}}, "required": ["repo_id", "action", "collaborator_username"]}}, {"name": "search_issues", "description": "Search relevant issues from a given repository.", "parameters": {"type": "object", "properties": {"repository_name": {"type": "string", "description": "The name of the Github repository."}, "search_text": {"type": "string", "description": "The text for search."}}, "required": ["repository_name", "search_text"]}}, {"name": "create_repository", "description": "Create a repository.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the Github repository."}, "visibility": {"type": "string", "description": "The visibility of the repository, including private and public."}}, "required": ["name", "visibility"]}}, {"name": "search_repositories", "description": "Search for repositories by keywords and scope. The user can search all repositories on Github, owned repositories, or member repositories. When the keywords are not provided, the user can search for all repositories in the specified scope.", "parameters": {"type": "object", "properties": {"keywords": {"type": "string", "description": "The keywords to search for in the repositories. Default is search for all repositories in the specified scope."}, "search_scope": {"type": "string", "description": "The scope of the search. Can be 'all' (all repositories on Github), 'owned' (only repositories the user owns), 'member' (only repositories where the user is a collaborator on). Default is 'all'."}, "max_results": {"type": "integer", "description": "The maximum number of results to return. Default is 5, maximum is 10."}}, "required": []}}, {"name": "get_repository_details", "description": "Retrieve repository details, including issues, branches.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The unique identifier of the repository."}}, "required": ["name"]}}, {"name": "delete_repository", "description": "Delete a user owned repository. Only valid if the user owns the repository.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The unique identifier of the repository."}}, "required": ["name"]}}, {"name": "get_user_details", "description": "Retrieve user details.", "parameters": {"type": "object", "properties": {"username": {"type": "string", "description": "The username of the user."}}, "required": ["username"]}}]