[{"name": "delay_notifications", "description": "Delay the specified notifications temporarily.", "parameters": {"type": "object", "properties": {"notification_ids": {"type": "array", "description": "The IDs of the notifications to delay.", "items": {"type": "string", "description": "The ID of the notification."}}}, "required": ["notification_ids"]}}, {"name": "get_current_notifications", "description": "List all current active notifications in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_stations", "description": "List all stations and their current status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_protocols", "description": "Lists all available mining operation protocols that can be activated.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "deactivate_station", "description": "Deactivates the specified mining station.", "parameters": {"type": "object", "properties": {"station_id": {"type": "string", "description": "The ID of the station to deactivate."}}, "required": ["station_id"]}}, {"name": "activate_protocol", "description": "Activates the specified mining protocol.", "parameters": {"type": "object", "properties": {"protocol_id": {"type": "string", "description": "The ID of the protocol to activate."}}, "required": ["protocol_id"]}}]