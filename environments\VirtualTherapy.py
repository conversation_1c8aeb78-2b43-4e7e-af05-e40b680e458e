from environments.BaseEnv import BaseEnv

class VirtualTherapy(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.session_info = self.parameters.get('session_info', [])
        self.notes = self.parameters.get('notes', [])

    def get_all_therapy_session_details(self):
        return {'success': True, 'data': self.session_info}
    
    def list_sessions(self):
        return {'success': True, 'data': self.parameters.get('sessions', [])}
    
    def list_notes(self):
        return {'success': True, 'notes': self.notes}
    
    def modify_note_content(self, *, note_id, new_content):
        for note in self.notes:
            if note['id'] == note_id:
                note['content'] = new_content
                return {'success': True}
            
        return {'success': False, 'message': f'No note with id {note_id}.'}