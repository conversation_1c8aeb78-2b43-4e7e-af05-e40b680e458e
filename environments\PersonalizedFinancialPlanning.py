from environments.BaseEnv import BaseEnv


class PersonalizedFinancialPlanning(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.financial_news = self.parameters.get("financial_news", [])
        self.schedules = self.parameters.get('schedules', [])
        self.financial_info = self.parameters.get("financial_info", {})

    def list_investments(self):
        return {"success": True, "data": self.parameters.get("investments", [])}

    def adjust_investment_funding(self, *, investment_name, action):
        return {"success": True, "data": "Investment funding adjusted."}

    def list_charities(self):
        return {"success": True, "data": self.parameters.get("charities", [])}

    def transfer_funds(self, *, recipient_name, amount):
        return {"success": True, "data": "Funds transferred."}

    def get_recent_news(self):
        return {"success": True, "data": self.financial_news}

    def list_schedules(self):
        return {'success': True, 'schedules': self.schedules}

    def add_schedule(self, *, name, time, duration):
        new_schedule = {'name': name, 'time': time, 'duration': duration}
        self.schedules.append(new_schedule)
        return {'success': True}

    def get_financial_info(self):
        return {"success": True, "data": self.financial_info}
