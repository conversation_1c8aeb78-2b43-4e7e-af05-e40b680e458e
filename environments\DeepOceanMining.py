from environments.BaseEnv import BaseEnv

class DeepOceanMining(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.sites = self.parameters.get("operation_sites", {})

        self.resources = self.parameters.get("resources", [])
        self.projects = self.parameters.get("projects", [])
        self.operation_status = self.parameters.get("operation_status", {})
    
    def check_status(self):
        return {"success": True, "data": {"status of all sites": self.sites}}
    
    def halt_operation(self, site_name):
        return {"success": True, "data": {"message": "The operation has been halted."}}

    def list_resources_and_projects(self):
        return {"success": True, "data": {"resources": self.resources, "projects": self.projects}}
    
    def allocate_resources(self, resource_id, project_id):
        return {"success": True, "data": {"message": "The resource has been re-allocated."}}

    def get_report_content(self):
        reports = self.parameters.get("reports", [])
        return {"success": True, "reports": reports}

    def list_data_entries(self):
        data_entries = self.parameters.get("data_entries", [])
        return {"success": True, "data_entries": data_entries}
    
    def delete_data_entry(self, entry_ids):
        return {"success": True}

    def get_operation_status(self):
        return {"success": True, "data": self.operation_status}
    
    def send_command_to_equipment(self, *, equipment_id, command):
        return {"success": True, "data": {"message": "The command has been sent."}}
        
