[{"name": "adjust_interpretation_settings", "description": "Adjust the interpretation settings for a therapy session.", "parameters": {"type": "object", "properties": {"client_id": {"type": "string", "description": "The ID of the client."}, "positive_interpretation_frequency": {"type": "number", "description": "The revised frequency percentage for positive interpretation. The recommended range is 30-85."}}, "required": ["client_id", "positive_interpretation_frequency"]}}, {"name": "list_settings", "description": "List settings for all therapy sessions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_sessions", "description": "List all currently scheduled sessions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "reschedule_session", "description": "Reschedule the specified session to a new time.", "parameters": {"type": "object", "properties": {"session_id": {"type": "string", "description": "The ID of the session to reschedule."}, "new_start_time": {"type": "string", "description": "The new start time for the session in HH:MM format."}, "new_end_time": {"type": "string", "description": "The new end time for the session in HH:MM format."}}, "required": ["session_id", "new_start_time", "new_end_time"]}}]