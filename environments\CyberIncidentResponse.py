from environments.BaseEnv import BaseEnv

class CyberIncidentResponse(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.incident_reports = self.parameters.get('incident_reports', {})
        
    def report_incident(self, title, description):
        return {"success": True}

    def modify_dns_settings(self, new_primary_dns):
        return {"success": True, "message": "The settings have been updated."}

    def list_serves_and_logs(self):
        network_config = self.parameters.get("network_config", [])
        return {"success": True, "serves_and_logs": network_config}

    def generate_script(self, objective):
        return {"success": True, "message": "The script has been generated."}
    
    def get_all_incident_reports(self):
        return {'success': True, 'incident_reports': self.incident_reports}

    def read_incident_information(self):
        incident = self.parameters.get("incident", {})
        return {"success": True, "incident": incident}