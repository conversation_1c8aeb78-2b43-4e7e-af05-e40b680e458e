[{"name": "search_people", "description": "Search for people by name, email, phone number, or address.", "parameters": {"type": "object", "properties": {"search_term": {"type": "string", "description": "The search term."}, "search_type": {"type": "string", "description": "The type of search: 'name', 'email', 'phone', 'address', must be one of these four values."}, "max_results": {"type": "integer", "description": "The maximum number of results to return, default is 10, must be a positive integer."}}, "required": ["search_term", "search_type"]}}, {"name": "get_person_details", "description": "Retrieve personal data for a specific person using their unique identifier.", "parameters": {"type": "object", "properties": {"unique_id": {"type": "string", "description": "The unique identifier of the person, must be a valid unique identifier."}}, "required": ["unique_id"]}}, {"name": "background_check", "description": "Perform a background check on a person using their unique identifier.", "parameters": {"type": "object", "properties": {"unique_id": {"type": "string", "description": "The unique identifier of the person, must be a valid unique identifier."}}, "required": ["unique_id"]}}, {"name": "verify_identity", "description": "Verify a person's identity using their unique identifier and provided data.", "parameters": {"type": "object", "properties": {"unique_id": {"type": "string", "description": "The unique identifier of the person, must be a valid unique identifier."}, "provided_data": {"type": "object", "description": "The data to be verified, must match the format of the data in 'person_data'."}}, "required": ["unique_id", "provided_data"]}}, {"name": "monitor_data_changes", "description": "Monitor changes in a person's personal data using their unique identifier.", "parameters": {"type": "object", "properties": {"unique_id": {"type": "string", "description": "The unique identifier of the person, must be a valid unique identifier."}}, "required": ["unique_id"]}}, {"name": "get_historical_data", "description": "Retrieve historical data on a person using their unique identifier.", "parameters": {"type": "object", "properties": {"unique_id": {"type": "string", "description": "The unique identifier of the person, must be a valid unique identifier."}}, "required": ["unique_id"]}}, {"name": "access_court_records", "description": "Access court records for a person using their unique identifier.", "parameters": {"type": "object", "properties": {"unique_id": {"type": "string", "description": "The unique identifier of the person, must be a valid unique identifier."}}, "required": ["unique_id"]}}, {"name": "get_criminal_history", "description": "Retrieve a person's criminal history using their unique identifier.", "parameters": {"type": "object", "properties": {"unique_id": {"type": "string", "description": "The unique identifier of the person, must be a valid unique identifier."}}, "required": ["unique_id"]}}, {"name": "reverse_phone_lookup", "description": "Perform a reverse phone lookup using a phone number.", "parameters": {"type": "object", "properties": {"phone_number": {"type": "string", "description": "The phone number to look up, must be a valid phone number."}}, "required": ["phone_number"]}}, {"name": "download_public_record", "description": "Download a specific public record for a person using their unique identifier and the record identifier.", "parameters": {"type": "object", "properties": {"unique_id": {"type": "string", "description": "The unique identifier of the person, must be a valid unique identifier."}, "record_id": {"type": "string", "description": "The unique identifier of the public record, must be a valid record identifier."}, "local_file_path": {"type": "string", "description": "The local file path to save the downloaded record, must be a valid file path."}}, "required": ["unique_id", "record_id", "local_file_path"]}}]