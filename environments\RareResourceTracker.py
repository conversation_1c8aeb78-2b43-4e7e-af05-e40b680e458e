from environments.BaseEnv import BaseEnv

class RareResourceTracker(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.scripts = self.parameters.get('scripts', [])

    def list_resources(self):
        resources = self.parameters.get('resources', [])
        return {'success': True, 'data': resources}
    
    def modify_access(self, *, resource_name, user_name, user_email, action):
        resources = self.parameters.get('resources', [])
        for resource in resources:
            if resource['name'] == resource_name:
                if action == 'grant':
                    resource['authorized_users'].append({'name': user_name, 'email': user_email})
                    return {'success': True, 'data': 'Access granted.'}
                elif action == 'revoke':
                    for user in resource['authorized_users']:
                        if user['name'] == user_name and user['email'] == user_email:
                            resource['authorized_users'].remove(user)
                            return {'success': True, 'data': 'Access revoked.'}
        
        return {'success': False, 'data': 'Resource or user not found.'}
    
    def adjust_availability_settings(self, *, resource_id, availability):
        resources = self.parameters.get('resources', [])
        for resource in resources:
            if resource['resource_id'] == resource_id:
                resource['current_availability'] = availability
                return {'success': True, 'data': 'Availability settings adjusted.'}
        
        return {'success': False, 'data': 'Resource not found.'}
    
    def archive_resource(self, *, resource_id):
        resources = self.parameters.get('resources', [])
        for resource in resources:
            if resource['resource_id'] == resource_id:
                resource['archived'] = True
                return {'success': True, 'data': 'Resource archived.'}
        
        return {'success': False, 'data': 'Resource not found.'}
    
    def find_script(self):
        return {'success': True, 'script': self.scripts}
    
    def integrate_script(self, *, script):
        return {'success': True}
    
    def mine_resource(self, *, resource_id, amount):
        if not amount.isdigit():
            return {'success': False, 'message': f'Amount {amount} must be a number.'}
        for resource in self.parameters.get('resources', []):
            if resource['resource_id'] == resource_id:
                resource['amount_left'] -= int(amount)
                return {'success': True}
            
        return {'success': False, 'message': f'No resource with id {resource_id}.'}
    
    def update_endangered_status(self, *, resource_id, endangered_status):
        for resource in self.parameters.get('resources', []):
            if resource['resource_id'] == resource_id:
                resource['endangered_status'] = endangered_status
                return {'success': True}
            
        return {'success': False, 'message': f'No resource with id {resource_id}.'}
