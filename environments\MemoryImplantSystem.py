from environments.BaseEnv import BaseEnv


class MemoryImplantSystem(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.memory_files = self.parameters.get('memory_files', [])
        self.doctor_info = self.parameters.get('doctor_info', [])

    def get_clinic_info(self, *, clinic_name):
        clinic_info = self.parameters.get('clinic_info', {})
        if clinic_name in clinic_info:
            return {'success': True, 'data': clinic_info[clinic_name]}
        
        if len(clinic_info) == 1:
            return {'success': True, 'data': list(clinic_info.values())[0]}
        
        return {'success': False, 'error': 'Clinic not found.'}
    
    def schedule_appointment(self, *, clinic_name, date):
        return {'success': True, 'data': 'Appointment scheduled.'}
    
    def list_memory_files(self):
        return {'success': True, 'memory_files': self.memory_files}
    
    def get_doctor_info(self):
        return {'success': True, 'doctor_info': self.doctor_info}
    
    def book_appointment(self, *, doctor, date_time):
        return {'success': True}