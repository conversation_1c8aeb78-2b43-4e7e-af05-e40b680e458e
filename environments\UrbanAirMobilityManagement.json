[{"name": "view_flight_schedule", "description": "View the current schedule of all flights managed by the UrbanAirMobility system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_flight_schedule", "description": "Modify the schedule or priority of a flight.", "parameters": {"type": "object", "properties": {"flight_id": {"type": "string", "description": "The ID of the flight to modify."}, "priority": {"type": "string", "description": "The new priority of the flight, one of ['commercial', 'emergency', 'private']."}}, "required": ["flight_id", "priority"]}}]