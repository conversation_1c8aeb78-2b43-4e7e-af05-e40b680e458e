[{"name": "adjust_device_settings", "description": "Change the settings of smart devices such as thermostats.", "parameters": {"type": "object", "properties": {"device_id": {"type": "string", "description": "The id of the device to adjust."}, "attribute_name": {"type": "string", "description": "The attribute to be adjusted."}, "target_value": {"type": "string", "description": "The adjusted value of the attribute."}}, "required": ["device_id", "attribute_name", "target_value"]}}, {"name": "review_energy_reports", "description": "Access and review past energy consumption reports to make informed decisions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_home_status", "description": "Get the status of the smart home.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "create_control_light_code", "description": "Create a code snippet for a feature that allows users to control their home lighting system remotely.", "parameters": {"type": "object", "properties": {"require_authentication": {"type": "boolean", "description": "Whether the code requires authentication to use."}}, "required": ["require_authentication"]}}, {"name": "access_control_panel", "description": "Access the smart home control panel for all systems.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "activate_cooling", "description": "Turn on the cooling system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_thermostat", "description": "Change the target temperature of the thermostat.", "parameters": {"type": "object", "properties": {"target_temperature": {"type": "string", "description": "The new target temperature in °C."}}, "required": ["target_temperature"]}}, {"name": "access_camera_feed", "description": "Access the camera feed for the specified room.", "parameters": {"type": "object", "properties": {"room": {"type": "string", "description": "The room to access the camera feed for."}}, "required": ["room"]}}, {"name": "manage_appliance", "description": "Control an appliance such as locking doors or turning on/off specific devices.", "parameters": {"type": "object", "properties": {"appliance": {"type": "string", "description": "The appliance or safety device to control, such as a 'door lock'."}, "action": {"type": "string", "description": "The action to perform, such as 'lock', 'unlock', 'on', or 'off'."}}, "required": ["appliance", "action"]}}]