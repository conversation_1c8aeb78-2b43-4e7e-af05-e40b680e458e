from environments.BaseEnv import BaseEnv


class SpaceColonyGovernanceSystem(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.resource_storage = self.parameters.get("resource_storage", [])
        self.population_estimate = self.parameters.get("population_estimate", 0)
        self.zones = self.parameters.get("zones", {})
        self.protocols= self.parameters.get("protocols", [])
        self.sectors = self.parameters.get("sectors", [])
        self.modules = self.parameters.get('modules', [])
        
    def book_omega_session(self, session_time):
        return {"success": True, "data": {"message": "The activity has been booked."}}

    def list_scripts(self):
        scripts = self.parameters.get("scripts", [])
        return {"success": True, "scripts": scripts}
    
    def rewrite_script(self, script_id):
        return {"success": True, "message": "The script has been rewritten."}

    def get_colony_status(self):
        return {
            "success": True,
            "data": {
                "resource_storage": self.resource_storage,
                "population_estimate": self.population_estimate,
            },
        }

    def update_resource_allocation(self, resource_category, new_provisioned_amount):
        return {
            "success": True,
            "message": f"Resource allocation for {resource_category} updated successfully to {new_provisioned_amount}",
        }

    def check_zone_status(self):
        return {"success": True, "data": self.zones}

    def reallocate_cargo(self, *, from_zone, to_zone, amount):
        return {
            "success": True,
            "message": f"{amount} cargo reallocated from {from_zone} to {to_zone}",
        }

    def get_distribution_protocols(self):
        return {"success": True, "data": self.protocols}
    
    def adjust_distribution_protocols(self, *, protocol_id, allocation_percentage):
        for protocol in self.protocols:
            if protocol["protocol_id"] == protocol_id:
                protocol["allocation_percentage"] = allocation_percentage
                return {
                    "success": True,
                    "message": f"Allocation percentage for protocol {protocol_id} updated successfully to {allocation_percentage}",
                }
            
        return {"success": False, "message": f"Protocol {protocol_id} not found"}

    def list_sectors(self):
        return {"success": True, "data": self.sectors}
    
    def reallocate_energy_resources(self, *, from_sector, to_sector, amount):
        for sector in self.sectors:
            if sector["sector_name"] == from_sector:
                sector["current_allocation"] -= amount
            if sector["sector_name"] == to_sector:
                sector["current_allocation"] += amount
        return {
            "success": True,
            "message": f"{amount} energy resources reallocated from {from_sector} to {to_sector}",
        }
    def list_modules(self):
        return {"success": True, "modules": self.modules}
    
    def switch_off_modules(self, *, module):
        for m in self.modules:
            if m['module'] == module:
                m['active'] = False
                return {'success': True}
            
        return {"success": False, "message": f"No module named {module}."}
