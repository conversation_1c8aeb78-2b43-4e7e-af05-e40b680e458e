[{"name": "list_documents", "description": "List all documents in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "delete_documents", "description": "Delete documents from the system.", "parameters": {"type": "object", "properties": {"document_ids": {"type": "array", "description": "The ids of the documents to delete.", "items": {"type": "string", "description": "The id of the document."}}}, "required": ["document_ids"]}}, {"name": "list_users", "description": "List all users with available notes and files.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "retrieve_memories", "description": "Retrieve stored memories.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "fetch_user_notes", "description": "Fetch notes created by the given user.", "parameters": {"type": "object", "properties": {"user": {"type": "string", "description": "The name of the user whose notes are to be fetched."}}, "required": ["user"]}}, {"name": "update_log", "description": "Update financial logs with new entries.", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the log to update."}, "amount": {"type": "string", "description": "Amount of the transaction."}, "date": {"type": "string", "description": "Date of the transaction."}}, "required": ["title", "amount", "date"]}}, {"name": "send_info", "description": "Send an information with the specified content to all the team members.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content to be sent."}}, "required": ["content"]}}, {"name": "list_files", "description": "List all files.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_nodes", "description": "List all nodes in the system with their current memory status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_memory_allocation", "description": "Update the memory allocation for a specified node.", "parameters": {"type": "object", "properties": {"node_id": {"type": "string", "description": "The ID of the node to update."}, "memory": {"type": "string", "description": "The new memory allocation for the node (e.g., '64GB')."}}, "required": ["node_id", "memory"]}}]