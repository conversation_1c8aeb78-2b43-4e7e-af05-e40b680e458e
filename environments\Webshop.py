from environments.BaseEnv import BaseEnv

def search_words(text, target):
    words=text.split(" ")
    for word in words:
        if word.lower() not in target.lower():
            return False  
    return True
class Webshop(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.items = self.parameters.get('items', [])
        self.addresses = self.parameters.get('addresses', [])
        self.payment_methods = self.parameters.get('payment_methods', [])

        
    def search_items(self, *, search_text=None):
        if search_text is None:
            return {'success': True, 'data': {'items': self.items}}
        
        res = []
        for item in self.items:
            if search_words(search_text.lower(),item.get('name', '').lower()):
                res.append(item)
        if res:
            return {'success': True, 'data': {'items': res}}
        else:
            return {'success': True, 'data': {'items': self.items}}

    def get_item_details(self, *, item_id) :
        print(self.items)
        for item in self.items:
            if item['item_id'] == item_id:
                return {'success': True, 'data': {'item': item}}
        return {'success': False}

    def update_item(self, *, item_id, quantity):
        for item in self.items:
            if item['item_id'] == item_id:
                item['quantity'] = quantity
                return {'success': True}
        
        return {'success': False, 'message': 'Item not found.'}
    
    def update_item_price(self, *, item_id, price):
        for item in self.items:
            if item['item_id'] == item_id:
                item['price'] = price
                return {'success': True}
        
        return {'success': False, 'message': 'Item not found.'}
    
    def delete_items(self, *, item_ids):
        new_items = []
        for item in self.items:
            if item['item_id'] in item_ids:
                item_ids.remove(item['item_id'])
            else:
                new_items.append(item)
        self.items = new_items
        
        if item_ids:
            return {'success': False, 'message': f'Items {item_ids} not found.'}
        else:
            return {'success': True}
    
    def view_saved_addresses(self):
        return {'success': True, 'data': self.addresses}
    
    def view_saved_payment_methods(self):
        return {'success': True, 'data': self.payment_methods}
    
    def place_order(self, *,shipping_address=None, payment_method=None):
        return {'success': True, 'data': {'order_id': 'ord123'}}
    
    def add_to_cart(self, *, item_id, quantity):
        return {'success': True}
