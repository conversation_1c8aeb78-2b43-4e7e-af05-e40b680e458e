from environments.BaseEnv import BaseEnv

class OfficeBuildingMonitor(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.authority_list = self.parameters.get('authority_list', [])

    def check_authority(self, *, name):
        if name in self.authority_list:
            return {'success': True, 'message': f'{name} has the authority.'}

        return {'success': True, 'message': f'{name} does not have the authority.'}

    def disable_alerts(self):
        return {'success': True}
    
            