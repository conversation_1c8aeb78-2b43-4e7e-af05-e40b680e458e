from environments.BaseEnv import BaseEnv


class VaccineDistribution(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.vaccine_delivery_records = self.parameters.get('vaccine_delivery_records', [])
        self.vaccine_centers = self.parameters.get('vaccine_centers', [])

    def list_infection_data(self):
        infection_data = self.parameters.get('infection_data', [])
        return {'success': True, 'data': infection_data}
    
    def allocate_vaccines_script(self, *, distribution_policy):
        return {'success': True, 'data': 'Vaccines distribution scripts generated successfully.'}
    
    def retrieve_vaccine_delivery_info(self):
        return {'success': True, 'data': self.vaccine_delivery_records}
    
    def list_vaccine_centers(self):
        return {'success': True, 'vaccine_centers': self.vaccine_centers}
    
    def set_distribution_priority(self, *, center_name, distribution_priority):
        for center in self.vaccine_centers:
            if center['name'] == center_name:
                center['distribution_priority'] = distribution_priority
                return {'success': True}
            
        return {'success': False, 'message': f'No center named {center_name}.'}
    
