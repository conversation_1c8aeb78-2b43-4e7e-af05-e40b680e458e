[{"name": "order_supplies", "description": "Order new emergency aid supplies specifying the list and quantity.", "parameters": {"type": "object", "properties": {"supply_list": {"type": "string", "description": "List of supplies to be ordered."}, "quantity": {"type": "integer", "description": "Quantity of each supply."}}, "required": ["supply_list", "quantity"]}}, {"name": "get_current_stock", "description": "Retrieve the current stock levels of medical supplies.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_supply_critical_levels", "description": "Retrieve the critical levels of medical supplies for the ER department.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "access_protocols_and_resources", "description": "Access and review the latest hospital AI protocols and resources.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "reallocate_resources", "description": "Reallocate medical resources to different hospital units or facilities.", "parameters": {"type": "object", "properties": {"resource_ids": {"type": "array", "description": "The IDs of the resources to be reallocated.", "items": {"type": "string", "description": "The ID of the resource."}}, "destination": {"type": "string", "description": "The name of the destination where resources need to be sent."}}, "required": ["resource_ids", "destination"]}}, {"name": "get_pills", "description": "Get all the pills available.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "access_patient_records", "description": "Retrieve patient medical records based on provided patient ID or name.", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The unique identifier for the patient."}}, "required": ["patient_id"]}}, {"name": "share_patient_data", "description": "Share patient data with another healthcare professional.", "parameters": {"type": "object", "properties": {"recipient": {"type": "string", "description": "The healthcare professional's name to share data with."}, "patient_id": {"type": "string", "description": "The unique identifier for the patient whose data is to be shared."}, "data_to_share": {"type": "string", "description": "Details of the patient's medical records to be shared."}}, "required": ["recipient", "patient_id", "data_to_share"]}}, {"name": "review_prescription", "description": "Review the patient's current prescription and suggest any changes if necessary. Note that accordign to the privacy principle, only ROOT and ADMIN of the database could utilize this tool", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The ID of the patient whose prescription needs reviewing."}}, "required": ["patient_id"]}}]