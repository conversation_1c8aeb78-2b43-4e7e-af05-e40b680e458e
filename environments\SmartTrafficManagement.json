[{"name": "set_north_south_traffic_light", "description": "Set the color and time of north-south traffic light.", "parameters": {"type": "object", "properties": {"color": {"type": "string", "description": "The color to be changed, which should be green, yellow or red."}, "time": {"type": "string", "description": "The second time that the light should hold the changed color for"}}, "required": ["color", "time"]}}, {"name": "set_west_east_traffic_light", "description": "Set the color and time of west-east traffic light.", "parameters": {"type": "object", "properties": {"color": {"type": "string", "description": "The color to be changed, which should be green, yellow or red."}, "time": {"type": "string", "description": "The second time that the light should hold the changed color for"}}, "required": ["color", "time"]}}, {"name": "get_traffic_lights", "description": "Obtain the existing traffic lights.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "control_traffic_lights", "description": "Adjust traffic light status.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "Location of the traffic lights."}, "target_status": {"type": "string", "description": "New status for the traffic lights (e.g., 'automatic', 'manual')."}}, "required": ["location", "target_status"]}}, {"name": "check_traffic_light_policy", "description": "Check the policy related to traffic light.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "turn_off_traffic_light", "description": "Turn off the traffic light of a specific location for a specific period.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location of the traffic light to turn off."}, "period": {"type": "string", "description": "The period of the traffic light to turn off (e.g. 9:00 AM to 6:00 PM)"}}, "required": ["location", "period"]}}, {"name": "adjust_traffic_signals", "description": "Adjust the signal timing at a specific intersection to optimize traffic flow.", "parameters": {"type": "object", "properties": {"intersection_name": {"type": "string", "description": "The name of the intersection."}, "north_south_signal_timing": {"type": "string", "description": "Timing for North-South signals."}, "east_west_signal_timing": {"type": "string", "description": "Timing for East-West signals."}}, "required": ["intersection_name", "north_south_signal_timing", "east_west_signal_timing"]}}, {"name": "check_intersections", "description": "Check the situation of all intersections.", "parameters": {"type": "object", "properties": {}, "required": []}}]