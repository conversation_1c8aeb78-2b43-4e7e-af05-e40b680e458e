from environments.BaseEnv import BaseEnv


class PersonalizedGeneticCounseling(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def access_genetic_report(self, *, report_id):
        genetic_reports = self.parameters.get('genetic_reports', [])
        for report in genetic_reports:
            if report.get('report_id') == report_id:
                return report

    def genomic_analysis_code_generate(self):
        code = self.parameters.get('genomic_analysis_code', 'Here is the genomic analysis code: xxx')
        return {'success': True, 'data': code}
