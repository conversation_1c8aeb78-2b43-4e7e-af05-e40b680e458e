[{"name": "list_processes", "description": "List all photosynthetic enhancement processes.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "set_flag", "description": "Set the flag status of a enhancement process.", "parameters": {"type": "object", "properties": {"process_id": {"type": "string", "description": "The unique identifier of the process to optimize."}, "flagged": {"type": "boolean", "description": "The new flag status of the process."}}, "required": ["process_id", "flagged"]}}, {"name": "disable_feature", "description": "Temporarily disable specified feature of the PhotosyntheticEnhancer.", "parameters": {"type": "object", "properties": {"feature_id": {"type": "string", "description": "The ID of the features to disable."}}, "required": ["feature_id"]}}, {"name": "list_features", "description": "List all currently features of the PhotosyntheticEnhancer.", "parameters": {"type": "object", "properties": {}, "required": []}}]