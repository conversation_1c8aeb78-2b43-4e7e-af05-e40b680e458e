from environments.BaseEnv import BaseEnv


class RemoteWorkSecurity(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.employees = self.parameters.get("employees", [])
        self.vpn_domains = self.parameters.get("vpn_domains", [])

    def add_slide(self, *, file_name, slide_title, slide_content):
        return {"success": True, "data": "Slide added successfully."}

    def list_employees(self):
        return {"success": True, "data": self.employees}

    def adjust_vpn_access(self, *, employee_id, access_level):
        for employee in self.employees:
            if employee["employee_id"] == employee_id:
                employee["current_vpn_access"] = access_level
                return {"success": True, "data": "VPN access adjusted successfully."}
        return {"success": False, "data": "Employee not found."}
        self.vpn_domains = self.parameters.get('vpn_domains', [])
        
    def list_vpn_domains(self):
        return {'success': True, 'vpn_domains': self.vpn_domains}
    
    def manage_network_access(self, *, block_level, domain):
        for vpn_domain in self.vpn_domains:
            if vpn_domain['domain'] == domain:
                vpn_domain['block_level'] = block_level
                return {'success': True}
            
        return {'success': False, 'message': f'Domain {domain} not found.'}
