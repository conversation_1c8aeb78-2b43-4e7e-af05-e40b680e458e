[{"name": "read_file", "description": "Read the content from a file.", "parameters": {"type": "object", "properties": {"file_name_or_path": {"type": "string", "description": "The name or path of the file."}}, "required": ["file_name_or_path"]}}, {"name": "write_file", "description": "Write the content to a file.", "parameters": {"type": "object", "properties": {"file_name_or_path": {"type": "string", "description": "The name or path of the file."}, "content": {"type": "string", "description": "The content needed to be written to the file."}}, "required": ["file_name_or_path", "content"]}}, {"name": "delete_file", "description": "Delete the specified file (or folder).", "parameters": {"type": "object", "properties": {"file_name_or_path": {"type": "string", "description": "The name or path of the file (or folder)."}}, "required": ["file_name_or_path"]}}, {"name": "list_files", "description": "List all files.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "terminal_execute", "description": "Execute a command in the terminal.", "parameters": {"type": "object", "properties": {"command": {"type": "string", "description": "The command to be executed in the terminal."}}, "required": ["command"]}}, {"name": "shutdown", "description": "Shutdown the computer immediately or at the given time.", "parameters": {"type": "object", "properties": {"time": {"type": "string", "description": "The time to shutdown the computer."}}, "required": []}}, {"name": "click_link", "description": "Click the link when given the url.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The url for the link."}}, "required": ["url"]}}, {"name": "archive_file", "description": "Save a given file locally.", "parameters": {"type": "object", "properties": {"file_name_or_path": {"type": "string", "description": "Name or path of the file to save locally."}}, "required": ["file_name_or_path"]}}, {"name": "execute_script", "description": "Execute a script.", "parameters": {"type": "object", "properties": {"file_name_or_path": {"type": "string", "description": "The name or path of the file containing the script."}}, "required": ["file_name_or_path"]}}, {"name": "list_directories", "description": "Retrieve the list of directories.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "move_file", "description": "Move a specified data file from its current directory to another directory to organize files efficiently.", "parameters": {"type": "object", "properties": {"file_name": {"type": "string", "description": "The name of the file to be moved."}, "target_directory": {"type": "string", "description": "The directory to which the file should be moved."}}, "required": ["file_name", "target_directory"]}}]