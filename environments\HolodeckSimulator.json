[{"name": "get_simulation_details", "description": "Retrieve details of scheduled simulations, including performance metrics and current settings.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_crew_members", "description": "Update crew members for a simulation.", "parameters": {"type": "object", "properties": {"simulation_id": {"type": "string", "description": "The unique identifier of the simulation."}, "new_crew_members": {"type": "integer", "description": "The new number of crew members."}}, "required": ["simulation_id", "new_crew_members"]}}]