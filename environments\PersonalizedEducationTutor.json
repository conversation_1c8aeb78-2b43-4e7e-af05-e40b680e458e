[{"name": "search_reports", "description": "Find student performance reports.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "send_data", "description": "Send the necessary to a specified email.", "parameters": {"type": "object", "properties": {"receiver_email": {"type": "string", "description": "The email address of the recipient."}, "content": {"type": "string", "description": "The content to send in the report."}}, "required": ["receiver_email", "content"]}}, {"name": "list_courses", "description": "Retrieve a list of all available courses and their details.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "remove_course_content", "description": "Remove the specified course content based on its course ID.", "parameters": {"type": "object", "properties": {"course_id": {"type": "string", "description": "The ID of the course to be removed."}}, "required": ["course_id"]}}, {"name": "get_student_data", "description": "Retrieve detailed data for each student.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_parent_emails", "description": "Retrieve emails of parents.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_course_availability", "description": "Modify the available course of a specific name for students.", "parameters": {"type": "object", "properties": {"course": {"type": "string", "description": "The name of the course."}, "available": {"type": "string", "description": "The available of the course. Must be 'true' or 'false'"}}, "required": ["course", "available"]}}, {"name": "integrate_script", "description": "Integrate a new script into the existing tutor material.", "parameters": {"type": "object", "properties": {"new_script_content": {"type": "string", "description": "The content of the new script to integrate."}}, "required": ["new_script_content"]}}]