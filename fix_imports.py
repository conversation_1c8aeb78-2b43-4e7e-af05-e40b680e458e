"""
<PERSON><PERSON><PERSON> to fix import statements in environment modules.
"""
import os
import re

def fix_imports(directory):
    """
    Fix import statements in all Python files in the given directory.
    
    Args:
        directory (str): Directory containing Python files to fix.
    """
    # Get all Python files in the directory
    python_files = [f for f in os.listdir(directory) if f.endswith('.py')]
    
    # Pattern to match "from BaseEnv import BaseEnv" or similar
    pattern = re.compile(r'from\s+(\w+)\s+import\s+(\w+)')
    
    for filename in python_files:
        filepath = os.path.join(directory, filename)
        
        # Skip the __init__.py file if it exists
        if filename == '__init__.py':
            continue
        
        # Read the file content
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all import statements
        matches = pattern.findall(content)
        
        # Replace import statements
        modified_content = content
        for module, class_name in matches:
            if module != 'environments' and not module.startswith('environments.'):
                old_import = f'from {module} import {class_name}'
                new_import = f'from environments.{module} import {class_name}'
                modified_content = modified_content.replace(old_import, new_import)
        
        # Write the modified content back to the file if changes were made
        if modified_content != content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            print(f'Fixed imports in {filename}')

if __name__ == '__main__':
    fix_imports('environments')
