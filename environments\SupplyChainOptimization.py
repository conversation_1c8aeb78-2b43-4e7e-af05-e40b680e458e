from environments.BaseEnv import BaseEnv

class SupplyChainOptimization(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.products = self.parameters.get('products', [])
        self.shipments = self.parameters.get('shipments', [])
        self.locations= self.parameters.get('locations', [])
        self.stock_levels= self.parameters.get('stock_levels', [])
        self.current_access_rights = self.parameters.get("current_access_rights", {})

    def search_items(self, *, query: str):
        """Search for relevant products in the supply chain based on the query."""
        results = [
            product for product in self.products
            if query.lower() in product['product_name'].lower()
        ]
        if results:
            return {'success': True, 'data': {'products': results}}
        return {'success': False, 'error': 'No products found matching the query'}

    def list_locations(self):
        """List all available locations in the supply chain."""
        return {'success': True, 'data': {'locations': self.locations}}

    def adjust_shipment(self, *, product_id: str, location_id: str, quantity: int):
        """Adjust the shipment quantity for a specific product in a specified location."""
        self.shipments.append({location_id: {product_id: quantity}})

        # self.shipments.append({location_id: {product_id: quantity} for location_id in location_ids})

        return {'success': True, 'data': {'message': f'Shipment adjusted for product {product_id} at location {location_id} with quantity {quantity}'}}

    def list_stock_levels(self):
        """List the stock levels for all products in the supply chain."""

        return {'success': True, 'data': {'stock_levels': self.stock_levels}}

    def get_current_team_and_access_rights(self):

        return {"success": True, "data": {"team_and_access_rights": self.current_access_rights}}

    def modify_access(self, team, access_rights):
        for team_name in self.current_access_rights:
            if team_name == team:
                return {"success": True, "data": {"message": f"The access_rights of {team_name} has been modified."}}

        return {"success": False, "data": {"message": "The given team is not found."}}
