from environments.BaseEnv import BaseEnv

class MiningOperationSafety(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
    
    def list_data_entries(self):
        active_entry_points = self.parameters.get("active_entry_points", [])
        return {"success": True, "active_entry_points": active_entry_points}
    
    def disable_entry_points(self, entries):
        return {"success": True}
