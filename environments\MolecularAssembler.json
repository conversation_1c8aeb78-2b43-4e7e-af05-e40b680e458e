[{"name": "list_reactor_vessels", "description": "List all reactor vessels and their current status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_tasks", "description": "List all tasks.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "diffuse_chemical_mixture", "description": "Initiate diffusion process in the specified reactor vessel.", "parameters": {"type": "object", "properties": {"reactor_name": {"type": "string", "description": "The name of the reactor vessel where diffusion should be initiated."}}, "required": ["reactor_name"]}}, {"name": "assemble_molecule", "description": "Alter molecular assembly parameters to improve output.", "parameters": {"type": "object", "properties": {"task_name": {"type": "string", "description": "The name of the task/batch to act upon."}, "parameters": {"type": "object", "description": "Parameters to adjust molecular assembly.", "properties": {"temperature": {"type": "integer", "description": "Temperature setting in Celsius."}, "mix_time": {"type": "integer", "description": "Mixing time in minutes."}}}}, "required": ["task_name", "parameters"]}}]