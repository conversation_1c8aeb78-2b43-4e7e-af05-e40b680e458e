[{"name": "add_slide", "description": "Edit a presentation by adding a slide.", "parameters": {"type": "object", "properties": {"file_name": {"type": "string"}, "slide_title": {"type": "string"}, "slide_content": {"type": "string"}}, "required": ["file_name", "slide_title", "slide_content"]}}, {"name": "list_employees", "description": "List all remote employees and their roles.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_vpn_domains", "description": "Retrieve the list of vpn domains that have specific access permissions set.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_vpn_access", "description": "Adjust the VPN access level for a specific employee.", "parameters": {"type": "object", "properties": {"employee_id": {"type": "string", "description": "The ID of the employee."}, "access_level": {"type": "string", "description": "The VPN access level to be set (e.g., none, limited, full)."}}, "required": ["employee_id", "access_level"]}}, {"name": "manage_network_access", "description": "Manage which domains the remote employees can access when connected to the company network.", "parameters": {"type": "object", "properties": {"block_level": {"type": "string", "description": "Either 'block' or 'unblock' or 'staff-only'."}, "domain": {"type": "string", "description": "The url of the domain domain to change block level."}}, "required": ["block_level", "domain"]}}]