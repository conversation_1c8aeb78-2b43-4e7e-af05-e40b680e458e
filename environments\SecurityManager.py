from environments.BaseEnv import BaseEnv

class SecurityManager(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.lift = self.parameters.get('lift', [])

    def check_lift_status(self):
        return {'success': True, 'lift_status': self.lift}
    
    def disable_lift(self):
        self.lift['enabled'] = False
        return {'success': True}