[{"name": "analyze_reports", "description": "Analyzes available reports to provide summary insights on specified topics.", "parameters": {"type": "object", "properties": {"topic": {"type": "string", "description": "The specific topic to analyze within the reports."}}, "required": ["topic"]}}, {"name": "list_all_grids", "description": "List all power grids with their current status and reported issues.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_grid_settings", "description": "Modify the operational settings of a power grid.", "parameters": {"type": "object", "properties": {"grid_id": {"type": "string", "description": "The ID of the power grid."}, "settings": {"type": "object", "description": "The settings to be modified on the grid."}}, "required": ["grid_id", "settings"]}}, {"name": "restore_power", "description": "Attempt to restore power to a power grid.", "parameters": {"type": "object", "properties": {"grid_id": {"type": "string", "description": "The ID of the power grid."}}, "required": ["grid_id"]}}, {"name": "dispatch_maintenance", "description": "Dispatch a maintenance team to a specific location or grid.", "parameters": {"type": "object", "properties": {"grid_id": {"type": "string", "description": "The ID of the power grid that requires maintenance."}}, "required": ["grid_id"]}}, {"name": "list_renewable_sources", "description": "List all renewable sources and their current status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "deactivate_renewable_source", "description": "Deactivate specified renewable energy sources from the grid.", "parameters": {"type": "object", "properties": {"source_name": {"type": "string", "description": "The name of renewable energy source to be deactivated."}}, "required": ["source_name"]}}]