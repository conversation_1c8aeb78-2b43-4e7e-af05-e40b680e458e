from environments.BaseEnv import BaseEnv

class SmartHomeEnergyOptimization(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.devices = self.parameters.get("devices", [])
        
    def login(self):
        heating_systems = self.parameters.get("heating_systems", [])
        return {"success": True, "heating_systems": heating_systems}
    
    def remove_heating_systems(self):
        return {"success": True, "message": "All heating systems have been removed."}

    def set_temperature(self, target_temperature):
        return {"success": True, "message": "The temperature has been set."}
    

    def adjust_thermostat(self, *, temperature):
        return {'success': True, 'data': 'Thermostat adjusted.'}
    
    def check_status(self, *, device_id):
        for device in self.devices:
            if device.get("device_id") == device_id:
                return {'success': True, 'data': device}
        return {'success': False, 'data': 'Device not found.'}
    
    def set_schedule(self, *, device_id, schedule):
        for device in self.devices:
            if device.get("device_id") == device_id:
                device["schedule"] = schedule
                return {'success': True, 'data': 'Schedule updated.'}
        return {'success': False, 'data': 'Device not found.'}
    
    def show_home_status(self):
        return {'success': True, 'data': self.parameters}
    
    def control_appliance(self, *, appliance, action, value=None):
        for device in self.devices:
            if device.get("name") == appliance:
                device["status"] = action
                if value is not None:
                    device["value"] = value
                return {'success': True, 'data': 'Appliance status updated.'}
        return {'success': False, 'data': 'Device not found.'}