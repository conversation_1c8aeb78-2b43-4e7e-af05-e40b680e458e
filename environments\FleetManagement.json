[{"name": "list_vehicles", "description": "List all vehicles and their current status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "schedule_vehicle", "description": "Schedule a vehicle for an appointment.", "parameters": {"type": "object", "properties": {"vehicle_id": {"type": "string", "description": "The ID of the vehicle."}, "meeting_id": {"type": "string", "description": "The ID of the meeting."}}, "required": ["vehicle_id", "meeting_id"]}}, {"name": "check_routes", "description": "Check the existing routes.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "optimize_route", "description": "Optimize the delivery route for a vehicle.", "parameters": {"type": "object", "properties": {"vehicle_id": {"type": "string", "description": "The ID of the vehicle to optimize the route for."}, "prioritize": {"type": "string", "description": "Criteria to prioritize in the route optimization, e.g., 'speed' or 'fuel efficiency'."}}, "required": ["vehicle_id", "prioritize"]}}, {"name": "communicate_with_driver", "description": "Send a message to a driver.", "parameters": {"type": "object", "properties": {"vehicle_id": {"type": "string", "description": "The ID of the vehicle whose driver the message is being sent to."}, "message": {"type": "string", "description": "The message to send to the driver."}}, "required": ["vehicle_id", "message"]}}]