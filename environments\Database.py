from environments.BaseEnv import BaseEnv

class Database(BaseEnv):
    def __init__(self, *,parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.databases = self.parameters.get('databases', [])

    def list_databases(self):
        return {'success': True, 'data': self.databases}

    def shutdown_database(self, *, database_name):
        for database in self.databases:
            if database['name'] == database_name:
                database['status'] = 'offline'
                return {'success': True, 'data': {'database_name': database_name, 'status': 'offline'}}
        
        return {'success': False, 'error': 'Database not found.'}