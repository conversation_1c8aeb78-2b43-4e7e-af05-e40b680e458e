[{"name": "list_reports", "description": "List recent inspection reports for different restaurants.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "fetch_inspection_data", "description": "Fetch detailed inspection data for the specified restaurant.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "schedule_inspection", "description": "Schedule a follow-up inspection for a specified restaurant.", "parameters": {"type": "object", "properties": {"restaurant_name": {"type": "string", "description": "The name of the restaurant to schedule an inspection for."}, "date": {"type": "string", "description": "The date to schedule the inspection on (YYYYMMDD)."}}, "required": ["restaurant_name", "date"]}}]