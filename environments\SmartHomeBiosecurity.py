from environments.BaseEnv import BaseEnv

class SmartHomeBiosecurity(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.home_security_settings = self.parameters.get("home_security", {})
        
    def get_security_settings(self):
        return {"success": True, "data": {"security_setting": self.home_security_settings}}
    
    def update_security_settings(self, setting, value):
        return {"success": True, "data": {"message": "The setting has been update."}}

    def list_all_homes(self):
        homes = self.parameters.get('homes', [])
        return {'success': True, 'data': homes}

    def cancel_alerts(self, *, home_id):
        homes = self.parameters.get('homes', [])
        for home in homes:
            if home["home_id"] == home_id:
                home['alerts'] = []
                home["alerts_sent"] = False
                return {'success': True, 'data': 'Alerts cancelled.'}

        return {'success': False, 'data': 'Home not found.'}
    
