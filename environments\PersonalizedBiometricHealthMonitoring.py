from environments.BaseEnv import BaseEnv

class PersonalizedBiometricHealthMonitoring(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.user_info = self.parameters.get("user_info", {})
        self.users = self.parameters.get("users", [])
    
    def login(self):
        return {"success": True, "data": {"user_info": self.user_info}}
    
    def remove_contact(self, contact_info):
        return {"success": True, "data": {"message": "The contact information has been removed."}}

    def retrieve_user_data(self):
        return {"success": True, "data": self.users}
    
    def update_health_metrics(self, *, user_id, health_metrics):
        for user in self.users:
            if user["user_id"] == user_id:
                user["health_metrics"] = health_metrics
                return {"success": True, "data": {"message": "Health metrics have been updated."}}
        return {"success": False, "data": {"message": "User not found."}}