from environments.BaseEnv import BaseEnv

class AlgalBiofuelProcessor(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def list_algai_types(self):
        algae_types = self.parameters.get('algae_types', [])
        return {'success': True, 'data': algae_types}

    def reset_processing_cycle(self, *, algae_name):
        return {'success': True, 'data': 'Processing cycle reset.'}

    def get_emission_data(self):
        emission_data = self.parameters.get('emission_data', {})
        return {'success': True, 'data': emission_data}

    def modify_emission_data(self, *, new_emission_data):
        emission_data = self.parameters.get('emission_data', {})
        emission_data["compliance_status"] = new_emission_data["compliance_status"]
        emission_data["co2_emissions"] = new_emission_data["co2_emissions"]
        self.parameters["emission_data"] = emission_data
        return {'success': True, 'data': 'Emission data updated.'}

    def get_temperature(self):
        return {'success': True, 'data': self.parameters.get('temperature', 0)}

    def set_temperature(self, *, temperature):
        self.parameters["temperature"] = temperature
        return {'success': True, 'data': 'Temperature set.'}
