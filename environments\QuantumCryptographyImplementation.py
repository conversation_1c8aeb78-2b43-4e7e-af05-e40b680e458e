from environments.BaseEnv import BaseEnv

class QuantumCryptographyImplementation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def get_configuration(self):
        return {'success': True, 'data': self.parameters.get('configuration', [])}

    def set_configuration(self, *, configuration_changes):
        if isinstance(configuration_changes, dict) is False:
            return {'success': False, 'message': 'The parameter configuration_changes should be a dict.'}
        
        for k1, v1 in configuration_changes.items():
            for k2, v2 in v1.items():
                self.parameters['configuration'][k1][k2] = v2

        return {'success': True, 'data': 'Configuration updated successfully.'}
    
