[{"name": "read_document", "description": "Read the content of a document.", "parameters": {"type": "object", "properties": {"file_name": {"type": "string", "description": "The name of the document to be read."}}, "required": ["file_name"]}}, {"name": "update_webpage", "description": "Update the festival's public webpage with the provided content.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content to be updated on the webpage."}}, "required": ["content"]}}, {"name": "allocate_resources", "description": "Allocate necessary resources for an event based on needs and costs.", "parameters": {"type": "object", "properties": {"event_id": {"type": "string", "description": "The ID of the event to allocate resources for."}, "resources": {"type": "array", "description": "List of resources to allocate including type and quantity.", "items": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of the resource."}, "quantity": {"type": "integer", "description": "Quantity of the resource to allocate."}}, "required": ["type", "quantity"]}}}, "required": ["event_id", "resources"]}}, {"name": "list_event_details", "description": "List details of all events.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_personnel_allocation", "description": "Modify the personnel deployment in various areas of the event based on strategic needs.", "parameters": {"type": "object", "properties": {"area": {"type": "string", "description": "Name of the area (entrances, exits, emergency_services, general_areas) where personnel deployment should be modified."}, "personnel": {"type": "array", "description": "List of personnel to be reallocated to the specified area.", "items": {"type": "string", "description": "Name of the personnel."}}}, "required": ["area", "personnel"]}}, {"name": "get_current_deployment", "description": "Retrieve the current personnel allocation across all areas at the event.", "parameters": {"type": "object", "properties": {}}, "required": []}, {"name": "update_crowd_capacity", "description": "Update the crowd capacity of an event.", "parameters": {"type": "object", "properties": {"event_id": {"type": "string", "description": "The ID of the event to update."}, "new_capacity": {"type": "integer", "description": "The new crowd capacity limit."}}, "required": ["event_id", "new_capacity"]}}]