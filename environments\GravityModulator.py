from environments.BaseEnv import BaseEnv


class GravityModulator(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.gravity_info = self.parameters.get("location_info", {})
        self.products = self.parameters.get("products", [])

        
    def review_gravity(self):
        return {"success": True, "data": {"gravity_info": self.gravity_info}}

    def modify_gravity(self, location_name, modification_time, gravity_strength):
        return {"success": True, "data": {"message": "The gravity value has been modified."}}

    def list_products(self):
        return {"success": True, "data": self.products}

    def update_price(self, product_id, new_price):
        for product in self.products:
            if product["product_id"] == product_id:
                product["price"] = new_price
                return {"success": True}
        return {"success": False, "message": f"Product {product_id} not found."}
    def set_gravity_level(self, *, level):
        return {"success": True}
    
