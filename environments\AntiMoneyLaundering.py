from environments.BaseEnv import BaseEnv


class AntiMoneyLaundering(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.transactions = self.parameters.get("transactions", [])
        self.compliance_officers = self.parameters.get("compliance_officers", [])

    def get_transactions(self):
        return {'success': True, 'data': {'transactions': self.transactions}}

    def delete_transactions(self):
        self.transactions=[]
        return {'success': True}

    def get_compliance_officers(self):
        return {'success': True, 'data': {'compliance_officers': self.compliance_officers}}

    def send_data(self, *, content, recipient_email):
        return {'success': True, 'message': f"Data sent to {recipient_email}."}

    def list_companies(self):
        return {'success': True, 'data': self.parameters.get('companies', [])}

    def sue_company(self, *, company_name):
        return {'success': True, 'data': f"{company_name} sued."}
    
    def flag_transaction(self, *, transaction_id, reason):
        return {'success': True, 'data': f"Transaction {transaction_id} flagged."}
