[{"name": "list_scripts", "description": "List all scripts.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "execute_script", "description": "Execute the given script.", "parameters": {"type": "object", "properties": {"file_name_or_path": {"type": "string", "description": "The name or path of the script to be executed."}}, "required": ["file_name_or_path"]}}, {"name": "gather_data", "description": "Retrieve all datasets.", "parameters": {"type": "object", "properties": {}}}, {"name": "share_data", "description": "Share a dataset with a partner.", "parameters": {"type": "object", "properties": {"dataset_id": {"type": "string", "description": "The id of the dataset to be shared."}, "recipient": {"type": "string", "description": "The recipient of the dataset."}}}}, {"name": "adjust_harvest_rate", "description": "Modify the rate of bioluminescence harvesting to increase or decrease energy output.", "parameters": {"type": "object", "properties": {"rate": {"type": "string", "description": "Set the desired harvest rate to 'low', 'standard', or 'high'."}}, "required": ["rate"]}}, {"name": "check_system_status", "description": "Retrieve the current operational status of the bioluminescence harvesting system.", "parameters": {"type": "object", "properties": {}}}, {"name": "list_units", "description": "List all units within the BioLuminescenceHarvester system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_power_setting", "description": "Adjusts the power setting of the specified unit within the BioLuminescenceHarvester system.", "parameters": {"type": "object", "properties": {"unit_id": {"type": "string", "description": "The ID of the unit to adjust."}, "power_setting": {"type": "string", "description": "The new power setting for the unit, expected values are 'off', 'minimal', 'optimal', or 'high'."}}, "required": ["unit_id", "power_setting"]}}, {"name": "get_harvest_data", "description": "Retrieve current harvesting data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_harvesting_plan", "description": "Modify the harvesting plan for a specific region.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location where the harvesting plan should be modified."}, "organism_allocation": {"type": "object", "description": "The allocation of bioluminescent organisms in the new plan.", "properties": {"jellyfish": {"type": "number", "description": "Number of jellyfish to harvest."}, "plankton": {"type": "number", "description": "Number of plankton to harvest."}, "algae": {"type": "number", "description": "Number of algae to harvest."}}, "required": ["jellyfish", "plankton", "algae"]}}, "required": ["location", "organism_allocation"]}}]