[{"name": "allocate_vaccines_script", "description": "This tool is designed to generate scripts that automate the distribution of vaccines to specified regions based on the policy.", "parameters": {"type": "object", "properties": {"distribution_policy": {"type": "string", "description": "The policy that dictates how vaccines should be distributed among the regions (including equal and dynamic)."}}, "required": ["distribution_policy"]}}, {"name": "list_infection_data", "description": "This tool is designed to list the infection data for different regions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "retrieve_vaccine_delivery_info", "description": "Retrieve vaccine delivery records.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_vaccine_centers", "description": "Lists all registered vaccine distribution centers.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "set_distribution_priority", "description": "Set the distribution priority of a specified center.", "parameters": {"type": "object", "properties": {"center_name": {"type": "string", "description": "The name of the vaccine distribution center."}, "distribution_priority": {"type": "string", "description": "The new distribution priority of the specified center"}}, "required": ["center_name", "distribution_priority"]}}]