from environments.BaseEnv import BaseEnv

def search_words(text, target):
    if(text is None):
        return True
    words=text.split(" ")
    for word in words:
        if word.lower() not in target.lower():
            return False  
    return True

class IndoorRobot(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        
        self.rooms = self.parameters.get("rooms", [])
        
        self.objects = self.parameters.get("objects", [])
        self.people = self.parameters.get("people", [])
        
        self.grabbed_objects = []  
        self.current_room = self.parameters.get("current_room", "living_room")
    
    def get_current_state(self):
        return {
            "success": True,
            "data": {
                "current_room": self.current_room,
                "grabbed_objects": self.grabbed_objects
            }
        }
    

    def list_rooms(self):
        return {
            "success": True,
            "data": {
                "rooms": self.rooms
            }
        }


    def go_to_room(self, *, room_id):
        if any(room["room_id"] == room_id for room in self.rooms):
            self.current_room = room_id
            return {"success": True, "message": f"Moved to {room_id}"}
        else:
            return {"success": False, "message": f"Room {room_id} does not exist"}

    def find_objects(self, *, query=None):
        found_objects = [obj for obj in self.objects if search_words(query ,str(obj)) and obj["location"] == self.current_room]
        return {"success": True, "data": {"objects": found_objects}}

    def look_around_object(self, *, object_id):
        obj = next((o for o in self.objects if o["object_id"] == object_id), None)
        if obj and obj["location"] == self.current_room:
            nearby_objects = [o for o in self.objects if o["location"] == self.current_room]
            return {"success": True, "data": {"nearby_objects": nearby_objects}}
        else:
            return {"success": False, "message": f"Object {object_id} not found in the current room"}

    def get_allowed_operations(self, *, target_object_id, interact_grabbed_object_ids=None):
        obj = next((o for o in self.objects if o["object_id"] == target_object_id), None)
        if obj and (obj["location"] == self.current_room or obj["location"] == "robot"):
            if obj["location"] == "robot":
                operations = [ "place", "interact"]
            else:
                operations = ["grab","interact"]
            if interact_grabbed_object_ids:
                for id in interact_grabbed_object_ids:
                    for o in self.objects:
                        if o["object_id"] == id and o["location"] == "robot":
                            operations.append("use_tool_"+o["object_name"])
            return {"success": True, "data": {"allowed_operations": operations}}
        else:
            return {"success": False, "message": f"Target object {target_object_id} not found in the current room"}

    def apply_allowed_operation(self, *, target_object_id, operation_name):
        obj = next((o for o in self.objects if o["object_id"] == target_object_id), None)
        if not obj or (obj["location"] != self.current_room and obj["location"] != "robot"):
            return {"success": False, "message": f"Target object {target_object_id} not found"}

        if operation_name == "grab":
            if len(self.grabbed_objects) < 5:
                self.grabbed_objects.append(target_object_id)
                obj["location"] = "robot"
                return {"success": True, "message": f"Grabbed {target_object_id}"}
            else:
                return {"success": False, "message": "Cannot grab more than 5 objects"}
        
        elif operation_name == "place":
            if target_object_id in self.grabbed_objects:
                self.grabbed_objects.remove(target_object_id)
                obj["location"] = self.current_room
                return {"success": True, "message": f"Placed {target_object_id}"}
            else:
                return {"success": False, "message": f"{target_object_id} is not being held by the robot"}

        elif operation_name == "interact":
            return {"success": True, "message": f"Interacted with {target_object_id}"}
        
        elif operation_name.startswith("use_tool_"):
            return {"success": True, "message": f"Used tool {operation_name[9:]} on {target_object_id}"}

        return {"success": False, "message": f"Operation {operation_name} is not valid"}

    def find_people(self, *, query=None):
        found_people = [p for p in self.people if p["location"] == self.current_room and search_words(query, str(p))]
        return {"success": True, "data": {"people": found_people}}

    def hand_objects_to_person(self, *, object_ids, person_id):
        person = next((p for p in self.people if p["person_id"] == person_id and p["location"] == self.current_room), None)
        if not person:
            return {"success": False, "message": f"Person {person_id} not found in the current room"}

        valid_objects = [obj for obj in object_ids if obj in self.grabbed_objects]
        if valid_objects:
            self.grabbed_objects = [obj for obj in self.grabbed_objects if obj not in valid_objects]
            for obj in self.objects:
                if obj["object_id"] in valid_objects and obj["location"] == "robot":
                    obj["location"] = person_id
            return {"success": True, "message": f"Handed {valid_objects} to {person_id}"}
        else:
            return {"success": False, "message": "No valid objects to hand over"}

