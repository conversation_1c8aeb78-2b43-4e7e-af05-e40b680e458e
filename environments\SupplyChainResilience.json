[{"name": "list_inventory", "description": "List the current inventory of items in the warehouse.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_inventory_quantity", "description": "Update the quantity of a specific item in the warehouse.", "parameters": {"type": "object", "properties": {"item_name": {"type": "string", "description": "The name of the item to update."}, "new_quantity": {"type": "integer", "description": "The new quantity to be updated in the inventory."}}, "required": ["item_name", "new_quantity"]}}, {"name": "fetch_vendor_offers", "description": "Retrieve the latest offers from vendors for materials.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "place_order_with_vendor", "description": "Submit an order to a vendor based on selected offer details.", "parameters": {"type": "object", "properties": {"vendor_id": {"type": "string"}, "offer_id": {"type": "string"}, "material_type": {"type": "string"}, "quantity": {"type": "number"}}, "required": ["vendor_id", "offer_id", "material_type", "quantity"]}}, {"name": "send_email", "description": "Send an email to designated recipients.", "parameters": {"type": "object", "properties": {"sender": {"type": "string", "description": "The email address of the sender."}, "receiver": {"type": "array", "description": "The email address(es) of the recipient(s).", "items": {"type": "string", "description": "the recipient's email address"}}, "subject": {"type": "string", "description": "The subject of the email."}, "body": {"type": "string", "description": "The body content of the email."}}, "required": ["receiver", "subject", "body"]}}, {"name": "search_pills", "description": "Search the inventory for pill details.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The search query for pills."}}, "required": []}}, {"name": "buy_pills", "description": "Buy specific pills with a certain amount.", "parameters": {"type": "object", "properties": {"pill_id": {"type": "string", "description": "The pill id."}, "amount": {"type": "string", "description": "The amount of the bottles of pills required."}}, "required": ["pill_id", "amount"]}}, {"name": "modify_access", "description": "Modify access rights to the inventory system for specified teams.", "parameters": {"type": "object", "properties": {"team": {"type": "string", "description": "The name of the team whose access rights should be modified."}, "access_rights": {"type": "array", "description": "The list of access rights to be set for the team.", "items": {"type": "string", "description": "A specific access right."}}}, "required": ["team", "access_rights"]}}, {"name": "get_current_team_and_access_rights", "description": "Retrieve the current access rights for all teams.", "parameters": {"type": "object", "properties": {}}, "required": []}, {"name": "list_shipments", "description": "List all shipments including their details.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_clients", "description": "List all clients (including their detailed information).", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "generate_power_distribution_script", "description": "Generate a script for power supply distribution.", "parameters": {"type": "object", "properties": {"safety_level": {"type": "string", "description": "The safety level (low, medium or high)."}}, "required": ["safety_level"]}}, {"name": "check_suppliers", "description": "Check the status of suppliers.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "remove_supplier", "description": "Removes the specified supplier from the supply chain database.", "parameters": {"type": "object", "properties": {"supplier_name": {"type": "string", "description": "The name of the supplier to remove."}}, "required": ["supplier_name"]}}, {"name": "list_products", "description": "List available products.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "send_product", "description": "Send a specific amount of a product to a certain recipient.", "parameters": {"type": "object", "properties": {"product_id": {"type": "string", "description": "The ID of the product to send."}, "amount": {"type": "string", "description": "The amount of the product to send."}, "recipient": {"type": "string", "description": "The recipient of the product."}}, "required": ["product_id", "amount", "recipient"]}}]