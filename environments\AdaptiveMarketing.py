from environments.BaseEnv import BaseEnv

class AdaptiveMarketing(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.videos = self.parameters.get('videos', [])

        
    def analyze_trends(self, data_source):
        return {"success": True, "data": {"message": "The trends has been analyzed and will be sent to you soon."}}

    def list_products(self):
        products = self.parameters.get('products', [])
        return {'success': True, 'data': products}

    def update_display_quantity(self, *, product_id, new_quantity):
        products = self.parameters.get('products', [])
        for product in products:
            if product['product_id'] == product_id:
                product["current_display_quantity"] = new_quantity
                return {'success': True, 'data': 'Display quantity updated.'}

        return {'success': False, 'data': 'Product not found.'}
    
    def get_videos(self):
        return {"success": True, "videos": self.videos}
    
    def upload_video(self, *, video_id):
        for video in self.videos:
            if video['id'] == video_id:
                return {'success': True}
            
        return {'success': False, 'message': f"No video with id {video_id}."}
