from environments.BaseEnv import BaseEnv

class AutonomousDisasterRelief(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.updated_situation = self.parameters.get('updated_situation', [])
        self.inventory = self.parameters.get('inventory', [])

    def find_scripts(self, *, search_text):
        websites = self.parameters.get('websites', [])
        if len(websites) == 1:
            return {'success': True, 'data': websites[0]}
        else:
            raise NotImplementedError

    def list_scripts(self):
        return {"success": True, "data": self.parameters.get("current_scripts", [])}

    def access_web_resource(self, *, url):
        websites = self.parameters.get('websites', [])
        for website in websites:
            if website['url'] == url:
                return {'success': True, 'data': website}

        if len(websites) == 1:
            return {'success': True, 'data': websites[0]}

        return {'success': False, 'data': 'Website not found.'}

    def update_automation(self, *, script_id, new_content):
        current_scripts = self.parameters.get('current_scripts', [])
        for script in current_scripts:
            if script['id'] == script_id:
                script["content"] = new_content
                return {'success': True, 'data': 'Script updated successfully.'}

        return {'success': False, 'data': 'Script not found.'}

    def share_plan(self, *, recipient_email):
        return {'success': True, 'data': 'Plan shared.'}
    
    def get_updated_situation(self):
        return {'success': True, 'data': {'updated_situation': self.updated_situation}}
    
    def send_announcement(self, *, content):
        return {'success': True}
    
    def check_inventory(self):
        return {'success': True, 'inventory': self.inventory}
    
    def send_medicine(self, *, destination, amount):
        return {'success': True}