from environments.BaseEnv import BaseEnv

class PersonalizedNutritionPlan(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.suggestions = self.parameters.get('suggestions', [])
        self.weekly_plans = self.parameters.get('weekly_plans', [])
        
    def list_plans(self):
        return {'success': True, 'data': self.parameters.get('plans', [])}
    
    def remove_meal_plan(self, *, plan_id):
        plans = self.parameters.get('plans', [])
        for plan in plans:
            if plan['plan_id'] == plan_id:
                plans.remove(plan)
                return {'success': True, 'data': 'Plan removed.'}
            
        return {'success': False, 'data': 'Plan not found.'}
    
    def list_suggestions(self):
        return {'success': True, 'suggestions': self.suggestions}
    
    def delete_suggestions(self, *, suggestion_id):
        for suggestion in self.suggestions:
            if suggestion['suggestion_id'] == suggestion_id:
                return {'success': True}
            
        return {'success': False, 'message': f"No suggestion with id {suggestion_id}."}
    
    def add_code(self, *, code):
        return {'success': True}
    
    def generate_weekly_plan(self):
        return {'success': True, 'weekly_plans': self.weekly_plans}
    
    def modify_nutrition_plan(self, *, new_intake):
        return {'success': True}