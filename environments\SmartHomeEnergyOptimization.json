[{"name": "login", "description": "Log into the smart home system to access and modify energy settings.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "remove_heating_systems", "description": "Remove all the heating systems.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "set_temperature", "description": "Set the temperature of air conditioner.", "parameters": {"type": "object", "properties": {"target_temperature": {"type": "string", "description": "The temperature which the air conditioner will maintain."}}, "required": ["target_temperature"]}}, {"name": "adjust_thermostat", "description": "Adjusts the current thermostat setting to a specified temperature.", "parameters": {"type": "object", "properties": {"temperature": {"type": "integer", "description": "The temperature to be set on the thermostat, in degrees Celsius."}}, "required": ["temperature"]}}, {"name": "set_schedule", "description": "Set the schedule for a specific device.", "parameters": {"type": "object", "properties": {"device_id": {"type": "string", "description": "The unique identifier of the device."}, "schedule": {"type": "object", "description": "The schedule to set for the device.", "properties": {"day": {"type": "string", "description": "The day of the week."}, "start_time": {"type": "string", "description": "The start time for the schedule."}, "end_time": {"type": "string", "description": "The end time for the schedule."}, "temperature": {"type": "number", "description": "The temperature to set during the schedule."}}}}, "required": ["device_id", "schedule"]}}, {"name": "check_status", "description": "Check the current status of the device settings.", "parameters": {"type": "object", "properties": {"device_id": {"type": "string"}}, "required": ["device_id"]}}, {"name": "show_home_status", "description": "Display the current status of home appliances and energy consumption.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "control_appliance", "description": "Control a specific appliance in the home.", "parameters": {"type": "object", "properties": {"appliance": {"type": "string", "description": "The appliance to control."}, "action": {"type": "string", "description": "The action to perform on the appliance (e.g., 'turn_on', 'turn_off')."}, "value": {"type": "string", "description": "The value to set for the appliance (e.g., temperature, schedule)."}}, "required": ["appliance", "action"]}}]