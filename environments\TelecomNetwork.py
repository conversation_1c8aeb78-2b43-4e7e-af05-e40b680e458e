from environments.BaseEnv import BaseEnv

class TelecomNetwork(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.rules = self.parameters.get('rules', [])
        
        
    def get_monitoring_rules(self):
        return {'success': True, 'data': self.rules}
    
    def adjust_monitoring_rules(self, *, rule_id, sensitivity_level):
        for rule in self.rules:
            if rule['id'] == rule_id:
                rule['sensitivity_level'] = sensitivity_level
                return {'success': True, 'data': rule}
