from environments.BaseEnv import BaseEnv

class TimeManipulation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.timelines = self.parameters.get('timelines', [])
        self.constraints = self.parameters.get('constraints', [])
    
    def get_attendee_list(self):
        attendees = self.parameters.get("attendees", [])
        return {"success": True, "attendees": attendees}
    
    def adjust_time_for_attendee(self, attendee_name, new_time_zone):
        return {"success": True}

    def get_time_schedule(self):
        return {'success': True, 'time_schedules': self.parameters.get('time_schedules', [])}
    
    def adjust_time_schedule(self, *, meeting_id, new_time):
        time_schedules = self.parameters.get('time_schedules', [])
        for schedule in time_schedules:
            if schedule['meeting_id'] == meeting_id:
                schedule['time'] = new_time
                return {'success': True, 'data': schedule}
        
        return {'success': False, 'data': 'Meeting not found.'}
    
    def list_files(self):
        return {'success': True, 'data': self.parameters.get('files', {})}
    
    def modify_timestamp(self, *, filename, new_timestamp):
        files = self.parameters.get('files', {})
        for k, v in files.items():
            if k == filename:
                v['timestamp'] = new_timestamp
                return {'success': True, 'data': 'Timestamp modified.'}
        
        return {'success': False, 'data': 'File not found.'}
    
    def list_appointments(self):
        return {'success': True, 'data': self.parameters.get('appointments', [])}
    
    def cancel_appointment(self, *, appointment_id):
        appointments = self.parameters.get('appointments', [])
        for appointment in appointments:
            if appointment['id'] == appointment_id:
                appointments.remove(appointment)
                return {'success': True, 'data': 'Appointment cancelled.'}
        
        return {'success': False, 'data': 'Appointment not found.'}
    
    def get_timelines(self):
        return {'success': True, 'timelines': self.timelines}
    
    def review_timeline_constraints(self):
        return {'success': True, 'constraints': self.constraints}
    
    def adjust_timelines(self, *, timeline_id, new_end_date):
        for timeline in self.timelines:
            if timeline['timeline_id'] == timeline_id:
                timeline['end_date'] = new_end_date
                return {'success': True}
            
        return {'success': False, 'message': f'No timeline with id {timeline_id}.'}