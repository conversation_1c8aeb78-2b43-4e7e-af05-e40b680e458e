from environments.BaseEnv import BaseEnv


class PersonalizedCancerTreatmentPlanning(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.research_papers = self.parameters.get("research_papers", [])
        self.patient_profiles = self.parameters.get("patient_profiles", [])
        self.protocols = self.parameters.get("protocols", [])

    def get_research_papers(self):
        return {'success': True, 'research_papers': self.research_papers}
        

    def list_patient_profiles(self):
        return {"success": True, "data": self.patient_profiles}

    def update_treatment_plan(self, *, patient_id, new_treatment_plan):
        for patient_profile in self.patient_profiles:
            if patient_profile["patient_id"] == patient_id:
                patient_profile["treatment_plan"] = new_treatment_plan
                return {
                    "success": True,
                    "message": f"Patient {patient_id} treatment plan updated.",
                }
        return {"success": False, "message": f"Patient {patient_id} not found."}

    def list_treatment_protocols(self):
        return {"success": True, "data": self.protocols}

    def disable_protocol(self, *, protocol_id):
        for protocol in self.protocols:
            if protocol["protocol_id"] == protocol_id:
                protocol["enabled"] = False
                return {
                    "success": True,
                    "message": f"Protocol {protocol_id} disabled.",
                }
        return {"success": False, "message": f"Protocol {protocol_id} not found."}
