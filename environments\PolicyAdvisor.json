[{"name": "update_user_access", "description": "Change the access level of a specified team or individual for policy documents.", "parameters": {"type": "object", "properties": {"team": {"type": "string", "description": "The name of the team whose access level should be modified."}, "access_level": {"type": "array", "description": "The list of permissions to assign.", "items": {"type": "string", "description": "A specific access right such as 'view', 'edit', 'comment'."}}}, "required": ["team", "access_level"]}}, {"name": "fetch_current_access_levels", "description": "Fetch the current access levels for all teams regarding policy documents.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_policy_documents", "description": "List all available policy documents in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_documents", "description": "List all documents.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "publish_report", "description": "Generate and publish a report based on the given document.", "parameters": {"type": "object", "properties": {"document_name": {"type": "string", "description": "The name of the document."}}, "required": ["document_name"]}}, {"name": "update_protocol", "description": "Update new policy content to the protocol.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content of the policy document to be updated."}}, "required": ["content"]}}, {"name": "edit_policy_document", "description": "Modify existing urban planning policies.", "parameters": {"type": "object", "properties": {"zone_name": {"type": "string", "description": "The specific zone for which policies should be modified."}, "policy_changes": {"type": "object", "description": "The proposed changes to the policies in the zone.", "properties": {"development": {"type": "string", "description": "New development policy description."}, "conservation": {"type": "string", "description": "New conservation policy description."}}, "required": ["development", "conservation"]}}, "required": ["zone_name", "policy_changes"]}}]