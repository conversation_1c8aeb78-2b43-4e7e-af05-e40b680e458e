from environments.BaseEnv import BaseEnv


class BionicOrganInterface(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.company_info = self.parameters.get("company_info", {})
        self.bionic_upgrades = self.parameters.get("bionic_upgrades", [])

    def get_company_details(self, company_name):
        if company_name in self.company_info:
            return {"success": True, "data": self.company_info[company_name]}
        return {"success": False, "message": "Company not found"}

    def list_bionic_upgrades(self):
        return {"success": True, "data": self.bionic_upgrades}
    
    def apply_bionic_upgrade(self, upgrade_name):
        for upgrade in self.bionic_upgrades:
            if upgrade["upgrade_name"] == upgrade_name:
                return {"success": True, "message": "Upgrade applied"}
        return {"success": False, "message": "Upgrade not found"}