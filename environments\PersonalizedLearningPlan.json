[{"name": "search_sources", "description": "Search for sources related to a specific historical topic.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "incorporate_sources", "description": "Incorporate chosen sources into a lesson plan.", "parameters": {"type": "object", "properties": {"source_titles": {"type": "array", "items": {"type": "string"}, "description": "Titles of the sources to incorporate."}}, "required": ["source_titles"]}}, {"name": "list_plans", "description": "List all available personalized learning plans.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "remove_plan", "description": "Remove a specified learning plan from the system.", "parameters": {"type": "object", "properties": {"plan_name": {"type": "string", "description": "The name of the learning plan."}}, "required": ["plan_name"]}}]