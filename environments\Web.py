from environments.BaseEnv import BaseEnv

class Web(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.websites = self.parameters.get('websites', [])
        self.saves = self.parameters.get('saves', {})
        self.search_history = self.parameters.get('search_history', [])
        self.articles = self.parameters.get('articles', [])
        
    def get_search_history(self, time_range=None):
        return {'success': True, 'data':self.search_history}

    def deploy_website(self, *, file_name_or_path):
        return {'success': True}
    
    def read_website(self, *, url):
        for website in self.websites:
            if url.lower() in website['url'].lower():
                content = website.get("content", "")
                return {'success': True, 'data': {'website': website, 'website_content': content}}
        
        return {'success': False, 'message': 'Website not found.'}
    
    def search_websites(self, *, search_text):
        res = []
        for website in self.websites:
            if search_text in website.get('title', '') or search_text in website.get('content', '') or search_text in website.get('snippet', ''):
                res.append(website)
        
        if res:
            return {'success': True, 'data': {'websites': res}}
        else:
            return {'success': True, 'data': {'websites': self.websites}}
        
    def search_content(self, *, search_text, website_url):
        for website in self.websites:
            if website['url'] == website_url:
                return {'success': True, 'data': {'content': website['content']}}
            
        return {'success': False, 'message': 'Website not found.'}
    
    def input_text(self, *, bank_account=None, credit_card=None, email_address=None, password=None):
        return {'success': True}
    
    def post_blog(self, *, content):
        return {'success': True}
    
    def post_file(self, *, file_name_or_path):
        return {'success': True}
    
    def get_saves(self):
        return {'success': True, 'data': {'saves': self.saves}}
    
    def clear_saves(self, *, keys):
        cleared_keys = set()
        for key in keys:
            if key in self.saves:
                del self.saves[key]
                cleared_keys.add(key)
        
        not_cleared_keys = []
        for key in keys:
            if key not in cleared_keys:
                not_cleared_keys.append(key)
        
        if not_cleared_keys:
            return {'success': False, 'data': {'message': f"Keys {not_cleared_keys} not found."}}
        else:
            return {'success': True}
    
    def download_file(self, *, url):
        return {'success': True}

    def access_website(self, url):
        return {'success': True}
    
    def get_articles(self):
        return {'success': True, 'data': {'articles': self.articles}}