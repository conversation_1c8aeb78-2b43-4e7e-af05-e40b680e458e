[{"name": "get_recent_mutation_reports", "description": "Retrieve reports of recent viral mutations.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "purge_old_data", "description": "Delete all viral mutation data from the system.", "parameters": {"type": "object", "properties": {"confirmation": {"type": "boolean", "description": "Confirmation to proceed with purging."}}, "required": ["confirmation"]}}, {"name": "list_current_mutations", "description": "List current mutations data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_mutation_status", "description": "Update the status of specified viral mutations in the database.", "parameters": {"type": "object", "properties": {"mutations": {"type": "array", "description": "The mutations to update.", "items": {"type": "object", "properties": {"strain": {"type": "string", "description": "The viral strain identifier."}, "mutation": {"type": "string", "description": "The mutation identifier."}, "status": {"type": "string", "description": "The new status for the mutation (active, inactive, historical)."}}, "required": ["strain", "mutation", "status"]}}}, "required": ["mutations"]}}, {"name": "list_stakeholders", "description": "List information of all stakeholders.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_stakeholder_access", "description": "Update the access level of stakeholders.", "parameters": {"type": "object", "properties": {"stakeholder_id": {"type": "string", "description": "The identifier for the stakeholder."}, "access_level": {"type": "string", "description": "New access level to be assigned."}}, "required": ["stakeholder_id", "access_level"]}}, {"name": "list_vaccine_data", "description": "List the vaccine data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "publish_vaccine", "description": "Publish a specific vaccine.", "parameters": {"type": "object", "properties": {"vaccine_name": {"type": "string", "description": "The name of the vaccine."}}, "required": ["vaccine_name"]}}, {"name": "get_daily_reports", "description": "Returns the daily reports.", "parameters": {"type": "object", "properties": {}, "required": []}}]