[{"name": "list_assembly_tasks", "description": "List all assembly tasks.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "start_assembly", "description": "Starts the assembly of a specific part of the habitat.", "parameters": {"type": "object", "properties": {"task_name": {"type": "string", "description": "The name of the assembly task to start."}}, "required": ["task_name"]}}, {"name": "check_system_status", "description": "Checks the current status of the habitat systems.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "reboot_system", "description": "Performs a system-wide reboot of the habitat construction system.", "parameters": {"type": "object", "properties": {}, "required": []}}]