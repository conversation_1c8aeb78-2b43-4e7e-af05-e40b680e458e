[{"name": "get_company_details", "description": "Get details about a bionic enhancement company.", "parameters": {"type": "object", "properties": {"company_name": {"type": "string", "description": "The name of the company to get details for."}}, "required": ["company_name"]}}, {"name": "list_bionic_upgrades", "description": "List all available bionic upgrades.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "apply_bionic_upgrade", "description": "Apply for a specific bionic upgrade.", "parameters": {"type": "object", "properties": {"upgrade_name": {"type": "string", "description": "The name of the bionic upgrade to apply for."}}, "required": ["upgrade_name"]}}]