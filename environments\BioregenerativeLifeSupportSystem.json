[{"name": "list_greenhouses", "description": "List all available greenhouses in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_light_exposure", "description": "Adjust the light exposure duration for a specified greenhouse.", "parameters": {"type": "object", "properties": {"greenhouse_id": {"type": "string", "description": "The ID of the greenhouse to adjust."}, "light_exposure_hours": {"type": "number", "description": "The new light exposure duration in hours."}}, "required": ["greenhouse_id", "light_exposure_hours"]}}, {"name": "adjust_resource", "description": "Adjust resource allocation for life support systems.", "parameters": {"type": "object", "properties": {"resource_name": {"type": "string", "description": "The resource name."}, "new_replenishment_rate": {"type": "string", "description": "The new replenishment rate."}}, "required": ["resource_name", "new_replenishment_rate"]}}, {"name": "list_resources", "description": "List current resource allocations for life support systems.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_systems", "description": "Retrieve a list of all systems within the BioregenerativeLifeSupportSystem.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_atmospheric_conditions", "description": "Fetch current atmospheric levels including oxygen and carbon dioxide.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_system_setting", "description": "Modify the settings of a specific system component.", "parameters": {"type": "object", "properties": {"system_id": {"type": "string", "description": "The ID of the system to adjust."}, "new_setting": {"type": "string", "description": "The new setting to apply."}}, "required": ["system_id", "new_setting"]}}, {"name": "adjust_oxygen_levels", "description": "Adjust the oxygen levels in the system to the specified percentage.", "parameters": {"type": "object", "properties": {"percentage": {"type": "string", "description": "The desired oxygen level percentage."}}, "required": ["percentage"]}}, {"name": "check_oxygen_levels", "description": "check the oxygen levels in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_oxygen_concentration", "description": "Adjust the oxygen concentration in a specific habitat zone to a certain percentage.", "parameters": {"type": "object", "properties": {"zone_id": {"type": "string", "description": "The ID of the habitat zone."}, "oxygen_concentration": {"type": "string", "description": "The new oxygen concentration percentage to set."}}, "required": ["zone_id", "oxygen_concentration"]}}, {"name": "list_habitat_zones", "description": "List the different habitat zones within the space habitat.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_system_status", "description": "Retrieve the current status of the Bioregenerative Life Support System.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_system_parameter", "description": "Adjust a specific parameter of the Bioregenerative Life Support System.", "parameters": {"type": "object", "properties": {"parameter": {"type": "string", "description": "The parameter to adjust (e.g., 'oxygen_level', 'temperature', 'nutrient_supply_rate')."}, "value": {"type": "string", "description": "The new value for the parameter."}}, "required": ["parameter", "value"]}}]