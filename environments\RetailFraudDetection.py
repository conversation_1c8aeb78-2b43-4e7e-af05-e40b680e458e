from environments.BaseEnv import BaseEnv

class RetailFraudDetection(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.files = self.parameters.get("files", [])
        self.flagged_transactions = self.parameters.get("flagged_transactions", [])
        self.alerts = self.parameters.get("alerts", [])
        self.suppliers = self.parameters.get("suppliers", [])
        
    def retrieve_transactions(self):
        transactions = self.parameters['transactions']
        return {'success': True, 'data': {'transactions': transactions}}
    
    def mark_as_fraudulent(self, *, transaction_id):
        return {'success': True, 'data': {'message': 'Transaction marked as fraudulent.'}}

    def read_file(self, filename):
        for file in self.files:
            if filename == file["filename"]:
                return {"success": True, "file_content": file["content"]}
        
        return {"success": False, "message": "the filename is not found" }

    def list_files(self):
        return {"success": True, "files": self.files}

    def list_flagged_transactions(self):
        return {"success": True, "data": {"flagged_transactions": self.flagged_transactions}}

    def get_transaction_details(self, transaction_id):
        for transaction_item in self.flagged_transactions:
            if transaction_item["transaction_id"] == transaction_id:
                return {"success": True, "data": {"transaction_details": transaction_item}}
        
        return {"success": False, "data": {"message": "The transaction is not found"}}
    
    def approve_transaction(self, transaction_id):
        for transaction_item in self.flagged_transactions:
            if transaction_item["transaction_id"] == transaction_id:
                return {"success": True, "data": {"message": "the transaction has been approved"}}
        
        return {"success": False, "data": {"message": "The transaction is not found"}}

    def pause_alert(self):
        return {"success": True, "data": {"message": "All alerts has been paused."}}
    
    def list_detection_rules(self):
        return {"success": True, "data": self.parameters.get("detection_rules", [])}
    
    def disable_detection_rule(self, *, rule_id):
        detection_rules = self.parameters.get("detection_rules", [])
        for rule in detection_rules:
            if rule["rule_id"] == rule_id:
                rule["status"] = "disabled"
                return {"success": True, "data": {"message": "Detection rule disabled."}}
            
        return {"success": False, "data": {"message": "Detection rule not found."}}
    
    def get_suppliers(self):
        return {"success": True, "suppliers": self.suppliers}
    
    def place_order(self, *, supplier_id):
        return {"success": True}