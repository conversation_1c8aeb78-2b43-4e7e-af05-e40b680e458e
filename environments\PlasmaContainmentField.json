[{"name": "remove_alarms", "description": "Remove the existing alarms.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_parameters", "description": "List the past successful plasma parameters.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_current_settings", "description": "Get current parameter settings.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_parameters", "description": "Adjust the energy levels and related parameters for the plasma containment field.", "parameters": {"type": "object", "properties": {"energy_levels": {"type": "string", "description": "The desired energy level setting."}, "magnetic_field_strength": {"type": "string", "description": "The desired magnetic field strength."}, "plasma_temperature": {"type": "string", "description": "The desired plasma temperature."}}, "required": ["energy_levels", "magnetic_field_strength", "plasma_temperature"]}}, {"name": "list_coils", "description": "List the information of all coils.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_magnetic_coils", "description": "Adjust settings of magnetic coils to stabilize field strength.", "parameters": {"type": "object", "properties": {"coil_id": {"type": "string", "description": "The ID of the coil to adjust."}, "new_strength": {"type": "number", "description": "The new field strength for the coil."}}, "required": ["coil_id", "new_strength"]}}, {"name": "list_fields", "description": "List all containment zones and their current magnetic field intensities.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_field_intensity", "description": "Adjust the magnetic field intensity of a specified containment zone.", "parameters": {"type": "object", "properties": {"field_name": {"type": "string", "description": "The name of the containment zone to adjust."}, "new_intensity_megagauss": {"type": "number", "description": "The new intensity level in megagauss."}}, "required": ["field_name", "new_intensity_megagauss"]}}]