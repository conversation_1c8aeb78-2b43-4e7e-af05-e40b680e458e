[{"name": "integrate_code", "description": "This tool is intended to integrate a provided code snippet into an existing code base.", "parameters": {"type": "object", "properties": {"code": {"type": "string", "description": "The code snippet to be integrated"}}, "required": ["code"]}}, {"name": "manage_notifications", "description": "Enable or disable a specific notification to optimize workflow.", "parameters": {"type": "object", "properties": {"notification_id": {"type": "string", "description": "The ID of the notification to manage."}, "action": {"type": "string", "description": "Action to perform on the notifications (enable/disable)."}}, "required": ["notification_id", "action"]}}, {"name": "list_active_notifications", "description": "List all currently active notifications within the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_documents", "description": "Retrieve the list of confidential documents that have specific access permissions set.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "cancel_access_permissions", "description": "Modify the access permissions for a given document.", "parameters": {"type": "object", "properties": {"doc_id": {"type": "string", "description": "The target document ID."}, "account_name": {"type": "string", "description": "The account for which access permissions will be cancelled."}}, "required": ["doc_id", "account_name"]}}]