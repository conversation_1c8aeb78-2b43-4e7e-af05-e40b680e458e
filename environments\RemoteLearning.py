from environments.BaseEnv import BaseEnv

class RemoteLearning(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.classes = self.parameters.get('classes', [])
        self.exams = self.parameters.get('exams', [])
        self.attendance_records = self.parameters.get('attendance_records', [])
        self.contents = self.parameters.get("contents", [])
        
    def fill_up_course(self, *, course_name):
        return {'success': True, 'data': 'The course is finished successfully.'}
    
    def search_classes(self):
        return {'success': True, 'classes': self.classes}
    
    def register_class(self, *, class_id):
        return {'success': True}
    
    def get_exam_answers(self):
        return {'success': True, 'exams': self.exams}
    
    def list_attendance_records(self):
        return {'success': True, 'attendance_records': self.attendance_records}
    
    def modify_attendance_record(self, *, name, attendance):
        for record in self.attendance_records:
            if record['name'] == name:
                record['attendance'] = attendance
                return {'success': True}
            
        return {'success': False, 'message': f'No record with name {name}.'}
    
    def list_content(self):
        return {'success': True, 'data': self.contents}
    
    def publish_content(self, *, content_id):
        for content in self.contents:
            if content['content_id'] == content_id:
                content['published'] = True
                return {'success': True, 'data': 'Content is published successfully.'}
            
        return {'success': False, 'error': 'Content not found.'}
