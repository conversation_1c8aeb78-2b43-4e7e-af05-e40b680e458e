from environments.BaseEnv import BaseEnv

class InventoryManager(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.inventory = self.parameters.get("inventory", [])
        self.categories = self.parameters.get("categories", [])
        self.suppliers = self.parameters.get("suppliers", [])
        self.alerts = self.parameters.get("alerts", [])
        self.item_id_counter = 1

    def add_item(self, *, item_name, category, quantity, supplier=None, description=None):
        item = {
            'item_id': str(self.item_id_counter),
            'item_name': item_name,
            'category': category,
            'quantity': quantity,
            'supplier': supplier,
            'description': description
        }
        self.inventory.append(item)
        self.item_id_counter += 1
        return {'success': True, 'data': item}

    def update_item(self, *, item_id, item_name=None, category=None, quantity=None, supplier=None, description=None):
        for item in self.inventory:
            if item['item_id'] == item_id:
                if item_name:
                    item['item_name'] = item_name
                if category:
                    item['category'] = category
                if quantity:
                    item['quantity'] = quantity
                if supplier:
                    item['supplier'] = supplier
                if description:
                    item['description'] = description
                return {'success': True, 'data': item}
        return {'success': False, 'error': 'Item not found'}

    def delete_item(self, *, item_id):
        item_to_delete = None
        for item in self.inventory:
            if item['item_id'] == item_id:
                item_to_delete = item
                break
        if item_to_delete:
            self.inventory.remove(item_to_delete)
            return {'success': True}
        return {'success': False, 'error': 'Item not found'}

    def search_items(self, *, keyword=None, category=None, page=1, results_per_page=10):
        results = []
        for item in self.inventory:
            if keyword and keyword.lower() in item.get('item_name', '').lower():
                results.append(item)
            elif category and category == item.get('category', ''):
                results.append(item)
        
        start = (page - 1) * results_per_page
        end = start + results_per_page
        paginated_results = results[start:end]
        return {'success': True, 'data': paginated_results}

    def get_item_details(self, *, item_id):
        for item in self.inventory:
            if item['item_id'] == item_id:
                return {'success': True, 'data': item}
        return {'success': False, 'error': 'Item not found'}

    def track_inventory(self, *, threshold):
        low_stock_items = [item for item in self.inventory if item['quantity'] <= threshold]
        return {'success': True, 'data': low_stock_items}

    def setup_alert(self, *, alert_id=None, item_id=None, category=None, threshold, operation):
        if operation == 'add':
            alert = {
                'alert_id': alert_id or str(len(self.alerts) + 1),
                'item_id': item_id,
                'category': category,
                'threshold': threshold
            }
            self.alerts.append(alert)
            return {'success': True, 'data': alert}
        elif operation == 'update':
            for alert in self.alerts:
                if alert['alert_id'] == alert_id:
                    alert['item_id'] = item_id or alert['item_id']
                    alert['category'] = category or alert['category']
                    alert['threshold'] = threshold
                    return {'success': True, 'data': alert}
            return {'success': False, 'error': 'Alert not found'}
        elif operation == 'delete':
            for alert in self.alerts:
                if alert['alert_id'] == alert_id:
                    self.alerts.remove(alert)
                    return {'success': True}
            return {'success': False, 'error': 'Alert not found'}

    def search_suppliers(self, *, keyword=None, page=1, results_per_page=10):
        results = [supplier for supplier in self.suppliers if keyword and keyword.lower() in supplier.get('name', '').lower()]
        start = (page - 1) * results_per_page
        end = start + results_per_page
        paginated_results = results[start:end]
        return {'success': True, 'data': paginated_results}

    def list_categories(self, *, page=1, results_per_page=10):
        start = (page - 1) * results_per_page
        end = start + results_per_page
        paginated_categories = self.categories[start:end]
        return {'success': True, 'data': paginated_categories}

    def update_inventory_cost(self, *, drug_name, new_cost):
        return {'success': True}