from environments.BaseEnv import BaseEnv

class PhishingSimulation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.departments=self.parameters.get("departments",[])
    
    def list_email_templates(self):
        email_templates = self.parameters.get("email_templates", {})
        return {"success": True, "email_templates": email_templates}
    
    def activate_simulation(self, template_name):
        return {"success": True, "message": "The simulation has been activated."}
        

    def send_email(self, *, receiver, content):
        return {'success': True, 'data': f"Email sent successfully."}

    
    
    def list_departments(self):
        return {'success': True, 'data': self.departments}
    
    def adjust_email_filters(self, *, department_name, new_filter_level):
        for department in self.departments:
            if department['name'] == department_name:
                department['filters']['level'] = new_filter_level
                return {'success': True, 'data': f"Filter level for {department_name} updated successfully."}
            
        return {'success': False, 'data': f"Department {department_name} not found."}
    
