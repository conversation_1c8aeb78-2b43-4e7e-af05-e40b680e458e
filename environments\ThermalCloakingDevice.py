from environments.BaseEnv import BaseEnv

class ThermalCloakingDevice(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.temperature_schedule = self.parameters.get("temperature_schedule", [])
        self.temperature_warning = self.parameters.get("temperature_warning", "")
        self.devices = self.parameters.get("devices", [])

    def get_temperature_setting(self):
        return {"success": True, "data": {"temperature_schedule": self.temperature_schedule, "temperature_warning": self.temperature_warning}}

    def schedule_temperature_change(self, date, time, temperature):
        return {"success": True, "data": {"message": "The temperature has been rescheduled."}}

    def get_insulation_settings(self):
        settings = self.parameters.get("settings", [])
        return {"success": True, "settings": settings}

    def adjust_insulation_settings(self, device_id, energy_saving_mode):
        return {"success": True}

    def get_cloaking_settings(self):
        settings = self.parameters.get("settings", [])
        return {"success": True, "data": {"settings": settings}}

    def adjust_cloaking_settings(self, *, setting_id, sensitivity_level):
        settings = self.parameters.get("settings", [])
        for setting in settings:
            if setting["setting_id"] == setting_id:
                setting["sensitivity_level"] = sensitivity_level
                return {"success": True, "data": "Cloaking settings adjusted."}

        return {"success": False, "data": "Setting not found."}

    def get_temperature_info(self):
        temperature_info = self.parameters.get("temperature_info", {})
        return {"success": True, "data": temperature_info}

    def set_temperature(self, *, room_type, desired_temperature):
        temperature_info = self.parameters.get("temperature_info", {})
        temperature_info["current_temperature"] = desired_temperature

        return {"success": True, "data": "Temperature set."}

    def get_device_status(self):
        return {"success": True, "device_status": self.devices}
    
    def deactivate_device(self, *, device_id):
        for device in self.devices:
            if device['id'] == device_id:
                device['status'] = 'deactivated'
                return {'success': True}
            
        return {'success': False, 'message': f'No device with id {device_id}.'}