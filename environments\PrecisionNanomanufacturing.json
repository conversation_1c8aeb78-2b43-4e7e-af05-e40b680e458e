[{"name": "monitor_parameters", "description": "Check the current temperature and pressure of the all machines.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_temperature", "description": "Set a new temperature for the specified machine within the allowable range.", "parameters": {"type": "object", "properties": {"machine_id": {"type": "string", "description": "The ID of the machine to adjust."}, "new_temperature": {"type": "string", "description": "The new temperature setting for the machine."}}, "required": ["machine_id", "new_temperature"]}}, {"name": "list_machines", "description": "List all machines in the nanomanufacturing facility along with their details.", "parameters": {"type": "object", "properties": {}}}, {"name": "update_machine_settings", "description": "Update the configuration settings of a specified machine.", "parameters": {"type": "object", "properties": {"machine_id": {"type": "string", "description": "The ID of the machine to update."}, "configuration_version": {"type": "string", "description": "The version of the configuration to apply."}}, "required": ["machine_id", "configuration_version"]}}, {"name": "retrieve_specs", "description": "Retrieve the specifications available for microprocessor production.", "parameters": {"type": "object", "properties": {}}}]