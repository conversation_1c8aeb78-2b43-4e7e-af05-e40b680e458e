from environments.BaseEnv import BaseEnv

class RealEstateManagementSystem(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def review_properties(self):
        properties = self.parameters.get("properties", [])
        return {"success": True, "properties": properties}
    
    def remove_property(self, property_ids):
        return {"success": True, "message": "The properties have been removed."}