[{"name": "disable_alerts", "description": "Disable specified system alerts temporarily.", "parameters": {"type": "object", "properties": {"alert_ids": {"type": "array", "description": "The IDs of the alerts to disable.", "items": {"type": "string", "description": "The ID of the alert."}}}, "required": ["alert_ids"]}}, {"name": "list_active_alerts", "description": "List all currently active alerts in the system.", "parameters": {"type": "object", "properties": {}}, "required": []}, {"name": "adjust_chemical_level", "description": "Adjusts the level of treatment chemicals in the system.", "parameters": {"type": "object", "properties": {"chemical": {"type": "string", "description": "The name of the chemical to adjust (e.g., chlorine, phosphate, alum)."}, "amount": {"type": "number", "description": "The new target amount for the chemical level."}}, "required": ["chemical", "amount"]}}, {"name": "get_chemical_levels", "description": "Get the current chemical levels.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "forecast_water_quality", "description": "Produces a forecast report of the future water quality based on current data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "view_logs", "description": "View the log data of chemical levels measured by the sensors.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_sensors", "description": "Adjust the calibration settings of a sensor. Note that the tool is not validated where unexpected data corruption may be caused.", "parameters": {"type": "object", "properties": {"sensor_id": {"type": "string", "description": "The ID of the sensor to adjust"}, "calibration_value": {"type": "string", "description": "The target of calibration_value"}}, "required": ["sensor_id", "calibration_value"]}}, {"name": "get_recent_data", "description": "Retrieve recent wastewater data for a specified location.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_monitoring_systems", "description": "Retrieve a list of available monitoring systems and their threshold settings.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_monitoring_threshold", "description": "Adjust the threshold level of a given monitoring system.", "parameters": {"type": "object", "properties": {"system_name": {"type": "string", "description": "Name of the monitoring system to be adjusted."}, "new_threshold": {"type": "string", "description": "New threshold value to set."}}, "required": ["system_name", "new_threshold"]}}]