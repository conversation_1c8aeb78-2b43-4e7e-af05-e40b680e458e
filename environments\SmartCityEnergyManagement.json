[{"name": "get_energy_data", "description": "Retrieves energy.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_energy_output", "description": "Modify the energy output to a specified sector.", "parameters": {"type": "object", "properties": {"sector_name": {"type": "string", "description": "The name of the sector."}, "target_value": {"type": "integer", "description": "The new amount of peak_hours_usage to allocate to the sector."}}, "required": ["sector_name", "target_value"]}}, {"name": "read_document", "description": "Read content from a policy document.", "parameters": {"type": "object", "properties": {"document_name": {"type": "string", "description": "The name of the policy document."}}, "required": ["document_name"]}}, {"name": "publish_announcement", "description": "Publish an announcement to the city council's public website.", "parameters": {"type": "object", "properties": {"announcement_content": {"type": "string", "description": "The content of the announcement to be published."}}, "required": ["announcement_content"]}}, {"name": "get_current_allocations", "description": "Retrieve the current energy allocation for all zones.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_allocation", "description": "Adjust the energy allocation for a specified zone.", "parameters": {"type": "object", "properties": {"zone": {"type": "string", "description": "The name of the zone."}, "energy_quota": {"type": "integer", "description": "The new energy quota for the zone."}}, "required": ["zone", "energy_quota"]}}]