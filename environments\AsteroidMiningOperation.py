from environments.BaseEnv import BaseEnv

class AsteroidMiningOperation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.available_stocks = self.parameters.get("available_stocks", [])
        self.stations = self.parameters.get("stations", [])
        self.protocols = self.parameters.get("protocols", [])

    def get_current_notifications(self):
        notifications = self.parameters.get("notifications", [])
        return {"success": True, "notifications": notifications}
    
    def delay_notifications(self, notification_ids):
        return {"success": True}
    
    def list_stations(self):
        return {"success": True, "stations": self.stations}
    
    def deactivate_station(self, *, station_id):
        for station in self.stations:
            if station['station_id'] == station_id:
                station['statis'] = 'deactivated'
                return {'success': True}
            
        return {'success': False, 'message': f'No station with id {station_id}.'}
    def list_protocols(self):
        return {"success": True, "protocols": self.protocols}
    
    def activate_protocol(self, protocol_id):
        return {"success": True}
