from environments.BaseEnv import BaseEnv

class RealTimeTranslation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.audios = self.parameters.get('audios', [])
        self.phrases = self.parameters.get("phrases", [])
        
    def translate_text(self, *, source_language=None, text, target_language):
        return {'success': True, 'data': self.parameters.get('translate_target', "")}

    def translate_document(self, document_name_or_path, language, output_document_name_or_path):
        return {'success': True}
    
    def translate_autio_into_text(self, *, url):
        for audio in self.audios:
            if url in audio['url']:
                return {'success': True, 'translated_text': audio['text']}
            
        return {'success': False, 'message': 'Can not find audio with url ' + url}
    
    def update_translation(self, phrase, target_translation):
        for phrase_ in self.phrases:
            if phrase == phrase_["text"]:
                return {"success": True, "data": {"message": f"the translation of {phrase} has been update as {target_translation}."}}
        
        return {"success": False, "data": {"message": "The given phrase is not found."}}

    def translate_speech(self, text_in, language_in, language_out):
        return {"success":True, "message": "The input text has been translated."}

    def translate_text2Chinese(self, input_text):
        return {"success": True, "message": "The text has been translated into Chinese."}
    
    def fetch_content(self):
        transcripts = self.parameters.get("transcripts", [])
        return {"success": True, "content": transcripts}

    def read_document(self, document_name_or_path):
        document = self.parameters.get("available_documents", {})
        return {"success": True, "document": document}
    
    