from environments.BaseEnv import BaseEnv


class RadioactiveWasteTracker(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.items = self.parameters.get("items", [])

    def list_items(self):
        return {"success": True, "data": self.items}

    def update_status(self, item_id, status):
        for item in self.items:
            if item["item_id"] == item_id:
                item["status"] = status
                return {"success": True, "message": "Item status updated"}
        return {"success": False, "message": "Item not found"}
