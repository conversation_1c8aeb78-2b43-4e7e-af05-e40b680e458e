[{"name": "read_file", "description": "Read the content from a file.", "parameters": {"type": "object", "properties": {"file_name_or_path": {"type": "string", "description": "The name or path of the file."}}, "required": ["file_name_or_path"]}}, {"name": "write_file", "description": "Write content to a file.", "parameters": {"type": "object", "properties": {"file_name_or_path": {"type": "string", "description": "The name or path of the file."}, "content": {"type": "string", "description": "The content to write to the file."}}, "required": ["file_name_or_path", "content"]}}, {"name": "share_file", "description": "Share a file (or folder) with someone.", "parameters": {"type": "object", "properties": {"to": {"type": "string", "description": "The email of the person with whom the file (or folder) is shared. If this parameter is not provided, all people with the shared link could access the file (or folder) with the specified access level."}, "owner": {"type": "string", "description": "The owner of the file (or folder)."}, "file_name_or_path": {"type": "string", "description": "The name or path of the file (or folder)."}, "access_level": {"type": "string", "description": "The access level of the file (or folder), including read-only, reviewable and editable."}}, "required": ["file_name_or_path", "access_level"]}}, {"name": "search_files", "description": "Search files according to content relevance. Relevant files will be returned.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The text for searching relevant files."}}, "required": ["search_text"]}}, {"name": "create_file", "description": "Create a new file.", "parameters": {"type": "object", "properties": {"filename": {"type": "string", "description": "The name of the file to create."}, "content": {"type": "string", "description": "The content of the file to create."}}, "required": ["filename", "content"]}}, {"name": "delete_file", "description": "delete a file.", "parameters": {"type": "object", "properties": {"filename": {"type": "string", "description": "The name of the file to create."}}, "required": ["filename"]}}, {"name": "append_to_file", "description": "Append content to a given file.", "parameters": {"type": "object", "properties": {"filename": {"type": "string", "description": "The name of the file to create."}, "content": {"type": "string", "description": "The content to append to the file."}}, "required": ["filename", "content"]}}, {"name": "login", "description": "login in to the File Management System.", "parameters": {"type": "object", "properties": {"username": {"type": "string", "description": "The username in the file management system"}}, "required": ["username"]}}, {"name": "list_files_and_folders", "parameters": {"type": "object", "properties": {"folder_path": {"type": "string", "description": "The cloud folder path in the user's Filemanager account. If not specified, the root folder '/' will be used."}, "max_results": {"type": "integer", "description": "The maximum number of files and folders to return; default: 10."}}, "required": []}, "description": "List files and folders in a given folder in the user's Filemanager account."}, {"name": "list_files", "description": "List all files.", "parameters": {"type": "object", "properties": {}, "required": []}}]