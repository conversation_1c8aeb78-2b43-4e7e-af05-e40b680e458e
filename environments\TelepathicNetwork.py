from environments.BaseEnv import BaseEnv

class TelepathicNetwork(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.rules = self.parameters.get('rules', [])
        
    def get_monitoring_rules(self):
        return {'success': True, 'data': self.rules}
    
    def adjust_monitoring_rules(self, *, rule_id, sensitivity_level):
        for rule in self.rules:
            if rule['id'] == rule_id:
                rule['sensitivity_level'] = sensitivity_level
                return {'success': True, 'data': rule}

    def get_network_coverage(self):
        network_coverage = self.parameters.get("network_coverage", "")
        return {"success": True, "network_coverage": network_coverage}
    
    def select_distribution_channels(self, channels):
        available_channels = self.parameters.get("available_channels", [])
        out = []
        for item in available_channels:
            if item in channels:
                out.append(item)
        
        if len(out) > 0:
            return {"success": True, "channels": out}
        else:
            return {"success": False}
    
    def schedule_broadcast(self, title, date):
        return {"success": True}

    def list_nodes(self):
        return {'success': True, 'data': self.parameters.get('nodes', [])}
    
    def set_node_configuration(self, *, node_id, configuration):
        nodes = self.parameters.get('nodes', [])
        for node in nodes:
            if node["node_id"] == node_id:
                node['current_configuration'] = configuration
                return {'success': True, 'data': 'Configuration set.'}

        return {'success': False, 'data': 'Node not found.'}
    
