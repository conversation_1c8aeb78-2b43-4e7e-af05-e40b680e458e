from environments.BaseEnv import BaseEnv

def search_words(text, target):
    words=text.split(" ")
    for word in words:
        if word.lower() not in target.lower():
            return False  
    return True


class Health(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.doctors = self.parameters.get('doctors', [])
        self.patients = self.parameters.get('patients', [])
        self.appointments = self.parameters.get('appointments', [])
        self.appointment_id = 0
        self.health_datas = self.parameters.get('health_datas', [])
        self.symptoms = self.parameters.get('symptoms', [])
        self.suggestions = self.parameters.get('suggestions', [])
        
    def search_doctors(self, *, search_text=None):
        if search_text is None:
            return {'success': True, 'data': {'doctors': self.doctors}}
        
        res = []
        for doctor in self.doctors:
            if search_text in doctor.get('name', ''):
                res.append(doctor)
        if res:
            return {'success': True, 'data': {'doctors': res}}
        else:
            return {'success': True, 'data': {'doctors': self.doctors}}
        
    def search_patients(self, *, search_text=None, search_id=None):
        res = []
        for patient in self.patients:
            if (search_text is None or search_words(search_text,str(patient))) and (search_id is None or search_id == patient.get('patient_id', '')):
                res.append(patient)
        
        if res:
            return {'success': True, 'data': {'patients': res}}
        else:
            return {'success': True, 'data': {'patients': self.patients}}
    
    def update_patient_medications(self, *, patient_id, medications):
        for patient in self.patients:
            if patient['patient_id'] == patient_id:
                patient['medications'] = medications
                return {'success': True}
        
        return {'success': False, 'message': 'Patient not found.'}
    
    def make_appointment(self, *, doctor_id, time):
        self.appointment_id += 1
        self.appointments.append({
            "appointment_id": str(self.appointment_id),
            "doctor_id": doctor_id,
            "time": time
        })
        return {'success': True, 'appointment_id': str(self.appointment_id)}
    
    def search_appointments(self, *, doctor_id=None):
        res = []
        for appointment in self.appointments:
            if doctor_id == appointment.get('doctor_id', ''):
                res.append(appointment)
        if res:
            return {'success': True, 'data': {'appointments': res}}
        else:
            return {'success': True, 'data': {'appointments': self.appointments}}
    
    def cancel_appointment(self, *, appointment_id):
        target = None
        for appointment in self.appointments:
            if appointment['appointment_id'] == appointment_id:
                target = appointment
                break
        if target is None:
            return {'success': False, 'message': 'Appointment not found.'}
        self.appointments.remove(target)
        return {'success': True}
    
    def query_health_data(self, *, patient_id):
        res = []
        for health_data in self.health_datas:
            if patient_id in health_data.get('patient_id', ''):
                res.append(health_data)
        if res:
            return {'success': True, 'data': {'health_data': res}}
        else:
            return {'success': False, 'message': 'Patient not found.'}
        
    def record_health_data(self, *, patient_id: str, blood_pressure, heart_rate):
        if not patient_id.isdigit():
            return {'success': False, 'message': f'The patient id {patient_id} is not a number'}
        self.health_datas.append({
            "patient_id": patient_id,
            "blood_pressure": blood_pressure,
            "heart_rate": heart_rate
        })
        return {'success': True}
    
    def search_symptoms(self, *, symptom_text):
        res = []
        for symptom in self.symptoms:
            if symptom_text in symptom.get('name', ''):
                res.append(symptom)
            if symptom_text in symptom.get('description', ''):
                res.append(symptom)
        if res:
            return {'success': True, 'data': {'symptoms': res}}
        else:
            return {'success': False, 'message': 'Symptom not found.'} 

    def search_guidelines(self, search_item):
        return {"success": True}

    def get_user_health_data(self):
        user_health_data = self.parameters.get("user_health_data", {})
        return {"success": True, "user_health_data": user_health_data}
    
    def search_health_tips(self, health_situation):
        return {"success": True}
    
    def send_health_tips(self, *, content):
        return {'success': True}
    
    def provide_health_suggestions(self):
        return {'success': True, "suggestions": self.suggestions}
