[{"name": "list_cargo", "description": "List the current cargo items and their details.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_cargo_manifest", "description": "Update the cargo manifest by modifying the quantities for a specific destination.", "parameters": {"type": "object", "properties": {"item": {"type": "string", "description": "The name of the cargo item."}, "quantity": {"type": "integer", "description": "The new quantity of the cargo item."}, "destination": {"type": "string", "description": "The destination for the cargo item."}}, "required": ["item", "quantity", "destination"]}}, {"name": "list_zones", "description": "List all zones.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "add_zone_to_route", "description": "Add a specific zone to the route.", "parameters": {"type": "object", "properties": {"zone_name": {"type": "string", "description": "The name of the zoo."}}, "required": ["zone_name"]}}, {"name": "list_components", "description": "List all available components of the interstellar travel system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "shutdown_component", "description": "Shut down a specified component of the interstellar travel system.", "parameters": {"type": "object", "properties": {"component_name": {"type": "string", "description": "The name of the component."}}, "required": ["component_name"]}}]