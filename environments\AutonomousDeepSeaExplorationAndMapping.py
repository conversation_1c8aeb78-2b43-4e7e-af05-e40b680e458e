from environments.BaseEnv import BaseEnv


class AutonomousDeepSeaExplorationAndMapping(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.mission_reports = self.parameters.get('mission_reports', [])

    def get_drone_status(self):
        drones = self.parameters.get("get_drone_status", [])
        return {"success": True, "drones": drones}

    def get_weather_reports(self):
        weather_conditions = self.parameters.get("weather_conditions", {})
        return {"success": True, "weather_conditions": weather_conditions}

    def adjust_drone_parameters(self, id, new_range):
        return {"success": True}

    def list_mission_reports(self):
        return {'success': True, 'mission_reports': self.mission_reports}