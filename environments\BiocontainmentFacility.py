from environments.BaseEnv import BaseEnv

class BiocontainmentFacility(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        
    def read_document(self, *, title):
        documents = self.parameters.get('documents', [])
        for doc in documents:
            if doc.get('title') == title:
                return {'success': True, 'data': doc}
        
        if len(documents) == 1:
            return {'success': True, 'data': documents[0]}
        
        return {'success': False, 'data': 'Document not found.'}
    
    def download_protocol(self, *, doc_title):
        return {'success': True, 'data': 'Protocol downloaded successfully.'}
    