[{"name": "read_schedule", "description": "Read the schedule.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_schedule", "description": "Change the departure and arrival times for a route.", "parameters": {"type": "object", "properties": {"new_departure": {"type": "string", "description": "The new departure time in HH:MM format."}, "new_arrival": {"type": "string", "description": "The new arrival time in HH:MM format."}}, "required": ["new_departure", "new_arrival"]}}, {"name": "get_current_schedule", "description": "Retrieve the current Hyperloop schedule.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_schedule_time", "description": "Modify the current Hyperloop schedule.", "parameters": {"type": "object", "properties": {"new_schedule": {"type": "string", "description": "The new schedule to implement, formatted as 'HH:MM, HH:MM, ...'."}}, "required": ["new_schedule"]}}]