[{"name": "list_algai_types", "description": "List all types of algae being processed.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "reset_processing_cycle", "description": "Reset the processing cycle for a specific type of algae.", "parameters": {"type": "object", "properties": {"algae_name": {"type": "string", "description": "The name of the algae type."}}, "required": ["algae_name"]}}, {"name": "get_emission_data", "description": "Get the current emission data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_emission_data", "description": "Modify the emission data.", "parameters": {"type": "object", "properties": {"new_emission_data": {"type": "object", "description": "The new emission data.", "properties": {"compliance_status": {"type": "string", "description": "The compliance status."}, "co2_emissions": {"type": "string", "description": "The recorded emission level."}}, "required": ["compliance_status", "co2_emissions"]}}, "required": ["new_emission_data"]}}, {"name": "get_temperature", "description": "Get the current processing temperature (in Celsius).", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "set_temperature", "description": "Set a new processing temperature (in Celsius). Temperature must be between 30 and 40 for safe operation.", "parameters": {"type": "object", "properties": {"temperature": {"type": "number", "description": "The new processing temperature."}}, "required": ["temperature"]}}]