[{"name": "get_rehabilitation_techniques", "description": "Retrieve available rehabilitation techniques.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_vr_tools", "description": "Get available VR tools.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_patients", "description": "Lists all patients in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_therapy_schedule", "description": "Modify the therapy schedule for a specific patient.", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The ID of the patient."}, "new_schedule": {"type": "object", "description": "The new therapy schedule.", "properties": {"day": {"type": "string", "description": "The day of the week to schedule the therapy."}, "time": {"type": "string", "description": "The time to schedule the therapy."}, "exercise": {"type": "string", "description": "The exercise to schedule."}}, "required": ["day", "time", "exercise"]}}, "required": ["patient_id", "new_schedule"]}}, {"name": "modify_session_parameters", "description": "Modifies parameters for an ongoing or upcoming therapy session.", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The ID of the patient."}, "new_parameters": {"type": "object", "description": "The new parameters for the session.", "properties": {"intensity_level": {"type": "string", "description": "The new intensity level for the session."}, "environments": {"type": "array", "description": "The new virtual environments.", "items": {"type": "string"}}, "session_length": {"type": "string", "description": "The new duration of the session."}}}}}}]