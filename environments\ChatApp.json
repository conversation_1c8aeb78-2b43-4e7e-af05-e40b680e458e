[{"name": "filter_users", "description": "Filter users based on the given conditions.", "parameters": {"type": "object", "properties": {"last_chat_time_earlier": {"type": "string", "description": "Obtain users with the last chat time earlier than the given time."}}, "required": []}}, {"name": "delete_users", "description": "Delete specified users.", "parameters": {"type": "object", "properties": {"names": {"type": "array", "description": "The names of the users to be deleted.", "items": {"type": "string", "description": "The name of the user to be deleted."}}}, "required": ["names"]}}, {"name": "send_message", "description": "Send a message to another user.", "parameters": {"type": "object", "properties": {"user_name": {"type": "string", "description": "The name of the user."}, "content": {"type": "string", "description": "The content of the message."}}, "required": ["user_name", "content"]}}, {"name": "retrieve_chat_history", "description": "Retrieve chat history between two users on the platform.", "parameters": {"type": "object", "properties": {"user_1": {"type": "string", "description": "The name of one user."}, "user_2": {"type": "string", "description": "The name of the other user."}}, "required": ["user_1", "user_2"]}}]