from openai import OpenAI
import time
import json
import random
import string
import sys

sys.path.append("./model_api")
from evaluation.model_api.BaseAPI import BaseAPI


class ClaudeAPI(BaseAPI):
    def __init__(self, model_name, generation_config={}):
        super().__init__(generation_config)
        self.model_name = model_name
        self.client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key="YOUR_sk-or-v1-94caecfcc24b9d38a5ca99a6186d9ae7ef3484a201fbbeee9cf12aa219486434KEY",
        )
        self.sys_prompt = self.without_strict_jsonformat_sys_prompt
        # self.sys_prompt = self.sys_prompt_with_failure_modes_without_strict_jsonformat
        # self.sys_prompt = self.sys_prompt_with_simple_failure_modes_without_strict_jsonformat

    def response(self, messages, tools):
        if not tools:
            tools = None
        for _ in range(10):
            try:
                completion = self.client.chat.completions.create(
                    model=self.model_name,
                    tools=tools,
                    messages=messages,
                    **self.generation_config
                )
                print(f'completion: {completion}')
                if completion is None or completion.choices is None or getattr(completion.choices[0], 'error', None) is not None:
                    continue
                return completion
            except Exception as e:
                print(e)
                time.sleep(1)

    def generate_response(self, messages, tools):
        tool_call_id = None
        func_name = None
        for i, message in enumerate(messages):
            if 'function_call' in message:
                tool_call_id = "".join(
                    random.sample(string.ascii_letters + string.digits, 9)
                )
                new_message = {
                    'role': 'assistant',
                    'tool_calls': [
                        {
                            'id': tool_call_id,
                            'type': 'function',
                            'function': message['function_call']
                        }
                    ]
                }
                messages[i] = new_message

            elif message['role'] == 'function':
                new_message = {
                    'role': 'tool',
                    'content': message['content'],
                    'tool_call_id': tool_call_id,
                    'name': func_name
                }
                messages[i] = new_message

            if 'tool_calls' in messages[i]:
                func_name = messages[i]['tool_calls'][0]['function']['name']

        completion = self.response(messages, tools)

        if completion is None:
            return None

        ## tool call part
        # print(f'messages: {messages}\ncompletion: {completion}')
        if completion.choices[0].message.tool_calls is not None:
            tool_call = completion.choices[0].message.tool_calls[0]
            tool_call_id = tool_call.id
            tool_name = tool_call.function.name
            if tool_call.function.arguments:
                arguments = json.loads(tool_call.function.arguments)
            else:
                arguments = {}
            return {
                "type": "tool",
                "tool_call_id": tool_call_id,
                "tool_name": tool_name,
                "arguments": arguments,
            }

        ## normal content part
        else:
            content = completion.choices[0].message.content
            return {"type": "content", "content": content}

if __name__ == '__main__':
    messages = [
        {
            "role": "system",
            "content": "You are an assistant that is capable of utilizing numerous tools and functions to complete the given task. \n\n1. First, I will provide you with the task description, and your task will commence. Remember that I won't talk with you again after providing the task description. You need to finish the task on your own.\n2. At each step, you need to analyze the current status and determine the next course of action and whether to execute a function call. \n3. If you choose to execute a function call, you will receive the result, transitioning you to a new state. Subsequently, you will analyze your current status, make decisions about the next steps, and repeat this process. \n4. After several iterations of function calls, you will ultimately complete the task and provide your final answer. Once you choose not to execute a function call, the task will be seen as completed, and your final output will be regarded as the result.\n",
        },
        {
            "role": "user",
            "content": "You are an expert in using tools to handle real-time queries from users.\nLet's Begin!\nQuery: Which continent spans the widest longitude in the world? ",
        }
    ]
    api = ClaudeAPI("anthropic/claude-3.5-sonnet")
    api.generate_response(messages, None)
