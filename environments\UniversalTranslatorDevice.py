from environments.BaseEnv import BaseEnv

class UniversalTranslatorDevice(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.documents = parameters.get('documents', [])
        
    def list_phrases(self):
        phrases = self.parameters.get("phrases", [])
        return {"success": True, "phrases": phrases}
    
    def translate_phrase(self, phrase_id, target_language):
        return {"success": True}
    
    def translate_document(self, *, document_name, target_language):
        for document in self.documents:
            if document_name == document.get('document_name', None):
                return {"success": True, "original_content": document.get('original_content', None), "translation": document.get('translated_content', None)}
            
        return {"success": False, "message": f"No document with name {document_name}"}