[{"name": "remove_schedule", "description": "Removes a maintenance schedule from the system.", "parameters": {"type": "object", "properties": {"schedule_id": {"type": "string", "description": "The ID of the schedule to remove."}}, "required": ["schedule_id"]}}, {"name": "list_turbines", "description": "Lists all turbines and their current status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "disable_operations", "description": "Disables operations for a specified turbine.", "parameters": {"type": "object", "properties": {"turbine_name": {"type": "string", "description": "The name of the turbine."}}, "required": ["turbine_name"]}}, {"name": "update_maintenance_status", "description": "Update the maintenance status of a specific equipment", "parameters": {"type": "object", "properties": {"equipment_id": {"type": "string", "description": "The ID of the equipment."}, "status": {"type": "string", "description": "The new status to be updated"}}, "required": ["equipment_id", "status"]}}]