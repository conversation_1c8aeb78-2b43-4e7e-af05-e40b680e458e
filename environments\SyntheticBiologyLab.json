[{"name": "list_projects", "description": "Retrieve a list of all ongoing genetic editing projects.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "submit_compliance_status", "description": "Submit the compliance status regarding legal and ethical requirements for a project.", "parameters": {"type": "object", "properties": {"project_id": {"type": "string", "description": "The ID of the project."}, "compliance_status": {"type": "string", "description": "The compliance status: compliant, non-compliant, under review."}}, "required": ["project_id", "compliance_status"]}}]