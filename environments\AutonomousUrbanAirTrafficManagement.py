from environments.BaseEnv import BaseEnv


class AutonomousUrbanAirTrafficManagement(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.available_drones = self.parameters.get("available_drones", [])
        self.weather_conditions = self.parameters.get("weather_conditions", {})
        self.uavs = self.parameters.get("uavs", [])

    def check_weather_conditions(self, *, location):
        if location not in self.weather_conditions:
            return {"success": False, "error": "Location not found"}
        return {"success": True, "data": self.weather_conditions[location]}

    def get_drone_availability(self):
        return {"success": True, "data": self.available_drones}

    def schedule_delivery(self, *, drone_id, destination, cargo_description=None):
        return {"success": True, "message": "Delivery scheduled"}

    def list_uavs(self):
        return {"success": True, "data": self.uavs}
    
    def modify_flight_plan(self, *, uav_id, new_flight_plan):
        for uav in self.uavs:
            if uav["uav_id"] == uav_id:
                uav["flight_plan"] = new_flight_plan
                return {"success": True, "message": "Flight plan modified"}
        return {"success": False, "error": "UAV not found"}