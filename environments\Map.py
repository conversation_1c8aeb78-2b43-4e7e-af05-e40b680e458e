from environments.BaseEnv import BaseEnv

class Map(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.current_location = self.parameters.get('current_location', '123 Main St, City, 12345')
        self.locations = self.parameters.get('locations', [])  
        self.address_book = self.parameters.get('address_book', [])  
        self.reviews = self.parameters.get('reviews', {})  

    def get_current_location(self):
        return {'success': True, 'data': {'location': self.current_location}}

    def search_locations(self, *, keywords, open_now=None, min_rating=None, base_location_address=None, max_distance=None, max_results=10):
        results = []
        if max_distance is not None:
            max_distance = float(max_distance)
        if max_results is not None:
            max_results = int(max_results)
            
        for location in self.locations:
            if keywords.lower() in location['name'].lower():
                if open_now is not None and location.get('open_now') != open_now:
                    continue
                if min_rating is not None and location.get('location_rating', 0) < min_rating:
                    continue
                if base_location_address is not None and max_distance is not None and location.get('location_distance', float('inf')) > max_distance:
                    continue
                results.append(location)
        return {'success': True, 'data': {'locations': results[:max_results]}}

    def get_location_details(self, *, location_address):
        for location in self.locations:
            if location['address'] == location_address:
                return {'success': True, 'data': {'location_details': location}}
        return {'success': False, 'error': 'Location not found'}


    def get_directions(self, *, start_location_address=None, end_location, mode):
        return {'success': True, 'data': {'directions': ['Turn right','Walk 2 miles'], "estimated_time": "0:12",  "estimated_distance": "2.5 miles"}}

    def geocoding(self, *, location_address):
        return {'success': True, 'data': {'coordinates': '37.7749,-122.4194'}}

    def reverse_geocoding(self, *, coordinates):
        # Mock conversion from coordinates to address
        return {'success': True, 'data': {'address': '123 Mock St, City, 12345'}}

    def get_location_reviews(self, *, location_address):
        return {'success': True, 'data': {'reviews': self.reviews.get(location_address, [])}}

    def search_address_book(self, *, keywords):
        results = [entry for entry in self.address_book if keywords.lower() in entry['name'].lower()]
        return {'success': True, 'data': {'results': results}}

    def add_address(self, *, name, address, note):
        self.address_book.append({'name': name, 'address': address, 'note': note})
        return {'success': True}
