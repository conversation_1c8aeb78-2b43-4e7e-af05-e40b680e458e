from environments.BaseEnv import BaseEnv

class MolecularAssembler(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def list_reactor_vessels(self):
        reactor_vessels = self.parameters.get('reactor_vessels', [])
        return {'success': True, 'data': reactor_vessels}

    def diffuse_chemical_mixture(self, *, reactor_name):
        return {'success': True, 'data': 'Action completed successfully.'}
    
    def list_tasks(self):
        tasks = self.parameters.get('tasks', [])
        return {'success': True, 'data': tasks}

    def assemble_molecule(self, *, task_name, parameters):
        tasks = self.parameters.get('tasks', [])
        for task in tasks:
            if task.get("task_name") == task_name:
                for param in parameters:
                    task[param] = parameters[param]
                return {'success': True, 'data': 'Parameters changed successfully.'}
        return {'success': False, 'data': 'Task not found.'}
    
