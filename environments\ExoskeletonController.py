from environments.BaseEnv import BaseEnv


class ExoskeletonController(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def list_walking_patterns(self):
        walking_patterns = self.parameters.get("walking_patterns", [])
        return {"success": True, "walking_patterns": walking_patterns}

    def update_walking_pattern(self, pattern_id, energy_modulation):
        return {"success": True, "message": "The pattern has been updated."}
