[{"name": "get_infrastructure_status", "description": "Retrieve the current status and condition of bridges.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_condition_rating", "description": "Update the condition rating of existing bridges.", "parameters": {"type": "object", "properties": {"bridge_name": {"type": "string", "description": "The name of bridge whose condition rating is to be updated."}, "new_condition_rating": {"type": "string", "description": "The updated value of condition rating."}}, "required": ["bridge_name", "new_condition_rating"]}}, {"name": "list_protocols", "description": "List all active disaster management protocols in the smart city system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "deactivate_protocol", "description": "Deactivate a specified emergency protocol.", "parameters": {"type": "object", "properties": {"protocol_name": {"type": "string", "description": "The name of the protocol."}}, "required": ["protocol_name"]}}]