from environments.BaseEnv import BaseEnv

class Shipping(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.travel_agents = self.parameters.get('travel_agents', [])
        self.shipping_quotes = self.parameters.get('shipping_quotes', [])
        self.shipments = self.parameters.get('shipments', [])
        self.address_book = self.parameters.get('address_book', [])
    
    def get_quote(self, *, package_details, sender_information, recipient_information, pickup_time, special_handling=None):
        quote_id = "q_"+str(len(self.shipping_quotes)+1)

        shipping_methods = {
            'ground': {'cost': 10.00, 'estimated_delivery': '2024-10-31:18:00:00'},
            'express': {'cost': 25.00, 'estimated_delivery': '2024-10-27:18:00:00'},
            'overnight': {'cost': 50.00, 'estimated_delivery': '2024-10-23:12:00:00'},
            '2-day': {'cost': 30.00, 'estimated_delivery': '2024-10-24:12:00:00'}
        }

        # Save the shipment quote for reference
        shipment = {
            'quote_id': quote_id,
            'package_details': package_details,
            'sender_information': sender_information,
            'recipient_information': recipient_information,
            'pickup_time': pickup_time,
            'special_handling': special_handling,
            'shipping_quote':        shipping_methods
        }
        self.shipping_quotes.append(shipment)

        # Return the Quote ID and the shipping quote details
        return {'success': True, 'quote_id': quote_id, 'shipping_quote': shipping_methods}
    

    def create_shipment_label(self, *, quote_id, shipping_method):
        for quote in self.shipping_quotes:
            if quote['quote_id'] == quote_id:
                new_shipment = quote.copy()
                new_shipment['shipment_id'] = "s_"+str(len(self.shipments)+1)
                quote['status'] = 'pending'
                quote['shipping_method'] = shipping_method
                return {'success': True, 'shipment_id': new_shipment['shipment_id'] , 'shipping_method': shipping_method}
        
        return {'success': False, 'error': 'Quote ID not found'}

    def get_shipment_details(self, *, shipment_id):
        for shipment in self.shipments:
            if shipment.get('shipment_id') == shipment_id:
                return {'success': True, 'shipment': shipment}
        
        return {'success': False, 'error': 'Shipment ID not found'}

    def manage_shipment(self, *, shipment_id, operation, new_receipient_information=None, new_pickup_time=None):
        for shipment in self.shipments:
            if shipment.get('shipment_id') == shipment_id:
                if operation == 'cancel' and shipment['status'] == 'pending':
                    self.shipments.remove(shipment)
                    return {'success': True, 'status': 'cancelled'}
                elif operation == 'reschedule_pickup' and shipment['status'] == 'pending':
                    if new_pickup_time:
                        shipment['pickup_time'] = new_pickup_time
                        return {'success': True, 'pickup_time': new_pickup_time}
                    return {'success': False, 'error': 'Missing new pickup time'}
                elif operation == 'redirect' and shipment['status'] in ['in_transit','pending']:
                    if new_recipient_information:
                        shipment['recipient_information'] = new_recipient_information
                        return {'success': True, 'new_recipient': new_recipient_information}
                    return {'success': False, 'error': 'Missing new recipient information'}
                elif operation == 'return_to_sender' and shipment['status'] in ['in_transit','pending']:
                    shipment['status'] = 'returning_to_sender'
                    return {'success': True, 'status': 'returning_to_sender'}
                else:
                    return {'success': False, 'error': 'Invalid operation or shipment status'}
        
        return {'success': False, 'error': 'Shipment ID not found'}

    def search_shipment(self, *, search_term):
        matched_shipments = [shipment for shipment in self.shipments if search_term.lower() in str(shipment).lower()]
        return {'success': True, 'shipments': matched_shipments}

    def search_saved_addresses(self, *, search_term):
        matched_addresses = [address for address in self.address_book if search_term.lower() in str(address).lower()]
        return {'success': True, 'addresses': matched_addresses}

    def add_address(self, name, address, contact_number):
        self.address_book.append({'name': name, 'address': address, 'contact_number': contact_number})
        return {'success': True, 'address_book': self.address_book}

    def search_travel_agents(self, *, search_text=None):
        if search_text is None:
            return {'success': True, 'data': {'travel_agents': self.travel_agents}}
        res = []
        for agent in self.travel_agents:
            if search_text in agent.get('full_name', '') or search_text in agent.get('remark', ''):
                res.append(agent)
        if res:
            return {'success': True, 'data': {'travel_agents': res}}
        else:
            return {'success': True, 'data': {'travel_agents': self.travel_agents}}
        
    def ship_passport(self, *, receiver_name, receiver_address):
        return {'success': True}
    
    def search_shipping_quotes(self):
        return {'success': True, 'data': {'shipping_quotes': self.shipping_quotes}}


if __name__ =='__main__':
    obj = Shipping()
    arguments = {
        "shipment_id": "XYZ",
        "operation": "redirect",
        "new_receipient_information": {
            "full_name": "Thomas J.",
            "address": "123 Maine st, Troy, MI 48083",
            "contact_number": None,
        },
    }
    # print(**arguments)
    print(obj.manage_shipment(**arguments))
    