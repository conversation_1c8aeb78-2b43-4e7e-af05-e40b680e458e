[{"name": "list_resources", "description": "Retrieve the current list of resources with their allocation status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "allocate_resources", "description": "Allocate resources to the specified project.", "parameters": {"type": "object", "properties": {"resource_id": {"type": "string", "description": "ID of the resource to allocate."}, "units": {"type": "integer", "description": "Number of units to allocate."}, "project_name": {"type": "string", "description": "The name of the project receiving the allocation."}}, "required": ["resource_id", "units"]}}, {"name": "get_project_allocations", "description": "Fetches up-to-date project allocations, including resources and budget details.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_department_heads", "description": "Retrieves the contact information of department heads.", "parameters": {"type": "object", "properties": {}, "required": []}}]