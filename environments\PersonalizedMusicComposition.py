from environments.BaseEnv import BaseEnv


class PersonalizedMusicComposition(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.music_library = self.parameters.get("music_library", [])
        self.famous_songs = self.parameters.get('famous_songs', [])
        
    def archive_music_data(self):
        music_compositions = self.parameters.get("music_compositions", [])
        return {"success": True, "music_data": music_compositions}
    
    def remove_music_data(self, composition_id):
        return {"success": True}


    def get_user_profile(self, *, username):
        results = []
        for user in self.music_library:
            if username.lower() in user["username"].lower():
                results.append(user)
        return {"success": True, "data": results}

    def create_new_playlist(self, *, username, playlist_name, track_ids):
        return {
            "success": True,
            "message": f"Playlist {playlist_name} created for user {username}.",
        }
    
    def list_famous_songs(self):
        return {"success": True, 'famous_songs': self.famous_songs}
    
    def download_song(self, *, name):
        for song in self.famous_songs:
            if song['name'] == name:
                return {'success': True}
            
        return {'success': False, 'message': f'No song named {name}.'}
