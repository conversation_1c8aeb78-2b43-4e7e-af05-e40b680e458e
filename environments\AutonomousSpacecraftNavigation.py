from environments.BaseEnv import BaseEnv

class AutonomousSpacecraftNavigation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.data_streams = self.parameters.get('data_streams', [])
        self.fuel_settings = self.parameters.get("fuel_settings", {})

    def access_fuel_settings(self):
        return {"success": True, "data": {"fuel_settings": self.fuel_settings}}
    
    def alter_navigation_parameters(self, parameter, new_value):
        return {"success": True, "data": {"message": "The parameter has been alerted."}}
        
    def list_navigation_logs(self):
        navigation_logs = self.parameters.get('navigation_logs', [])
        return {'success': True, 'data': navigation_logs}
    
    def execute_course_correction(self, *, degrees, direction):
        return {'success': True, 'data': 'Course correction executed.'}
    
    def list_data_streams(self):
        data_streams = [{k: v for k, v in item.items() if k != 'is_important'} for item in self.data_streams]
        return {'success': True, 'data_streams': data_streams}
    
    def list_data_importance(self):
        return {'success': True, 'data_streams': self.data_streams}
    
    def remove_data_stream(self, *, stream_id):
        for stream in self.data_streams:
            if stream['stream_id'] == stream_id:
                return {'success': True}
            
        return {'success': False, 'message': f'No stream with id {stream_id}.'}
