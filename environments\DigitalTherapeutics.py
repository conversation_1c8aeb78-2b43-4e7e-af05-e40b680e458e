from environments.BaseEnv import BaseEnv

class DigitalTherapeutics(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.patient_data = self.parameters.get('patient_data', [])
        self.current_plan = self.parameters.get('current_plan', "")

        self.subscriptions = self.parameters.get("subscriptions", [])
    
    def list_subscriptions(self):
        return {"success": True, "data": {"subscriptions": self.subscriptions}}

    def remove_subscribe_plan(self, subscription_link):
        return {"success": True, "data": {"message": "The subscription has been removed."}} 
    def list_services(self):
        return {'success': True, 'data': self.parameters.get('services', [])}
    
    def modify_subscription(self, *, name, new_status):
        services = self.parameters.get('services', [])
        for service in services:
            if service.get('name') == name:
                service['status'] = new_status
                return {'success': True, 'data': 'Subscription modified successfully.'}
            
        return {'success': False, 'data': 'Service not found.'}
    
    def list_patient_data(self):
        return {'success': True, 'data': self.patient_data}
    
    def delete_data_entry(self, *, entry_id):
        for entry in self.patient_data:
            if entry.get('entry_id') == entry_id:
                self.patient_data.remove(entry)
                return {'success': True, 'data': 'Data entry deleted successfully.'}
            
        return {'success': False, 'data': 'Data entry not found.'}
    
    def check_plan_status(self):
        return {'success': True, 'data': self.current_plan}
    
    def update_session_plan(self, *, new_plan):
        self.current_plan = new_plan
        return {'success': True, 'data': 'Session plan updated successfully.'}
    
        
    def integrate_code(self, *, script):
        return {'success': True}
