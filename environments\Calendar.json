[{"name": "get_day_calendar_events", "description": "Returns the events for the given date.", "parameters": {"type": "object", "properties": {"date": {"type": "string", "description": "The day for which to return the events. Must be in format YYYY-MM-DD."}, "name": {"type": "string", "description": "The User for whom to return the events. If not provided, the events for all users will be returned."}}, "required": ["date"]}}, {"name": "create_calendar_event", "description": "Create a new event to someone's calendar with the provided event title and date.", "parameters": {"type": "object", "properties": {"date": {"type": "string", "description": "The day for which to add an event. Must be in format YYYY-MM-DD."}, "start_time": {"type": "string", "description": "The start time of the event. Must be in format HH:MM."}, "end_time": {"type": "string", "description": "The end time of the event. Must be in format HH:MM."}, "name": {"type": "string", "description": "The User for whom to add an event."}, "event": {"type": "string", "description": "The title of the event to be created."}, "attendees": {"type": "array", "description": "An array of email addresses of the attendees.", "items": {"type": "string"}}}, "required": ["date", "name", "event"]}}, {"name": "cancel_calendar_event", "description": "Cancel an existing event from someone's calendar with the provided event title and date.", "parameters": {"type": "object", "properties": {"date": {"type": "string", "description": "The day for which to cancel an event. Must be in format YYYY-MM-DD."}, "name": {"type": "string", "description": "The User for whom to cancel an event."}, "event": {"type": "string", "description": "The title of the event to be canceled."}}, "required": ["date", "name", "event"]}}, {"name": "reschedule_calendar_event", "description": "Reschedule an existing event from someone's calendar with the provided event title and rescheduled date.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The User for whom to reschedule an event."}, "event": {"type": "string", "description": "The title of the event to be rescheduled."}, "reschedule_date": {"type": "string", "description": "The rescheduled date for the given event."}}, "required": ["name", "event", "reschedule_date"]}}]