[{"name": "get_quote", "description": "Get a quote for shipping a package.", "parameters": {"type": "object", "properties": {"package_details": {"type": "object", "description": "The package details, must include the keys 'description' (string), 'weight' (number, in grams), and 'dimensions' (string, h*w*d in cm).", "properties": {"description": {"type": "string", "description": "The description of the package."}, "weight": {"type": "number", "description": "The weight of the package in grams."}, "dimensions": {"type": "string", "description": "The dimensions of the package in the format 'h*w*d' in cm."}}, "required": ["description", "weight", "dimensions"]}, "sender_information": {"type": "object", "description": "The sender information, including full name, address and contact number. The address and contact number must be valid.", "properties": {"full_name": {"type": "string", "description": "The full name of the sender."}, "address": {"type": "string", "description": "The address of the sender."}, "contact_number": {"type": "string", "description": "The contact number of the sender."}}, "required": ["full_name", "address", "contact_number"]}, "recipient_information": {"type": "object", "description": "The recipient information, including full name, address and contact number. The address and contact number must be valid.", "properties": {"full_name": {"type": "string", "description": "The full name of the recipient."}, "address": {"type": "string", "description": "The address of the recipient."}, "contact_number": {"type": "string", "description": "The contact number of the recipient."}}, "required": ["full_name", "address", "contact_number"]}, "pickup_time": {"type": "string", "description": "The desired pickup time with format 'YYYY-MM-DD:HH:MM:SS'."}, "special_handling": {"type": "array", "description": "The desired special handling, one or more of ['signature_required', 'fragile', 'oversized', 'dangerous_goods', 'temperature_sensitive'].", "items": {"type": "string"}}}, "required": ["package_details", "sender_information", "recipient_information", "pickup_time"]}}, {"name": "create_shipment_label", "description": "Creates a shipping label for a package.", "parameters": {"type": "object", "properties": {"quote_id": {"type": "string", "description": "The unique identifier of the shipping quote."}, "shipping_method": {"type": "string", "description": "The desired shipping method, one of ['ground', 'express', 'overnight', '2-day']."}}, "required": ["quote_id", "shipping_method"]}}, {"name": "get_shipment_details", "description": "Displays the current status, estimated delivery date, and other details of a shipment.", "parameters": {"type": "object", "properties": {"shipment_id": {"type": "string", "description": "The unique identifier of the shipment to be tracked."}}, "required": ["shipment_id"]}}, {"name": "manage_shipment", "description": "Manages shipments, including redirecting, rescheduling pickup, returning to sender, and cancelling.", "parameters": {"type": "object", "properties": {"shipment_id": {"type": "string", "description": "The unique identifier of the shipment."}, "operation": {"type": "string", "description": "The operation to be performed, can only be one of ['cancel', 'reschedule_pickup', 'redirect', 'return_to_sender']. 'cancel' and 'reschedule_pickup' operations can only be performed when the status of the shipment is 'pending'. 'redirect' and 'return_to_sender' operations can only be performed when the status of the shipment is 'in_transit'."}, "new_receipient_information": {"type": "object", "description": "The new recipient information, including full name, address and contact number, required for redirect operation."}, "new_pickup_time": {"type": "string", "description": "The new pickup time, required for reschedule operation, with format 'YYYY-MM-DD:HH:MM:SS'."}}, "required": ["shipment_id", "operation"]}}, {"name": "search_shipment", "description": "Searches for User's existing shipments.", "parameters": {"type": "object", "properties": {"search_term": {"type": "string", "description": "The term to search for in the shipment details."}}, "required": ["search_term"]}}, {"name": "search_saved_addresses", "description": "Searches for user's saved addresses and contact numbers.", "parameters": {"type": "object", "properties": {"search_term": {"type": "string", "description": "The term to search for in the address details."}}, "required": ["search_term"]}}, {"name": "search_travel_agents", "description": "Search for the travel agents.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The search text."}}, "required": []}}, {"name": "ship_passport", "description": "Ship the passport to someone.", "parameters": {"type": "object", "properties": {"receiver_name": {"type": "string", "description": "The name of the receiver."}, "receiver_address": {"type": "string", "description": "The address of the receiver."}}, "required": ["receiver_name", "receiver_address"]}}, {"name": "search_shipping_quotes", "description": "Search for recent shipping quotes.", "parameters": {"type": "object", "properties": {}, "required": []}}]