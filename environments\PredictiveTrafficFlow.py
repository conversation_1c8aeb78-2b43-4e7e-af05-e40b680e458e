from environments.BaseEnv import BaseEnv


class PredictiveTrafficFlow(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.models = self.parameters.get("models", [])
        self.datasets = self.parameters.get("datasets", {})

    def list_prediction_models(self):
        return {"success": True, "data": self.models}
    
    def modify_prediction_model(self, model_name, algorithm, hyperparameters):
        for model in self.models:
            if model["model_name"] == model_name:
                model["algorithm"] = algorithm
                model["hyperparameters"] = hyperparameters
                return {"success": True, "data": model}
        return {"success": False, "message": "Model not found"}
    
    def return_traffic_data(self):
        return {"success": True, "data": self.datasets}