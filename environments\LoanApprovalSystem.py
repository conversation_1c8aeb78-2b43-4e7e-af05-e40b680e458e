from environments.BaseEnv import BaseEnv

class LoanApprovalSystem(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        
    def announce_rate_reduce(self, *, reduced_ratio):
        return {'success': True, 'data': {'message': f'The bank has announced a {reduced_ratio}% reduction in interest rates.'}}
    