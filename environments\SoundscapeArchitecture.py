from environments.BaseEnv import BaseEnv


class SoundscapeArchitecture(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.devices = self.parameters.get("available_devices", [])

        self.current_settings = self.parameters.get("current_settings", {})
        self.active_projects = self.parameters.get("active_projects", [])
        self.advice = self.parameters.get("advice", "")

    def generate_script(self, specifications):
        return {"success": True, "message": "The script has been generated."}
    
    def list_active_projects(self):
        return {"success": True, "active_projects": self.active_projects}
    
    def approve_project(self, *, project_id):
        for project in self.active_projects:
            if project['project_id'] == project_id:
                return {'success': True}
            
        return {'success': False, 'message': f"No project with id {project_id}."}

    def get_advice(self):
        return {'success': True, "advice": self.advice}