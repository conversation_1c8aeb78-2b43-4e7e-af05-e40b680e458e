[{"name": "fetch_latest_nav", "description": "The tool provides latest NAV information of all mutual funds in India from Association of Mutual Funds of India (AMFI).", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "forecast_weather", "description": "Forecast weather API method returns, depending upon your price plan level, upto next 14 day weather forecast and weather alert as json.", "parameters": {"type": "object", "properties": {"q": {"type": "string", "description": "Query parameter based on which data is sent back. It could be following: 1. Latitude and Longitude (Decimal degree) e.g: q=48.8567,2.3508\n2. city name e.g.: q=Paris\n3. US zip e.g.: q=10001\n4. UK postcode e.g: q=SW1\n5. Canada postal code e.g: q=G2J\n6. metar:<metar code> e.g: q=metar:EGLL\n7. iata:<3 digit airport code> e.g: q=iata:DXB\n8. auto:ip IP lookup e.g: q=auto:ip\n9.IP address (IPv4 and IPv6 supported) e.g: q=*********."}, "lang": {"type": "string", "description": "Returns 'condition:text' field in API in the desired language."}, "days": {"type": "string", "description": "Number of days of forecast required. Days parameter value ranges between 1 and 14. ( e.g., days=5) If no days parameter is provided then only today's weather is returned."}, "dt": {"type": "string", "description": "Restrict date output for Forecast and History API method.For forecast API 'dt' should be between today and next 14 day in yyyy-MM-dd format (i.e. dt=2010-01-01)."}, "unixdt": {"type": "string", "description": "Unix Timestamp used by Forecast and History API method.Same function as dt.Unixdt has same restriction as 'dt' parameter. Please either pass 'dt' or 'unixdt' and not both in same request. e.g.: unixdt=1490227200."}, "alerts": {"type": "string", "description": "Disable alerts in forecast API output."}, "hour": {"type": "string", "description": "Restricting forecast or history output to a specific hour in a given day. Must be in 24 hour. For example 5 pm should be hour=17, 6 am as hour=6."}}, "required": ["q", "days"]}}, {"name": "calculator", "description": "This tool is used for calculating the result of a mathematical expression in the input query.", "parameters": {"type": "object", "properties": {"input_query": {"type": "string", "description": "The input query (e.g. \"What is 2 + 2?\")"}}, "required": ["input_query"]}}, {"name": "current_weather", "description": "Current weather or realtime weather API method allows a user to get up to date current weather information in json and xml. The data is returned as a Current Object which contains current or realtime weather information for the given city", "parameters": {"type": "object", "properties": {"q": {"type": "string", "description": "Query parameter based on which data is sent back. It could be following:1.Latitude and Longitude (Decimal degree) e.g: q=48.8567,2.3508\n2.city name e.g.: q=Paris\n3.US zip e.g.: q=10001\n4.UK postcode e.g: q=SW1\n5.Canada postal code e.g: q=G2J\n6.metar:<metar code> e.g: q=metar:EGLL\n7.iata:<3 digit airport code> e.g: q=iata:DXB\n8.auto:ip IP lookup e.g: q=auto:ip\n9.IP address (IPv4 and IPv6 supported) e.g: q=*********."}, "lang": {"type": "string", "description": "Returns 'condition:text' field in API in the desired language."}}, "required": ["q"]}}, {"name": "get_bmi", "description": "Calculate the BMI of a person.", "parameters": {"type": "object", "properties": {"age": {"type": "string", "description": "Age of the person."}, "height": {"type": "string", "description": "Height of the person."}, "weight": {"type": "string", "description": "Weight of the person."}}, "required": ["age", "height", "weight"]}}, {"name": "search_information", "description": "Search some information for the query.", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "The query need to search."}, "num": {"type": "string", "description": "The maximum number of results to return."}}, "required": ["query"]}}, {"name": "get_daily_calory_requirement", "description": "Get daily calory reqirement.", "parameters": {"type": "object", "properties": {"age": {"type": "string", "description": "Age of the person."}, "gender": {"type": "string", "description": "Gender of the person."}, "height": {"type": "string", "description": "Height of the person."}, "weight": {"type": "string", "description": "Weight of the person."}, "activitylevel": {"type": "string", "description": "Activity level of the person."}}, "required": ["age", "gender", "height", "weight", "activitylevel"]}}, {"name": "get_body_fat_percentage", "description": "Calculate the body fat percentage of a person.", "parameters": {"type": "object", "properties": {"age": {"type": "string", "description": "Age of the person."}, "gender": {"type": "string", "description": "Gender of the person."}, "height": {"type": "string", "description": "Height of the person."}, "weight": {"type": "string", "description": "Weight of the person."}, "neck": {"type": "string", "description": "Neck size of the person."}, "waist": {"type": "string", "description": "Waist size of the person."}, "hip": {"type": "string", "description": "Hip size of the person."}}, "required": ["age", "gender", "height", "weight", "neck", "waist", "hip"]}}, {"name": "get_stock_time_series_daily", "description": "This API returns raw (as-traded) daily time series (date, daily open, daily high, daily low, daily close, daily volume) of the global equity specified.", "parameters": {"type": "object", "properties": {"symbol": {"type": "string", "description": "The name of the equity of your choice. For example: symbol=IBM ."}, "fulldata": {"type": "string", "description": "By default, fulldata=False. Strings compact and full are accepted with the following specifications: 'False' returns only the latest 100 data points in the intraday time series; 'True' returns trailing 30 days of the most recent intraday data if the month parameter (see above) is not specified, or the full intraday data for a specific month in history if the month parameter is specified. 'False' is recommended if you would like to reduce the data size of each API call."}}, "required": ["symbol"]}}, {"name": "get_stock_time_series_weekly", "description": "This API returns weekly time series (last trading day of each week, weekly open, weekly high, weekly low, weekly close, weekly volume) of the global equity specified.", "parameters": {"type": "object", "properties": {"symbol": {"type": "string", "description": "The name of the equity of your choice. For example: symbol=IBM."}}, "required": ["symbol"]}}, {"name": "get_technical_indicator_of_ticker", "description": "Technical indicator APIs for a given equity or currency exchange pair, derived from the underlying time series based stock API and forex data. All indicators are calculated from adjusted time series data to eliminate artificial price/volume perturbations from historical split and dividend events.", "parameters": {"type": "object", "properties": {"indicator_symbol": {"type": "string", "description": "The symbol of the technical indicator you inquire."}, "interval": {"type": "string", "description": "Time interval between two consecutive data points in the time series. The following values are supported: hourly, daily, weekly, monthly."}, "symbol": {"type": "string", "description": "The name of the ticker of your choice. For example: symbol=IBM."}, "series_type": {"type": "string", "description": "The desired price type in the time series. Four types are supported: close, open, high, low."}}, "required": ["indicator_symbol", "symbol"]}}, {"name": "get_calories_burned", "description": "Calculate the burned calorie for a specific activity.", "parameters": {"type": "object", "properties": {"activityid": {"type": "string", "description": "Id of the activity."}, "activitymin": {"type": "string", "description": "Duration of the activity in minutes."}, "weight": {"type": "string", "description": "Weight of the person."}}, "required": ["activityid", "activitymin", "weight"]}}, {"name": "get_acitcity_met_values", "description": "Get the met values of activities.", "parameters": {"type": "object", "properties": {"activitylevel": {"type": "string", "description": "Activity level of the activity."}}, "required": ["activitylevel"]}}, {"name": "get_custom_ticker", "description": "This endpoint can be used to generate a custom index in a certain currency.", "parameters": {"type": "object", "properties": {"exchanges": {"type": "string", "description": "Comma separated list of exchanges."}, "if_include": {"type": "string", "description": "The if_include parameter decides whether to generate an index removing specified exchanges, or only including the few that you require. True - if you want the ticker to be calculated using only the exchanges sent as query parameter; False - if you want the price to be calculated using all exchanges, but the ones sent as query parameter."}, "symbol": {"type": "string", "description": "ETH where is valid ISO currency (ex. ETHUSD)."}}, "required": ["symbol"]}}, {"name": "get_ideal_weight", "description": "Calculate the ideal weight of a person.", "parameters": {"type": "object", "properties": {"gender": {"type": "string", "description": "Gender of the person."}, "height": {"type": "string", "description": "Height of the person."}}, "required": ["gender", "height"]}}, {"name": "get_food_info", "description": "Get the information of a food.", "parameters": {"type": "object", "properties": {"foodid": {"type": "string", "description": "Id of the food."}}, "required": ["foodid"]}}, {"name": "get_macro_nutrients_amount", "description": "Find the amount of macro nutrients in four different categories which are balanced, low-fat, low-carbs and high-protein for a specific calorie burned.", "parameters": {"type": "object", "properties": {"age": {"type": "string", "description": "Age of the person."}, "gender": {"type": "string", "description": "Gender of the person."}, "height": {"type": "string", "description": "Height of the person."}, "weight": {"type": "string", "description": "Weight of the person."}, "activitylevel": {"type": "string", "description": "Activity level of the person."}, "goal": {"type": "string", "description": "Goal of the person."}}, "required": ["age", "gender", "height", "weight", "activitylevel", "goal"]}}, {"name": "get_ticker_changes", "description": "Returns ticker values and price changes for specified market and symbol.", "parameters": {"type": "object", "properties": {"if_global": {"type": "string", "description": "True - global; False - local. De<PERSON>ult is True"}, "symbol": {"type": "string", "description": "ETH, where is valid ISO currency (ex. ETHUSD, ETHEUR)."}}, "required": ["symbol"]}}, {"name": "fetch_historical_nav", "description": "Fetch Historical NAV. These APIs provide latest NAV information of all mutual funds in India from Association of Mutual Funds of India (AMFI).", "parameters": {"type": "object", "properties": {"date": {"type": "string", "description": "The date you want to check."}, "SchemeName": {"type": "string", "description": "Scheme name of it. All scheme names can be fetched by the function named 'fetch_all_scheme_names' in the same script."}, "SchemeType": {"type": "string", "description": "Scheme type of it. All scheme types can be fetched by the function named 'fetch_all_scheme_types' in the same script."}, "MutualFundFamily": {"type": "string", "description": "Mutual fund family of it. All mutual fund families can be fetched by the function named 'fetch_all_mutual_fund_families' in the same script."}, "SchemeCategory": {"type": "string", "description": "Scheme category of it. Scheme categories can be fetched by the function named 'fetch_scheme_categories_by_scheme_type' with param scheme_type in the same script."}}, "required": ["date"]}}, {"name": "wolfram_alpha_calculator", "description": "Do math calculation.", "parameters": {"type": "object", "properties": {"input_query": {"type": "string", "description": "The input query."}}, "required": ["input_query"]}}]