import sys
from datetime import datetime


class TeeOutput:
    def __init__(self, filename, mode="w"):
        self.file = open(filename, mode)
        self.stdout = sys.stdout
        self.stderr = sys.stderr


class StdoutTee(TeeOutput):
    def write(self, data):
        # timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
        # file_data = f"{timestamp} [INFO] {data}"
        file_data = data
        self.file.write(file_data)
        self.stdout.write(data)
        self.flush()

    def flush(self):
        self.file.flush()
        self.stdout.flush()

    def close(self):
        if self.file is not None:
            self.file.close()
            self.file = None

def redirect_output(output_file, mode):
    tee_out = StdoutTee(output_file, mode)
    sys.stdout = tee_out
    sys.stderr = tee_out


if __name__ == "__main__":
    # 使用示例
    output_file = "output.txt"
    tee_out = StdoutTee(output_file, mode='a')

    sys.stdout = tee_out
    sys.stderr = tee_out

    # 测试
    print("Normal output")
    print("Error message", file=sys.stderr)
