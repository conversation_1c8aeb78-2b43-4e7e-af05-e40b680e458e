from environments.BaseEnv import BaseEnv

class OceanCurrentPredictor(BaseEnv):
    def __init__(self, *,parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.files = self.parameters.get('files', [])

    def list_files(self):
        return {'success': True, 'files': self.files}

    def change_access(self, *, file_id, access):
        for file in self.files:
            if file['id'] == file_id:
                file['access'] = access
                return {'success': True}
    
        return {'success': False, 'message': f'No file with id {file_id}.'}
            