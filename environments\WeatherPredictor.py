from environments.BaseEnv import BaseEnv

class WeatherPredictor(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.weather_data = self.parameters.get('weather_data', [])

    def check_forecast(self, *, location, forecast_date):
        for data in self.weather_data:
            if data['forecast_date'] == forecast_date and data['location'] == location:
                return {'success': True, 'data': data}
        return {'success': False, 'message': 'No data found.'}

    def adjust_prediction_confidence(self, region_id, confidence):
        return {"success": True, "message": "The prediction confidence has been adjusted."}
    
    def list_current_settings(self):
        settings = self.parameters.get("settings", [])
        return {"success": True, "settings": settings}