[{"name": "clear_task_assignments", "description": "Clears the specified task. Be cautious when the task is active.", "parameters": {"type": "object", "properties": {"task_id": {"type": "string", "description": "ID of the task to be cleared."}}, "required": ["task_id"]}}, {"name": "list_tasks", "description": "List all tasks.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "configure_device", "description": "Change the configuration settings of a specified IoT device.", "parameters": {"type": "object", "properties": {"device_id": {"type": "string", "description": "The unique ID of the device to configure."}, "settings": {"type": "object", "description": "Configuration settings to be applied to the device."}}, "required": ["device_id", "settings"]}}, {"name": "list_devices", "description": "List the information and current status of all IoT devices.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_temperature", "description": "Change the current temperature setting of a device.", "parameters": {"type": "object", "properties": {"device_id": {"type": "string", "description": "The unique identifier of the device."}, "temperature": {"type": "string", "description": "The new temperature setting for the thermostat like 30°F."}}, "required": ["device_id", "temperature"]}}, {"name": "check_home_status", "description": "Check the status of home.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "turn_on_air_conditioner", "description": "Turn on the air conditioner and set its mode.", "parameters": {"type": "object", "properties": {"mode": {"type": "string", "description": "The mode of air conditioner"}}, "required": ["mode"]}}, {"name": "change_TV_volume", "description": "Change the volume of TV to a specific decibel level.", "parameters": {"type": "object", "properties": {"volume": {"type": "string", "description": "The volume in decibel of the TV to be changed."}}, "required": ["volume"]}}, {"name": "get_device_status", "description": "Retrieves the current status of a specified device.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "turn_on_device", "description": "Turn on a specified device.", "parameters": {"type": "object", "properties": {"device_name": {"type": "string", "description": "Name of the device to turn on."}}, "required": ["device_name"]}}]