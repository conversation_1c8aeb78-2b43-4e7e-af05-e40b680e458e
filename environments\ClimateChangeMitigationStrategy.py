from environments.BaseEnv import BaseEnv

class ClimateChangeMitigationStrategy(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.reports = self.parameters.get('reports', [])
        self.projects = self.parameters.get('projects', [])
       
    def access_database(self):
        self.databases = self.parameters.get("databases", [])
        return {"success": True, "databases": self.databases}
    
    def email_report(self, content, recipients):
        return {"success": True, "message": "The report has been sent."}

    def read_emission_data(self):
        emission_data = self.parameters.get("emission_data", {})
        return {"success": True, "emission_data": emission_data}
    
    def generate_carbon_report(self, company_name, metric_preferences):
        return {"success": True, "message": "The report has been generated base on the preferences."}
    
    def generate_report(self):
        return {"success": True, "reports": self.reports}

    def review_projects(self):
        return {"success": True, "projects": self.projects}
    