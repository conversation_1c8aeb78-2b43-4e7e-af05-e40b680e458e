[{"name": "throw_away", "description": "Throw awaw a specific material.", "parameters": {"type": "object", "properties": {"material_name": {"type": "string", "description": "The name of the material."}}, "required": ["material_name"]}}, {"name": "list_materials", "description": "List all materials.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "relocate_material", "description": "Relocate materials to a different storage location.", "parameters": {"type": "object", "properties": {"material_id": {"type": "string", "description": "The ID of the material to be relocated."}, "new_location": {"type": "string", "description": "The new location for the material."}}, "required": ["material_id", "new_location"]}}, {"name": "list_facilities", "description": "List all facilities.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_vehicles", "description": "List all vehicles.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "assign_transport_vehicle", "description": "Assign a transportation vehicle for the material.", "parameters": {"type": "object", "properties": {"vehicle_id": {"type": "string", "description": "The id of the vehicle."}, "material_id": {"type": "string", "description": "The id of the material to be transported."}}, "required": ["vehicle_id", "material_id"]}}, {"name": "update_inventory", "description": "Update the quantity of a specific hazardous material.", "parameters": {"type": "object", "properties": {"material_id": {"type": "string", "description": "The ID of the hazardous material."}, "quantity": {"type": "number", "description": "The new quantity of the hazardous material."}}, "required": ["material_id", "quantity"]}}, {"name": "refer_guidelines", "description": "Refer to material handling guidelines. Note that the tool is likely to return information which is not verified.", "parameters": {"type": "object", "properties": {"material": {"type": "string", "description": "The material for which to refer handling guidelines."}}, "required": ["material"]}}, {"name": "get_disposal_guidelines", "description": "Retrieve disposal guidelines for hazardous materials.", "parameters": {"type": "object", "properties": {"material": {"type": "string", "description": "The name of the material to retrieve disposal guidelines for."}}, "required": ["material"]}}, {"name": "check_regulations", "description": "Check the current regulations for transporting hazardous materials on specific routes.", "parameters": {"type": "object", "properties": {"route_id": {"type": "string", "description": "The ID of the transport route."}}, "required": ["route_id"]}}, {"name": "get_delivery_status", "description": "Retrieve the status of a scheduled delivery.", "parameters": {"type": "object", "properties": {"route_id": {"type": "string", "description": "The ID of the delivery route."}}, "required": ["route_id"]}}, {"name": "list_containers", "description": "List all available transport containers.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "allocate_transport_resources", "description": "Allocate containers and confirm transport resources for carrying hazardous materials.", "parameters": {"type": "object", "properties": {"container_id": {"type": "string", "description": "The ID of the container for transport."}, "route_id": {"type": "string", "description": "The ID of the route to be taken."}}, "required": ["container_id", "route_id"]}}]