from environments.BaseEnv import BaseEnv
  

class DisasterRecovery(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.suppliers = self.parameters.get("suppliers", {})
        self.contracts = self.parameters.get("contracts", {})

        self.file_paths = self.parameters.get("file_paths", [])
        self.contents = self.parameters.get("contents", {})
        self.zones= self.parameters.get("zones", [])
        self.resources = self.parameters.get("resources", [])
        self.disaster_situation = self.parameters.get("disaster_situation", "")
        
    def create_contract(self, supplier_name, contract_value, duration_months, start_date):
        return {"success": True, "data": {"message": "The contract has been created"}}
    
    def get_supplier_info(self, supplier_id):
        if supplier_id in self.suppliers:
            return {"success": True, "data": {"supplier_info": self.suppliers[supplier_id]}}
        else:
            return {"success": False, "data": {"message": "The supplier is not found"}}

    def read_contract(self, contract_id):
        for contract in self.contracts:
            if contract_id == contract["contract_id"]:
                return {"success": True, "data": {"contract": contract}}
        
        return {"success": False, "data": {"message": "The contract is not found"}}

    def initiate_backup_restore(self, *, data_center_id):
        return {'success': True, 'data': 'Backup initiated successfully.'}
    
    def retrieve_backup_logs(self, *, data_center_id):
        data_centers = self.parameters.get('data_centers', [])
        for d in data_centers:
            if d['id'] == data_center_id:
                return {'success': True, 'data': d['backup_log']}
        
        if len(data_centers) == 1:
            return {'success': True, 'data': data_centers[0]['backup_log']}
        
        return {'success': False, 'data': 'Data center not found.'}

    def get_disaster_recovery_plan(self, city):
        cities = self.parameters.get("cities", [])
        for city_item in cities:
            if city == city_item["name"]:
                return {"success": True, "current_plan": city_item["current_plan"]}
        
        return {"success": False, "message": "No plan is found for the city."}

    def read_file(self, file_path):
        emergency_plans = self.parameters.get("emergency_plans", {})
        if "file_paths" in emergency_plans:
            file_paths = emergency_plans["file_paths"]
        if "contents" in emergency_plans:
            contents = emergency_plans["contents"]
        
        if file_path in contents:
            return {"success": True, "content": contents.get(file_path, "")}
        else:
            return {"success": False, "message": "The file is not found."}

    def recommend_updates(self, plan_type, new_recommendations):
        return {"success": True, "message": "Recommendations updates."}
    
    def list_resources(self):
        return {'success': True, 'data': self.resources}
    
    def list_zones(self):
        return {'success': True, 'data': self.zones}
    
    def reallocate_resources(self, *, resource_ids, target_location):
        return {'success': True, 'data': 'Resources reallocated successfully.'}
    
    def check_disaster_situation(self):
        return {'success': True, 'disaster_situation': self.disaster_situation}
    
    def broadcast_message(self, *, content):
        return {'success': True}
