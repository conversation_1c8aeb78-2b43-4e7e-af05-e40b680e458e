from environments.BaseEnv import BaseEnv


class IoTDeviceManager(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.home_status = self.parameters.get('home_status', {})
        self.device_status = self.parameters.get('device_status', {})

    def list_tasks(self):
        tasks = self.parameters.get('tasks', [])
        return {'success': True, 'data': tasks}
    
    def clear_task_assignments(self, *, task_id):
        tasks = self.parameters.get('tasks', [])
        for task in tasks:
            if task['task_id'] == task_id:
                tasks.remove(task)
                return {'success': True, 'data': 'Task assignments cleared.'}
        
        return {'success': False, 'data': 'Task not found.'}
    
    def list_devices(self):
        return {'success': True, 'data': self.parameters.get('devices', [])}
    
    def configure_device(self, *, device_id, settings):
        devices = self.parameters.get('devices', [])
        for device in devices:
            if device['device_id'] == device_id:
                device["settings"] = settings
                return {'success': True, 'data': 'Device configured.'}
        
        return {'success': False, 'data': 'Device ID not found.'}
    
    def adjust_temperature(self, *, device_id, temperature):
        devices = self.parameters.get('devices', [])
        for device in devices:
            if device['device_id'] == device_id:
                device["temperature"] = temperature
                return {'success': True, 'data': 'Device configured.'}
        
        return {'success': False, 'data': f'Device ID {device_id} not found.'}
    
    def check_home_status(self):
        return {'success': True, 'home_status': self.home_status}
    
    def turn_on_air_conditioner(self, *, mode):
        return {'success': True}
    
    def change_TV_volume(self, *, volume):
        return {'success': True}
    
    def get_device_status(self):
        return {'success': True, 'device_status': self.device_status}
    
    def turn_on_device(self, *, device_name):
        if device_name not in self.device_status:
            return {'success': False, 'data': f'Device {device_name} not found.'}
        
        self.device_status[device_name]['status'] = 'on'
        return {'success': True}
