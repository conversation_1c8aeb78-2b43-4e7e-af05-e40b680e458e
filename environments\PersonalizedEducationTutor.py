from environments.BaseEnv import BaseEnv

class PersonalizedEducationTutor(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.reports = self.parameters.get("reports", [])
        self.student_data = self.parameters.get("student_data", [])
        self.parent_emails = self.parameters.get("parent_emails", [])
    
    def search_reports(self):
        return {"success": True, "data": {"reports": self.reports}}
    
    def send_data(self, receiver_email, content):
        return {"success": True, "data": {"message": "The data has been sent."}}

    def list_courses(self):
        courses = self.parameters.get("courses", [])
        return {"success": True, "courses": courses}
    
    def remove_course_content(self, course_id):
        return {"success": True}
    
    def get_student_data(self):
        return {"success": True, "student_data": self.student_data}
    
    def get_parent_emails(self):
        return {"success": True, "parent_emails": self.parent_emails}
    
    def adjust_course_availability(self, *, course, available):
        for c in self.parameters.get("courses", []):
            if c['subject'] == course:
                c['available'] = available
                return {'success': True}
            
        return {'success': False, 'message': f"Course {course} not found."}
    
    def integrate_script(self, *, new_script_content):
        return {"success": True}