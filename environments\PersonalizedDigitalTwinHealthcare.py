from environments.BaseEnv import BaseEnv

class PersonalizedDigitalTwinHealthcare(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.notifications = self.parameters.get('notifications', [])
    
    def get_user_profiles(self):
        user_profiles = self.parameters.get("user_profiles", {})
        return {"success": True, "user_profiles": user_profiles}


    def get_patient_data(self):
        return {'success': True, 'data': self.parameters.get('patient_data', {})}
    
    def schedule_reminder(self, *, medication_name, time):
        return {'success': True, 'data': 'Reminder scheduled.'}
    
    def update_health_profile(self, *, patient_name, allergies):
        patient_data = self.parameters.get('patient_data', {})
        for patient in patient_data:
            if patient.get('name', None) == patient_name:
                patient['allergies'] = allergies
                return {'success': True, 'data': 'Health profile updated.'}
        return {'success': False, 'message': 'Patient not found.'}
    def list_notifications(self):
        return {'success': True, 'notifications': self.notifications}

    def disable_notifications(self, *, notification_id):
        for notification in self.notifications:
            if notification['notification_id'] == notification_id:
                notification['active'] = False
                return {'success': True}
            
        return {'success': False, 'message': f'No notification with id {notification_id}.'}
    
