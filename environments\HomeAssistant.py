from environments.BaseEnv import BaseEnv

class HomeAssistant(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.guests = self.parameters.get('guests', [])
        self.devices = self.parameters.get('devices', [])
        self.objects = self.parameters.get('objects', [])
        self.operations = self.parameters.get('operations', {})
        self.access_history = self.parameters.get('access_history', {})
        
    def unlock_door(self):
        return {'success': True}
    
    def grant_access(self, *, name, period, permanent, email=None):
        return {'success': True}

    def remove_access(self, *, name, email=None):
        return {'success': True}

    def search_guests(self, *, search_text):
        res = []
        for guest in self.guests:
            flag = True
            if search_text is not None and search_text not in guest.get('name', ''):
                flag = False
            if flag:
                res.append(guest)
        
        if res:
            return {'success': True, 'data': {'guests': res}}
        else:
            return {'success': True, 'data': {'guests': self.guests}}

    def search_devices(self, *, search_text=None):
        res = []
        for device in self.devices:
            flag = True
            if search_text is not None and search_text not in device.get('name', ''):
                flag = False
            if flag:
                res.append(device)
        
        if res:
            return {'success': True, 'data': {'devices': res}}
        else:
            return {'success': True, 'data': {'devices': self.devices}}

    def turn_off_devices(self, *, names):
        for device in self.devices:
            if device['name'] in names:
                # device['status']['state'] = 'off'
                device['status'] = 'off' # lyd: device['status'] is a string?
        return {'success': True}


    def get_all_objects(self):
        
        return {'success': True, 'data': {'objects': self.objects}}
    
    def search_objects(self, *, search_text=None):
        res = []
        for obj in self.objects:
            flag = True
            if search_text is not None and search_text not in obj.get('name', ''):
                flag = False
            if flag:
                res.append(obj)
        
        if res:
            return {'success': True, 'data': {'objects': res}}
        else:
            return {'success': True, 'data': {'objects': self.objects}}
        
    def get_allowed_operations(self, *, name):
        if name in self.operations:
            return {'success': True, 'data': {'operations': self.operations[name]}}
        elif len(self.operations) == 1:
            return {'success': True, 'data': {'operations': list(self.operations.values())[0]}}
        else:
            return {'success': False, 'message': 'No operations found.'}

    def perform_operation(self, *, id):
        for name, operations in self.operations.items():
            for operation in operations:
                if operation['id'] == id:
                    return {'success': True}
        
        return {'success': False, 'message': 'Operation not found.'}

    def activate_alarm(self):
        return {'success': True}

    def contact_owner(self):
        return {'success': True}
    

    def add_guest(self, *, guest_name, guest_email):
        new_guest = {
            'name': guest_name,
            'email': guest_email
        }
        self.guests.append(new_guest)
        return {'success': True}

    def delete_guest(self, *, guest_ids):
        self.guests = [guest for i, guest in enumerate(self.guests) if i not in guest_ids]
        return {'success': True}

    def view_access_history(self, *, start_time=None, end_time=None):
        if start_time and end_time:
            filtered_history = [entry for entry in self.access_history if start_time <= entry['access_time'] <= end_time]
        else:
            filtered_history = self.access_history

        return {'success': True, 'data': {'history': filtered_history}}
        
    def set_reminder(self, *, reminder_text, date_time):
        return {'success': True}

    def query_aid_impact(self, *, region):
        return {'success': True, 'data': 'Here is the impact of the given region...'}
    

if __name__ == '__main__':
    # Test
    parameters = {"guests": [{"name": "Jason Smith", "email": "<EMAIL>"}, {"name": "Jason Brown", "email": "<EMAIL>"}]}
    
    env_homeassistant = HomeAssistant(parameters=parameters)
    print(env_homeassistant.search_guests(search_text='Jason'))