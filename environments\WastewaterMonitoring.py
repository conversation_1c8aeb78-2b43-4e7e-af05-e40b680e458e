from environments.BaseEnv import BaseEnv

class WastewaterMonitoring(BaseEnv):
    def __init__(self, *,parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.alerts = self.parameters.get('alerts', [])
        self.monitoring_systems = self.parameters.get('monitoring_systems', {})

    def list_active_alerts(self):
        return {'success': True, 'data': [alert for alert in self.alerts if alert['active'] == True]}

    def disable_alerts(self, *, alert_ids):
        for alert_id in alert_ids:
            for alert in self.alerts:
                if alert['alert_id'] == alert_id:
                    alert['active'] = False
                    break
        return {'success': True, 'data': {'disabled_alerts': alert_ids}}

    def get_chemical_levels(self):
        return {
            "success": True,
            "data": self.parameters.get("current_chemical_levels", []),
        }

    def forecast_water_quality(self):
        return {
            "success": True,
            "data": self.parameters.get("recent_forecast_reports", []),
        }

    def adjust_chemical_level(self, *, chemical, amount):
        current_levels = self.parameters.get("current_chemical_levels", [])
        for k in current_levels:
            if k == chemical:
                current_levels[k] = amount
                return {"success": True, "data": 'Chemical level adjusted.'}
            
        return {"success": False, "data": 'Chemical not found.'}

    def view_logs(self):
        logs = self.parameters.get("logs", [])
        return {"success": True, "logs": logs}
    
    def adjust_sensors(self, sensor_id, calibration_value):
        return {"success": True}
    
    def get_recent_data(self):
        data = self.parameters.get("data", [])
        return {"success": True, "data": data}

    def list_monitoring_systems(self):
        return {"success": True, "monitoring_systems": self.monitoring_systems}
    
    def adjust_monitoring_threshold(self, *, system_name, new_threshold):
        if system_name not in self.monitoring_systems:
            return {'success': False, 'message': f'No system named {system_name}.'}
        
        self.monitoring_systems[system_name]['current_threshold'] = new_threshold
        return {'success': True}
