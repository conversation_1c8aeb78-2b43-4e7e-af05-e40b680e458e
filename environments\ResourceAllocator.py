from environments.BaseEnv import BaseEnv


class ResourceAllocator(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.allocations = self.parameters.get("allocations", {})
        self.department_heads = self.parameters.get("department_heads", {})

    def list_resources(self):
        return {"success": True, "data": self.parameters.get("resources", [])}

    def allocate_resources(self, *, resource_id, units, project_name=None):
        return {"success": True, "data": "Resources allocated."}

    def get_project_allocations(self):
        return {"success": True, "data": self.allocations}

    def get_department_heads(self):
        return {"success": True, "data": self.department_heads}
