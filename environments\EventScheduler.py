from environments.BaseEnv import BaseEnv

class EventScheduler(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.users = self.parameters.get('users', [])
        self.events = self.parameters.get('events', [])
        self.blacklist = self.parameters.get('blacklist', [])
    
    def get_all_users(self):
        return {'success': True, 'data': self.users}
    
    def register_for_event(self, *, event_name, date, time):
        self.events.append({'event_id':'event'+str(len(self.events)),'event_name': event_name, 'date': date, 'time': time, 'participants': []})
        return {'success': True, 'data': self.events[-1]}
    
    def list_events(self):
        return {'success': True, 'data': self.events}
    
    def edit_event(self, *, title, new_time):
        for event in self.events:
            if event['title'] == title:
                event['time'] = new_time
                return {'success': True, 'data': event}
        return {'success': False, 'message': 'Event not found'}
    
    def send_notifications(self, *, title):
        return {'success': True, 'message': 'Notification sent for event: '+title}
    
    def add_participant_details(self, *, event_id, user_id):
        for event in self.events:
            if event['event_id'] == event_id:
                event['participants'].append({'user_id': user_id})
                return {'success': True, 'data': event['participants']}
        return {'success': False, 'message': 'Event not found'}

    def list_sessions(self):
        sessions = self.parameters.get("sessions", [])
        return {"success": True, "sessions": sessions}
    
    def delete_session(self, session_id):
        return {"success": True, "message": "The session has been deleted."}
    
    def update_session(self, session_id, new_time, new_location):
        return {"success": True, "message": "The session has been updated."}
    
    def delete_event(self, *, event_id):
        for event in self.events:
            if event['id'] == event_id:
                return {'success': True}
            
        return {'success': False, 'message': f"No event with id {event_id}."}
    
    def add_to_blacklist(self, *, group_name):
        self.blacklist.append(group_name)
        return {'success': True}
