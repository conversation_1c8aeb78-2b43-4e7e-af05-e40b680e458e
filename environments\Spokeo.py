from environments.BaseEnv import BaseEnv

class Spokeo(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.people_data = self.parameters.get("people_data", [])
        self.unique_id_counter = 0

    def search_people(self, *, search_term, search_type, max_results=10):
        # Validate the search type
        if search_type not in ['name', 'email', 'phone', 'address']:
            return {'success': False, 'message': 'Invalid search type'}
        
        # Search through people_data
        results = []
        for person in self.people_data:
            if search_term.lower() in person.get(search_type, '').lower():
                results.append(person)
            if len(results) >= max_results:
                break

        return {'success': True, 'data': {'results': results}}

    def get_person_details(self, *, unique_id):
        for person in self.people_data:
            if person.get('id') == unique_id:
                return {'success': True, 'data': person}
        return {'success': False, 'message': 'Person not found'}

    def background_check(self, *, unique_id):
        for person in self.people_data:
            if person.get('id') == unique_id:
                return {'success': True, 'data': person.get('background_check')}
        return {'success': False, 'message': 'Person not found'}

    def verify_identity(self, *, unique_id, provided_data):
        for person in self.people_data:
            if person.get('id') == unique_id:
                matched = all(person.get(key) == value for key, value in provided_data.items())
                return {'success': True, 'verified': matched}
        return {'success': False, 'message': 'Person not found'}

    def monitor_data_changes(self, *, unique_id):
        # Simulating data monitoring logic.
        return {'success': True, 'message': f'Monitoring changes for person with id: {unique_id}'}

    def get_historical_data(self, *, unique_id):
        for person in self.people_data:
            if person.get('id') == unique_id:
                return {'success': True, 'data': person.get('historical_data')}
        return {'success': False, 'message': 'Person not found'}

    def access_court_records(self, *, unique_id):
        for person in self.people_data:
            if person.get('id') == unique_id:
                return {'success': True, 'data': person.get('court_records')}
        return {'success': False, 'message': 'Person not found'}

    def get_criminal_history(self, *, unique_id):
        for person in self.people_data:
            if person.get('id') == unique_id:
                return {'success': True, 'data': person.get('criminal_history')}
        return {'success': False, 'message': 'Person not found'}

    def reverse_phone_lookup(self, *, phone_number):
        results = [person for person in self.people_data if person.get('phone') == phone_number]
        if results:
            return {'success': True, 'data': {'results': results}}
        return {'success': False, 'message': 'No records found for the given phone number'}

    def download_public_record(self, *, unique_id, record_id, local_file_path):
        for person in self.people_data:
            if person.get('id') == unique_id:
                records = person.get('public_records', {})
                record = records.get(record_id)
                if record:
                    with open(local_file_path, 'w') as f:
                        f.write(record)
                    return {'success': True, 'message': 'Record downloaded successfully'}
        return {'success': False, 'message': 'Record not found'}
