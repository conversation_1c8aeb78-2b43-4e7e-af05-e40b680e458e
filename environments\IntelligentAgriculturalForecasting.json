[{"name": "generate_forecast", "description": "Utilize the forecasting tool by entering crop data and region.", "parameters": {"type": "object", "properties": {"crop_type": {"type": "string", "description": "Type of crop for which forecast is needed."}, "region": {"type": "string", "description": "Region for which yield forecast is required."}}, "required": ["crop_type", "region"]}}, {"name": "generate_yield_forecast", "description": "Generate a forecast for crop yield based on the given parameters.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_yield_parameters", "description": "Update parameters affecting crop yield forecasts.", "parameters": {"type": "object", "properties": {"parameter_name": {"type": "string", "description": "The name of the parameter to update, e.g., rainfall, soil_quality."}, "value": {"type": "number", "description": "The new value of the parameter."}}, "required": ["parameter_name", "value"]}}, {"name": "adjust_forecast_settings", "description": "Adjust the forecast settings for a crop.", "parameters": {"type": "object", "properties": {"crop_id": {"type": "string", "description": "The ID of the crop."}, "forecast_accuracy": {"type": "number", "description": "The revised forecast accuracy percentage."}}, "required": ["crop_id", "forecast_accuracy"]}}, {"name": "list_datasets", "description": "List all available datasets.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "delete_data", "description": "Delete specific datasets from the system.", "parameters": {"type": "object", "properties": {"dataset_ids": {"type": "array", "description": "The IDs of the datasets to be deleted.", "items": {"type": "string"}}}, "required": ["dataset_ids"]}}]