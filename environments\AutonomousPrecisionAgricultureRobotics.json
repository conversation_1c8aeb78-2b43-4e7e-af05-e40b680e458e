[{"name": "update_water_usage", "description": "Update the water usage for a given field.", "parameters": {"type": "object", "properties": {"field_id": {"type": "string", "description": "The id of the field to update water usage for."}, "new_water_usage": {"type": "integer", "description": "The new water usage value in liters."}}, "required": ["field_id", "new_water_usage"]}}, {"name": "fetch_water_usage_data", "description": "Retrieve the water usage data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "scan_zones", "description": "Scan and list all the zones within the farm for water allocation.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "allocate_water", "description": "Allocate a specific percentage of the total water supply to a specified zone.", "parameters": {"type": "object", "properties": {"zone_id": {"type": "string", "description": "The id of the zone for water allocation."}, "water_percent": {"type": "number", "description": "The percentage of total water supply to allocate to the zone."}}, "required": ["zone_id", "water_percent"]}}, {"name": "modify_robot_access", "description": "Modify robot control access rights for specified teams.", "parameters": {"type": "object", "properties": {"team": {"type": "string", "description": "The name of the team whose access rights should be modified."}, "access_rights": {"type": "array", "description": "The list of access rights to be set for the team.", "items": {"type": "string", "description": "A specific access right."}}}, "required": ["team", "access_rights"]}}, {"name": "get_current_robot_and_access_rights", "description": "Retrieve the current robot control access rights for all teams.", "parameters": {"type": "object", "properties": {}}, "required": []}]