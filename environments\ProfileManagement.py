from environments.BaseEnv import BaseEnv


class ProfileManagement(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def get_user_profile(self, *, profile_name):
        profiles = self.parameters.get('profiles', [])
        
        for profile in profiles:
            if profile.get('name') == profile_name:
                return {'success': True, 'data': profile}
        
        if len(profiles) == 1:
            return {'success': True, 'data': profiles[0]}
        
        return {'success': False, 'data': 'Profile not found.'}
    
