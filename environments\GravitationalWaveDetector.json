[{"name": "get_wave_detections", "description": "Access the latest gravitational wave detections and their properties.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "categorize_wave_detections", "description": "Sort the wave detections into appropriate categories for research access.", "parameters": {"type": "object", "properties": {"event_id": {"type": "string", "description": "The ID of the wave event to categorize."}, "category": {"type": "string", "description": "The category to assign to the wave detection."}}, "required": ["event_id", "category"]}}, {"name": "list_sensors", "description": "List all sensors in the gravitational wave detector.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_calibration", "description": "Adjust the calibration settings for a specific sensor.", "parameters": {"type": "object", "properties": {"sensor_id": {"type": "string", "description": "The ID of the sensor to adjust."}, "settings": {"type": "object", "description": "The new calibration settings.", "properties": {"laser_power": {"type": "string", "description": "The laser power setting."}, "frequency_stability": {"type": "string", "description": "The frequency stability setting."}, "alignment": {"type": "string", "description": "The alignment setting."}}, "required": ["laser_power", "frequency_stability", "alignment"]}}, "required": ["sensor_id", "settings"]}}, {"name": "list_data_entries", "description": "List all data entries available in the detector system.", "parameters": {"type": "object", "properties": {}}}, {"name": "delete_data_entry", "description": "Delete the specified data entry from the detector storage.", "parameters": {"type": "object", "properties": {"entry_id": {"type": "string", "description": "The ID of the data entry to be deleted."}}, "required": ["entry_id"]}}]