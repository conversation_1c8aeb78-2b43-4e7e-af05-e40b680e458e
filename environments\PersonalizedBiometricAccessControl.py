from environments.BaseEnv import BaseEnv

class PersonalizedBiometricAccessControl(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.user_data= self.parameters.get("users", {})
        
    def scan_fingerprint(self, user_id):
        users = self.parameters.get("users", [])
        for user_info in users:
            if user_info["user_id"] == user_id:
                return {"success": True, "user_info_with_fingerprint": user_info}
        
        return {"success": False, "message": "The user is not found."}

    def grant_access(self, user_id, file_names):
        return {"success": True}

    def list_access_permissions(self):
        users = self.parameters.get("users", [])
        pending_reqs = self.parameters.get("pending_requests", [])
        return {"success": True, "users": users, "pending_requests": pending_reqs}
    
    def update_access_settings(self, name, access_areas):
        return {"success": True, "message": "The access settings have been updated."}

    def get_user_biometric_data(self, user_id):
        if user_id in self.user_data:
            return {"success": True, "data": self.user_data[user_id]}
        
        return {"success": False, "message": "The user is not found."}
    
    def authenticate_user(self, user_id, biometric_id):
        if user_id in self.user_data:
            if self.user_data[user_id]["biometric_id"] == biometric_id:
                return {"success": True, "message": "The user has been authenticated."}
        
        return {"success": False, "message": "The user is not authenticated."}
    
    def generate_temporary_access_code(self, user_id):
        return {"success": True, "message": "The temporary access code has been generated."}