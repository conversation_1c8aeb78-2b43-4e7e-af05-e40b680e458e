from environments.BaseEnv import BaseEnv

class InfoRepository(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
    
    def access_web_sources(self, url):
        web_sources = self.parameters.get("web_sources", [])
        
        content = ""
        for web in web_sources:
            if web["url"] == url:
                content = web["summary"]
        
        return {"success": True, "sources": content}