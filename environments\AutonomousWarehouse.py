from environments.BaseEnv import BaseEnv

class AutonomousWarehouse(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.files = self.parameters.get('files', [])

    def check_inventory_orders(self):
        inventory = self.parameters.get('inventory', [])
        orders = self.parameters.get('orders', [])
        return {'success': True, 'data': {'inventory': inventory, 'orders': orders}}

    def restock_item(self, *, item_id, quantity):
        orders = self.parameters.get('orders', [])
        orders.append({"item_id": item_id, "order_quantity": quantity})
        self.parameters['orders'] = orders
        
        return {'success': True, 'data': 'Item restocked.'}
    
    def list_files(self):
        return {'success': True, 'files': self.files}
    
    def send_file(self, *, file_name):
        for file in self.files:
            if file['name'] == file_name:
                return {'success': True}
            
        return {'success': False, 'message': f'No file with name {file_name}.'}