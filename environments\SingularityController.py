from environments.BaseEnv import BaseEnv

class SingularityController(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def list_traffic_signals(self):
        traffic_signals = self.parameters.get("traffic_signals", [])
        return {"success": True, "traffic_signals": traffic_signals}
    
    def remove_traffic_signals(self, signal_id):
        return {"success": True, "message": "The traffic signal has been removed."}

    def get_current_alerts(self):
        alerts = self.parameters.get("alerts", [])
        return {"success": True, "alerts": alerts}
