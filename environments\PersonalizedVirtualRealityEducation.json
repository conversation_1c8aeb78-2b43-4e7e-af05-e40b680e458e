[{"name": "purchase_module", "description": "Purchase a specific educational module for a student.", "parameters": {"type": "object", "properties": {"module_id": {"type": "string", "description": "The unique identifier of the educational module to purchase."}, "account_name": {"type": "string", "description": "The name of the student purchasing the module."}}, "required": ["module_id", "account_name"]}}, {"name": "list_modules", "description": "List all available educational modules for purchase.", "parameters": {"type": "object", "properties": {}}, "required": []}, {"name": "get_student_account_info", "description": "Retrieve the account information of the student, including balance and payment method.", "parameters": {"type": "object", "properties": {}, "required": []}}]