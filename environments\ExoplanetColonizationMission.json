[{"name": "forward_message", "description": "Forwards some content to another crew member.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The forward content."}, "recipient_id": {"type": "string", "description": "The id of the crew member to forward the email to."}}, "required": ["content", "recipient_id"]}}, {"name": "list_internal_emails", "description": "List all internal emails.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_crew_members", "description": "List all crew members.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_power_status", "description": "List the operational status and reserve levels of backup power units.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_power_settings", "description": "Update reserve levels for backup power units.", "parameters": {"type": "object", "properties": {"unit_id": {"type": "string", "description": "The ID of the backup power unit."}, "reserve_level": {"type": "number", "description": "The new reserve level percentage."}}, "required": ["unit_id", "reserve_level"]}}, {"name": "list_habitats", "description": "List all habitats with their current resource supply levels and consumption status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_resource_allocation", "description": "Update the resource allocations for a habitat to balance supply and demand.", "parameters": {"type": "object", "properties": {"habitat_id": {"type": "string", "description": "The unique identifier for the habitat."}, "new_oxygen_supply": {"type": "string", "description": "The new oxygen supply level for the habitat."}, "new_water_supply": {"type": "string", "description": "The new water supply level for the habitat."}, "new_food_supply": {"type": "string", "description": "The new food supply level for the habitat."}}, "required": ["habitat_id", "new_oxygen_supply", "new_water_supply", "new_food_supply"]}}, {"name": "list_reports", "description": "List all available reports in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_landing_instructions", "description": "Retrieve the current landing instructions for the mission.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_landing_sites", "description": "List all available landing sites for the mission.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_landing_instructions", "description": "Update the landing instructions for the mission.", "parameters": {"type": "object", "properties": {"instructions": {"type": "string", "description": "The updated instructions for landing."}}, "required": ["instructions"]}}]