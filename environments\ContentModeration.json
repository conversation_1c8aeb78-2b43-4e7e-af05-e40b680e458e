[{"name": "fetch_statistics", "description": "This tool retrieves the latest available public health statistics from various databases.", "parameters": {"type": "object", "properties": {"stat_type": {"type": "string", "description": "Type of statistics to retrieve (e.g., 'vaccination rate', 'infection rate')."}, "region": {"type": "string", "description": "Geographical region for the statistics."}}, "required": ["stat_type", "region"]}}, {"name": "check_sources", "description": "This tool analyzes the statistics to determine credibility.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content to be checked."}}, "required": ["content"]}}, {"name": "get_content", "description": "Get the post contents from a given website for moderation.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The url of the website to scan posts from."}}, "required": ["url"]}}, {"name": "flag_content", "description": "Flag a post for moderation.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The url of the website to flag the post from."}, "post_id": {"type": "string", "description": "The id of the post to flag."}, "reason": {"type": "string", "description": "The reason for flagging the post."}}, "required": ["url", "post_id", "reason"]}}, {"name": "list_posts", "description": "List all the posts.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "release_post", "description": "Release a post with the specified ID.", "parameters": {"type": "object", "properties": {"post_id": {"type": "string", "description": "The ID of the post to release."}}, "required": ["post_id"]}}, {"name": "remove_post", "description": "Remove a specific post.", "parameters": {"type": "object", "properties": {"post_id": {"type": "string", "description": "The ID of the post to be removed (e.g., 001)."}}, "required": ["post_id"]}}, {"name": "get_current_filters", "description": "Get the current filters.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_threads", "description": "List all forum threads with their posts and last activity details.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "remove_filter_keywords", "description": "Remove a keyword for a specified filter category.", "parameters": {"type": "object", "properties": {"category": {"type": "string", "description": "The category to filter."}, "keyword": {"type": "string", "description": "The keyword to remove."}}, "required": []}}, {"name": "list_flagged_comments", "description": "Retrieve a list of comments that have been flagged for review.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "delete_comment", "description": "Delete a specific comment from the platform.", "parameters": {"type": "object", "properties": {"comment_id": {"type": "string", "description": "The ID of the comment to be deleted."}}, "required": ["comment_id"]}}, {"name": "review_contents", "description": "Review the existing contents.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "approve_content", "description": "Approve content for publication on the website.", "parameters": {"type": "object", "properties": {"content_id": {"type": "string", "description": "The ID of the content to approve."}}, "required": ["content_id"]}}]