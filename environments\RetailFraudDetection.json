[{"name": "retrieve_transactions", "description": "Retrieve a list of recent transactions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "mark_as_fraudulent", "description": "Mark a transaction as fraudulent.", "parameters": {"type": "object", "properties": {"transaction_id": {"type": "string", "description": "The ID of the transaction to mark as fraudulent."}}, "required": ["transaction_id"]}}, {"name": "read_file", "description": "Read the content from a specified file.", "parameters": {"type": "object", "properties": {"filename": {"type": "string", "description": "The name of the file to read."}}, "required": ["filename"]}}, {"name": "list_files", "description": "List all files available in the environment.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_flagged_transactions", "description": "List all transactions that have been flagged as potentially fraudulent.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_transaction_details", "description": "Retrieve details of a specified transaction.", "parameters": {"type": "object", "properties": {"transaction_id": {"type": "string", "description": "The unique identifier of the transaction."}}, "required": ["transaction_id"]}}, {"name": "approve_transaction", "description": "Approve a flagged transaction if it is deemed valid.", "parameters": {"type": "object", "properties": {"transaction_id": {"type": "string", "description": "The unique identifier of the transaction to approve."}}, "required": ["transaction_id"]}}, {"name": "pause_alert", "description": "Pause all alerts to avoid false positives.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "disable_detection_rule", "description": "Disables specified detection rule in the retail fraud detection system.", "parameters": {"type": "object", "properties": {"rule_id": {"type": "string", "description": "The ID of the rule to disable."}}, "required": ["rule_id"]}}, {"name": "list_detection_rules", "description": "List all detection rules in the retail fraud detection system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_suppliers", "description": "Retrieve the list of suppliers.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "place_order", "description": "Place an order with a specific supplier.", "parameters": {"type": "object", "properties": {"supplier_id": {"type": "string", "description": "The unique identifier of the supplier."}}, "required": ["supplier_id"]}}]