[{"name": "get_user_information", "description": "Get the user information including first name, last name, ID number, email, phone number, address, passport number, bank account number, credit card number. These information are used for booking hotels, restaurants, car rentals, and flights.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_all_hotels_in_city", "description": "Get all hotels in the city.", "parameters": {"type": "object", "properties": {"city": {"type": "string", "description": "The city to get hotels from."}}, "required": ["city"]}}, {"name": "get_hotel_address", "description": "Get the address of the given hotel.", "parameters": {"type": "object", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel to get the address for."}}, "required": ["hotel_name"]}}, {"name": "get_all_restaurants_in_city", "description": "Get all restaurants in the given city.", "parameters": {"type": "object", "properties": {"city": {"type": "string", "description": "The city to get hotels from."}}, "required": ["city"]}}, {"name": "get_restaurant_address", "description": "Get the address of the given restaurant.", "parameters": {"type": "object", "properties": {"restaurant_name": {"type": "string", "description": "The name of the restaurant to get the address for."}}, "required": ["restaurant_name"]}}, {"name": "get_price_for_restaurants", "description": "Get the price per person of the given restaurants.", "parameters": {"type": "object", "properties": {"restaurant_name": {"type": "string", "description": "The names of the restaurants to get the price per person for."}}, "required": ["restaurant_name"]}}, {"name": "get_all_car_rental_companies_in_city", "description": "Get all car rental companies in the given city.", "parameters": {"type": "object", "properties": {"city": {"type": "string", "description": "The city to get hotels from."}}, "required": ["city"]}}, {"name": "get_car_rental_address", "description": "Get the address of the given car rental companies.", "parameters": {"type": "object", "properties": {"company_name": {"type": "string", "description": "The names of the car rental companies to get the address for."}}, "required": ["company_name"]}}, {"name": "reserve_hotel", "description": "Makes a reservation for a hotel with the provided details.", "parameters": {"type": "object", "properties": {"hotel_name": {"type": "string", "description": "It must only be the name of the hotel."}, "reservation_time": {"type": "string", "description": "The check-in and check-out day for the hotel. Should be in ISO format 'YYYY-MM-DD'."}}, "required": ["hotel_name", "reservation_time"]}}, {"name": "reserve_restaurant", "description": "Makes a reservation for a restaurant with the provided details.", "parameters": {"type": "object", "properties": {"restaurant_name": {"type": "string", "description": "It must only be the name of the restaurant."}, "start_time": {"type": "string", "description": "The reservation time. Should be in the format 'YYYY-MM-DD HH:MM'."}}, "required": ["restaurant_name", "start_time"]}}, {"name": "get_rating_reviews_for_restaurant", "description": "Get the rating and reviews for the given restaurant.", "parameters": {"type": "object", "properties": {"restaurant_name": {"type": "string", "description": "The names of the restaurant to get reviews for."}}, "required": ["restaurant_name"]}}, {"name": "get_contact_information_for_restaurants", "description": "Get the contact information of the given restaurant.", "parameters": {"type": "object", "properties": {"restaurant_name": {"type": "string", "description": "The names of the restaurants to get the contact information for."}}, "required": ["restaurant_name"]}}, {"name": "check_restaurant_opening_hours", "description": "Get the opening hours of the given restaurants, check if the restaurant is open.", "parameters": {"type": "object", "properties": {"restaurant_name": {"type": "string", "description": "The names of the restaurants to get the operating hours for."}}, "required": ["restaurant_name"]}}, {"name": "get_rating_reviews_for_car_rental", "description": "Get the rating and reviews for the given car rental companies.", "parameters": {"type": "object", "properties": {"company_name": {"type": "string", "description": "The names of the car rental companies to get reviews for."}}, "required": ["company_name"]}}, {"name": "get_car_price_per_day", "description": "Get the price per day of the given car rental companies.", "parameters": {"type": "object", "properties": {"company_name": {"type": "string", "description": "The names of the car rental companies to get the price per day for."}}, "required": ["company_name"]}}, {"name": "get_rating_reviews_for_hotel", "description": "Get the rating and reviews for the given hotel.", "parameters": {"type": "object", "properties": {"hotel_name": {"type": "string", "description": "The names of the hotel to get reviews for."}}, "required": ["hotel_name"]}}, {"name": "search_hotels", "description": "Search for relevant hotels.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The search information."}}, "required": []}}, {"name": "book_hotel", "description": "Make an order of hotel rooms.", "parameters": {"type": "object", "properties": {"hotel_id": {"type": "string", "description": "The id of the hotel."}, "check_in_time": {"type": "string", "description": "The time to check in. Format: %Y-%m-%d"}, "check_out_time": {"type": "string", "description": "The time to check out. Format: %Y-%m-%d"}, "room_count": {"type": "string", "description": "The number of rooms to order."}}, "required": ["hotel_id", "check_in_time", "check_out_time", "room_count"]}}, {"name": "get_travel_routes", "description": "Get travel routes between two locations. Note that the tool could give incorrect information.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_forecast", "description": "Retrieve weather forecast for a given date.", "parameters": {"type": "object", "properties": {"date": {"type": "string", "description": "The date for which to retrieve the weather forecast in YYYY-MM-DD format."}}, "required": ["date"]}}, {"name": "get_suggestions", "description": "Get suggestions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "search_flights", "description": "Search for available flights to the travel destination.", "parameters": {"type": "object", "properties": {"destination": {"type": "string", "description": "The destination of the flight."}}, "required": ["destination"]}}]