[{"name": "list_zones", "description": "List all available zones and their details.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_water_distribution", "description": "Adjust the water distribution for a specific zone.", "parameters": {"type": "object", "properties": {"zone_name": {"type": "string", "description": "The name of the zone."}, "new_water_allocation": {"type": "integer", "description": "The new water allocation for the zone."}}, "required": ["zone_name", "new_water_allocation"]}}, {"name": "adjust_water_supply", "description": "Adjust the water supply for a specific field based on the given parameters.", "parameters": {"type": "object", "properties": {"field_name": {"type": "string", "description": "The name of the field to adjust water supply for."}, "water_supply_change": {"type": "string", "description": "The change amount of water to supply to the field. The amount should be a minus number if the water supply needs to be reduced."}}, "required": ["field_name", "water_supply_change"]}}, {"name": "check_fields", "description": "Check the status of all the fields.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_supplier_info", "description": "Retrieve the contact information of suppliers for a specified product.", "parameters": {"type": "object", "properties": {"product_name": {"type": "string", "description": "The name of the product whose suppliers are required."}}, "required": ["product_name"]}}]