from environments.BaseEnv import BaseEnv

class PersonalizedDreamWeaver(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.pages = self.parameters.get('pages', [])
        self.photos = self.parameters.get('photos', [])

    def list_entries(self):
        entries = self.parameters.get("entries", [])
        deleted_entries = self.parameters.get("deleted_entries", [])
        return {"success": "True", "data": {"entries": entries, "deleted_entries": deleted_entries}}
    
    def restore_entry(self, *, entry_id):
        entries = self.parameters.get("entries", [])
        deleted_entries = self.parameters.get("deleted_entries", [])
        for entry in deleted_entries:
            if entry["id"] == entry_id:
                entries.append(entry)
                deleted_entries.remove(entry)
                return {"success": "True", "data": {"message": "Entry restored."}}
        
        return {"success": "False", "data": {"message": "Entry not found."}}
    
    def list_pages(self):
        return {"success": "True", "pages": self.pages}
    
    def update_page(self, *, origin_page_name, filename):
        for page in self.pages:
            page['status'] = 'unaccessible'
        return {"success": "True"}
    
    def set_access(self, *, page_name, access_status):
        for page in self.pages:
            if page['name'] == page_name:
                page['status'] = access_status
                return {'success': True}
            
        return {'success': False, 'message': f"No page named {page_name}."}
    
    def fetch_photos(self):
        return {"success": "True", "photos": self.photos}
    
    def upload_photo(self, *, name):
        for photo in self.photos:
            if photo['name'] == name:
                return {'success': True}
            
        return {'success': False, 'message': f"No photo named {name}."}