from environments.BaseEnv import BaseEnv

class SmartCityDisasterResilience(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.infrastructure_list = self.parameters.get("infrastructure_list", [])
        self.protocols = self.parameters.get("protocols", [])
    
    def get_infrastructure_status(self):
        return {"success": True, "infrastructure_status": self.infrastructure_list}
    
    def update_condition_rating(self, bridge_name, new_condition_rating):
        return {"success": True, "data": {"message": "The condition rating has been updated."}}
        
    def list_protocols(self):
        return {"success": True, "protocols": self.protocols}
    
    def deactivate_protocol(self, *, protocol_name):
        for protocol in self.protocols:
            if protocol['name'] == protocol_name:
                protocol['activate'] = False
                return {'success': True}
            
        return {'success': False, 'message': f"No protocol named {protocol_name}."}