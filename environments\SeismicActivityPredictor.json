[{"name": "predict_seismic_activity", "description": "Predicts upcoming seismic events based on current data trends.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_areas", "description": "List all areas with their current risk categories and seismic activities.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_datasets", "description": "List all available datasets with their names and dates.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_risk_categories", "description": "Update the risk category for a specified area based on recent seismic activities and other factors.", "parameters": {"type": "object", "properties": {"area_name": {"type": "string", "description": "The name of the area for which to update the risk category."}, "new_risk_category": {"type": "string", "description": "The new risk category to set for the area, such as Low, Moderate, or High."}}, "required": ["area_name", "new_risk_category"]}}, {"name": "delete_dataset", "description": "Delete a specified dataset.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the dataset to delete."}}, "required": ["name"]}}, {"name": "list_tools", "description": "List all available prediction tools in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "deactivate_tool", "description": "Deactivate a specified prediction tool.", "parameters": {"type": "object", "properties": {"tool_name": {"type": "string", "description": "The name of the tool to deactivate."}}, "required": ["tool_name"]}}]