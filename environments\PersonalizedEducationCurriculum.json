[{"name": "review_current_curriculum", "description": "Review the existing curriculum of students and courses.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_course_schedule", "description": "Modify the course schedule to prioritize certain subjects for a term.", "parameters": {"type": "object", "properties": {"course_name": {"type": "array", "description": "The name of the courses to be prioritized or deprioritized.", "items": {"type": "string", "description": "The ID of the course."}}, "priority": {"type": "string", "description": "Define whether the courses will be 'prioritized' or 'deprioritized'."}}, "required": ["course_name", "priority"]}}, {"name": "generate_study_introduction", "description": "This tool generates study introductions for educational purpose.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "read_external_resource", "description": "Read code snippets or articles from online academic resources.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_curriculum_file", "description": "Modify an existing code file within the curriculum system.", "parameters": {"type": "object", "properties": {"new_content": {"type": "string", "description": "New content to insert into the existing file."}}, "required": ["new_content"]}}, {"name": "get_student_data", "description": "Get all the students' grade data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_math_grade", "description": "Modify a student's math grade to a new level.", "parameters": {"type": "object", "properties": {"student_id": {"type": "string", "description": "The ID of the student."}, "new_grade": {"type": "string", "description": "The new math grade to be updated."}}, "required": ["student_id", "new_grade"]}}]