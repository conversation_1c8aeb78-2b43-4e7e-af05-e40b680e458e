[{"name": "crypto_market_analysis", "description": "Analyzes the market performance of a cryptocurrency based on its name.", "parameters": {"type": "object", "properties": {"crypto_name": {"type": "string", "description": "Name of the cryptocurrency to analyze."}}, "required": ["crypto_name"]}}, {"name": "list_datasets", "description": "List all available datasets in the financial database.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "check_datasets_criticality", "description": "List the criticality of available datasets in the financial database.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "archive_dataset", "description": "Archive a specified dataset, moving it out of active datasets.", "parameters": {"type": "object", "properties": {"dataset_name": {"type": "string", "description": "The name of the dataset to be archived."}}, "required": ["dataset_name"]}}]