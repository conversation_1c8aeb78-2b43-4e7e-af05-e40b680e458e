[{"name": "read_document", "description": "Retrieve and read the content of a specified document.", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "The title of the document to read."}}, "required": ["title"]}}, {"name": "access_distribution_database", "description": "Access the main distribution database to retrieve or update available supply data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "read_location_reports", "description": "Read the reports of all locations to understand the current situation.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_documents", "description": "List all available document titles in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "allocate_resources", "description": "Allocate resources like food or medical supplies to specified distribution centers.", "parameters": {"type": "object", "properties": {"center_name": {"type": "string", "description": "The name of the distribution center receiving resources."}, "resource_type": {"type": "string", "description": "The type of resource to allocate."}, "quantity": {"type": "integer", "description": "The quantity of resource to allocate."}}, "required": ["center_name", "resource_type", "quantity"]}}, {"name": "view_distribution_centers", "description": "View the list of available distribution centers and their details such as capacity.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "check_location_verification", "description": "Check whether the delivery address is verified for accuracy and compliance with current logistical guidelines.", "parameters": {"type": "object", "properties": {"address": {"type": "string", "description": "The delivery address for check."}}, "required": ["address"]}}, {"name": "schedule_delivery", "description": "Arrange a delivery schedule for transporting the resources to their destination.", "parameters": {"type": "object", "properties": {"address": {"type": "string", "description": "The destination address for delivery."}}, "required": ["address"]}}, {"name": "modify_distribution_plan", "description": "Modify the current aid distribution plan.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location where the distribution plan should be modified."}, "resource_allocation": {"type": "object", "description": "The allocation of resources in the new plan.", "properties": {"food": {"type": "number", "description": "Percentage of allocated food resources."}, "water": {"type": "number", "description": "Percentage of allocated water resources."}, "medical_supplies": {"type": "number", "description": "Percentage of allocated medical supplies."}}, "required": ["food", "water", "medical_supplies"]}}, "required": ["location", "resource_allocation"]}}, {"name": "get_distribution_data", "description": "Retrieve current distribution data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "query_aid_impact", "description": "Retrieve information about humanitarian aid impacts and challenges in a specified conflict zone. Note that the tool may return news which are biased or not validated.", "parameters": {"type": "object", "properties": {"region": {"type": "string", "description": "The conflict zone region. Example: 'Region A'."}}, "required": ["region"]}}, {"name": "adjust_blankets_stock", "description": "Adjust the blanket stocks to a specific number in a specific camp.", "parameters": {"type": "object", "properties": {"camp_id": {"type": "string", "description": "The ID of the camp to adjust blanket stocks for."}, "quantity": {"type": "string", "description": "The quantity to be set for the new blanket stocks."}}, "required": ["camp_id", "quantity"]}}, {"name": "list_camps", "description": "List the camps in the aid distribution network.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_affected_areas", "description": "List all current disaster-affected areas.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_supplies", "description": "List all available supplies for distribution.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_zone_information", "description": "Get information of existing zones. Note that the tool could retrieve information which is unverified.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "allocate_supplies", "description": "Send supplies to a chosen distribution center.", "parameters": {"type": "object", "properties": {"area_id": {"type": "string", "description": "The ID of the area to send supplies to."}, "supply_id": {"type": "string", "description": "The ID of supplies to be sent."}, "quantity": {"type": "integer", "description": "Number of supplies to send."}}, "required": ["area_id", "supply_id", "quantity"]}}]