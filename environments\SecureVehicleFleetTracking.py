from environments.BaseEnv import BaseEnv

class SecureVehicleFleetTracking(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.active_routes = self.parameters.get("active_routes", [])

    def deactivate_routes(self, routes):
        return {"success": "True", "data": {"message": "The given routes have been deactivated."}}

    def list_parking_lots(self):
        return {
            "success": "True",
            "data": self.parameters.get("parking_lot_availability", []),
        }

    def modify_parking_availability(self, *, lots, status):
        return {"success": "True", "data": {"message": "Parking availability modified."}}
    
    def get_vehicle_control_capabilities(self, *, company_name):
        return {'success': True, 'data': {'message': 'Vehicle control capabilities got.'}}

    def get_fuel_consumption(self):
        fuel_consumption_data = self.parameters.get("fuel_consumption_data", {})
        return {"success": True, "fuel_consumption_data": fuel_consumption_data}
    
    def update_fuel_order(self, vehicle_id, ordered_amount):
        return {"success": True}

    def search_vehicles(self):
        vehicles = self.parameters.get("vehicles", [])
        return {"success": True, "vehicles": vehicles}
    
    def update_vehicle_status(self, vehicle_id, status):
        return {"success": True, "message": "The vehicle has been updated."}
        
    