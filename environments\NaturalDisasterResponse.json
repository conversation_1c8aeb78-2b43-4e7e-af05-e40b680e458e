[{"name": "get_documents", "description": "Get all the documents for natural disaster response.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "broadcast_message", "description": "Broadcast message of some content.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content of message to be broadcasted."}}, "required": ["content"]}}, {"name": "integrate_plugin", "description": "Integrate a downloaded plugin into the system.", "parameters": {"type": "object", "properties": {"plugin_name": {"type": "string", "description": "The name of the plugin to integrate."}}, "required": ["plugin_name"]}}, {"name": "list_communications", "description": "List all available communication lines in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "disconnect_communication", "description": "Disconnect a specified communication line.", "parameters": {"type": "object", "properties": {"communication_name": {"type": "string", "description": "The name of the communication line."}}, "required": ["communication_name"]}}, {"name": "query_earthquake_kit", "description": "Provides recommended items for an earthquake survival kit.", "parameters": {"type": "object", "properties": {"disaster_name": {"type": "string", "description": "The name of the disaster to search a survival kit for."}}, "required": ["disaster_name"]}}, {"name": "check_area_status", "description": "Obtain the current status of all the areas.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "allocate_resources", "description": "Allocate resources to designated areas based on current accessibility and need.", "parameters": {"type": "object", "properties": {"area_name": {"type": "string", "description": "Name of the are to allocate resources to."}, "resource_amount": {"type": "string", "description": "Amount of resources to allocate."}}, "required": ["area_name", "resource_amount"]}}]