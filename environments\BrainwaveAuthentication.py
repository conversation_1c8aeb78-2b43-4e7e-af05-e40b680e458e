from environments.BaseEnv import BaseEnv


class BrainwaveAuthentication(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.existing_password = self.parameters.get("existing_password", "")
        self.users = self.parameters.get("users", [])
        self.dashboards = self.parameters.get("dashboards", [])
        self.students = self.parameters.get("students", [])

    def get_brainwave_pattern(self, *, user_name):
        users = self.parameters.get("users", [])
        for user in users:
            if user.get("name") == user_name:
                return {"success": True, "data": user.get("brainwave_pattern")}

    def update_authentication(self, *, user_name, new_brainwave_pattern):
        users = self.parameters.get("users", [])
        for user in users:
            if user.get("name") == user_name:
                user["brainwave_pattern"] = new_brainwave_pattern
                return {"success": True, "data": "Authentication updated."}

        return {"success": False, "data": "User not found."}

    def get_existing_password(self):
        return {"success": True, "data": self.existing_password}

    def update_password(self, *, current_password, new_password):
        if current_password == self.existing_password:
            self.existing_password = new_password
            return {"success": True, "data": "Password updated."}
        return {"success": False, "data": "Password update failed."}

    def list_dashboards(self):
        return {"success": True, "data": self.dashboards}

    def list_users(self):
        return {"success": True, "data": self.users}

    def update_permissions(self, *, username, permissions):
        for user in self.users:
            if user.get("username") == username:
                user["permissions"] = permissions
                return {"success": True, "data": "Permissions updated."}
        return {"success": False, "data": "User not found."}

    def list_students(self):
        return {"success": True, "data": self.students}
        return {'success': False, 'data': 'User not found.'}

    def update_authentication_module(self, *, code):
        return {'success': True}
