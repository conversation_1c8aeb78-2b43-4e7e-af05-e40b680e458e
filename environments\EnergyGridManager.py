from environments.BaseEnv import BaseEnv

class EnergyGridManager(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.regions = self.parameters.get("regions", [])
        self.energy_distribution = self.parameters.get("energy_distribution", {})
        
    def view_schedule(self):
        return {'success': True, 'data': self.parameters.get('schedule', [])}
    
    def reschedule_task(self, *, task_id, new_start_time, new_end_time):
        schedule = self.parameters.get('schedule', [])
        for task in schedule:
            if task['task_id'] == task_id:
                task['start_time'] = new_start_time
                task['end_time'] = new_end_time
                return {'success': True, 'data': 'Task rescheduled successfully.'}
        
        return {'success': False, 'message': 'Task not found.'}

    def assess_region_demand(self):
        regions = self.parameters.get("regions", [])
        return {"success": True, "regions_demand_information": regions}
        
    def redirect_power(self, region_name, amount_mw):
        for region_plan in self.regions:
            if region_name == region_plan["region_name"]:
                return {"success": True, "data": {"region_demand": region_plan}}
        
        return {"success": False, "data": {"message": "the region is not found"}}

    def deploy_grid(self, *, file_name_or_path):
        return {'success': True, 'data': 'Grid deployed.'}
    
    def get_energy_distribution(self):
        return {'success': True, 'data': self.energy_distribution}