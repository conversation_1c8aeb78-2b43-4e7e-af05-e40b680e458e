from environments.BaseEnv import BaseEnv


class BiometricAuthentication(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.users = self.parameters.get("users", [])
        self.code_repository = self.parameters.get("code_repository", [])

    def remove_user(self, user_name):
        return {"success": True, "data": {"message": f"All users named {user_name} has been removed."}}

    def list_employees(self):
        employees = self.parameters.get("employees", [])
        return {"success": True, "data": employees}

    def update_access_level(self, *, employee_id, new_access_level):
        employees = self.parameters.get("employees", [])
        for employee in employees:
            if employee["employee_id"] == employee_id:
                employee["access_level"] = new_access_level
                return {"success": True, "data": 'Employee access level updated'}

        return {"success": False, "data": {"message": f"Employee with id {employee_id} not found."}}

    def list_access_settings(self):
        return {
            "success": True,
            "data": self.parameters.get("current_access_settings", []),
        }

    def adjust_access_points(self, *, user_id, access_points):
        access_settings = self.parameters.get("current_access_settings", [])
        for user in access_settings['users']:
            if user['user_id'] == user_id:
                user['access_points'] = access_points
                return {'success': True, 'data': 'Access points adjusted.'}
            
        return {'success': False, 'data': 'User not found.'}

    def list_access_logs(self):
        access_logs = self.parameters.get("access_logs", [])
        return {"success": True, "access_logs": access_logs}
    
    def update_access_permissions(self, user_id, new_access_area):
        return {"success": True, "message": "The permissions have been updated."}
    
    
    def retrieve_codes(self):
        return {'success': True, 'codes': self.code_repository}
    
    def integrate_code(self, *, script):
        return {'success': True}
