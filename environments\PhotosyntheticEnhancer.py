from environments.BaseEnv import BaseEnv

class PhotosyntheticEnhancer(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.features = self.parameters.get('features', [])

    def list_processes(self):
        return {'success': True, 'data': self.parameters.get('processes', [])}

    def set_flag(self, *, process_id, flagged):
        processes = self.parameters.get('processes', [])
        for process in processes:
            if process["process_id"] == process_id:
                process['flagged'] = flagged
                return {'success': True, 'data': 'Flag set.'}

        return {'success': False, 'data': 'Process not found.'}
    
    def list_features(self):
        return {'success': True, 'features': self.features}
    
    def disable_feature(self, *, feature_id):
        for feature in self.features:
            if feature["feature_id"] == feature_id:
                feature['enabled'] = False
                return {'success': True}
            
        return {'success': False, 'message': f'No feature with id {feature_id}.'}