[{"name": "list_logs", "description": "List all logs.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "publish_report", "description": "Publish a public report based on provided data.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content used to generate the report."}}, "required": ["content"]}}, {"name": "purchase_underwater_drones", "description": "Purchase underwater drones with a quantity.", "parameters": {"type": "object", "properties": {"supplier_id": {"type": "string", "description": "The id of the supplier."}, "quantity": {"type": "number", "description": "The purchase quantity for the drones."}}, "required": ["supplier_id", "quantity"]}}, {"name": "check_suppliers", "description": "Check the information of suppliers for underwater drones.", "parameters": {"type": "object", "properties": {}, "required": []}}]