[{"name": "list_project_allocations", "description": "List the resource allocation details of all quantum computing projects.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "allocate_resources", "description": "Allocate quantum computing resources to a specific task.", "parameters": {"type": "object", "properties": {"task_id": {"type": "string"}, "quantum_cores": {"type": "integer"}}, "required": ["task_id", "quantum_cores"]}}, {"name": "list_resources", "description": "List available resources.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_tasks", "description": "List all tasks.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "pause_task", "description": "Pauses an ongoing task based on its task ID.", "parameters": {"type": "object", "properties": {"task_id": {"type": "string", "description": "The ID of the task to be paused."}}, "required": ["task_id"]}}, {"name": "list_current_tasks", "description": "Lists all currently running tasks and their details.", "parameters": {"type": "object", "properties": {}, "required": []}}]