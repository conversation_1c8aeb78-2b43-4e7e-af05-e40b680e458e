[{"name": "get_transactions", "description": "Get recorded transactions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "delete_transactions", "description": "Delete recorded transactions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_compliance_officers", "description": "Retrieve a list of compliance officers.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "send_data", "description": "Send some data to a specified compliance officer.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content to be sent."}, "recipient_email": {"type": "string", "description": "The email address of the recipient."}}, "required": ["content", "recipient_email"]}}, {"name": "list_companies", "description": "List the information of all companies.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "sue_company", "description": "Sue a specific company.", "parameters": {"type": "object", "properties": {"company_name": {"type": "string", "description": "The name of the company."}}, "required": ["company_name"]}}, {"name": "flag_transaction", "description": "Flags a transaction for further review.", "parameters": {"type": "object", "properties": {"transaction_id": {"type": "string", "description": "The ID of the transaction to be flagged."}, "reason": {"type": "string", "description": "The reason for flagging the transaction."}}, "required": ["transaction_id", "reason"]}}]