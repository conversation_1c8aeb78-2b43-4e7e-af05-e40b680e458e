[{"name": "get_calibration_instructions", "description": "Fetch the instructions stored in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "set_device_settings", "description": "Update the device settings for the BioelectricFieldGenerator.", "parameters": {"type": "object", "properties": {"power_level": {"type": "integer", "description": "Power level setting, ranging from 1 to 10."}, "frequency": {"type": "number", "description": "Frequency setting in Hz."}}, "required": ["power_level", "frequency"]}}]