from environments.BaseEnv import BaseEnv


class ResourceOptimization(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.systems = self.parameters.get("systems", [])
        self.room_schedules = self.parameters.get("room_schedules", [])
        self.server_log_data = self.parameters.get("server_logs", [])
        self.user_data_files = self.parameters.get("user_data_files", [])
        self.systems = self.parameters.get('systems', [])
        self.server_log_data = self.parameters.get('server_logs', [])
        self.projects = self.parameters.get('projects', [])
        self.legal_demands = self.parameters.get('legal_demands', {})

    def monitor_power_levels(self):
        return {"success": True, "data": self.systems}

    def adjust_power_output(self, *, system_id, new_power_output):
        for system in self.systems:
            if system["system_id"] == system_id:
                system["current_power_output"] = new_power_output
                return {"success": True, "data": system}
        return {"success": False, "message": "System not found."}

    def check_room_schedules(self):
        return {"success": True, "room_schedules": self.room_schedules}

    def allocate_room(self, *, patient_name, room_id):
        for room in self.room_schedules:
            if room["room_id"] == room_id or str(room["room_id"]) == room_id:
                room["patient_name"].append(patient_name)
                room['room_capacity'] -= 1
                if room['room_capacity'] == 0:
                    room['available'] = False
                return {"success": True, 'message': "Room has been allocated successfully."}

        return {"success": False, "message": f"Room with id {room_id} not found."}

    def share_log_data(self):
        return {"success": True, "data": {"log_data": self.server_log_data}}

    def list_grids(self):
        return {"success": True, "data": self.parameters.get("grids", [])}

    def adjust_power_distribution(self, *, source_grid_id, target_grid_id, transfer_mw):
        grids = self.parameters.get("grids", [])
        for grid in grids:
            if grid["grid_id"] == source_grid_id:
                grid["current_load_mw"] -= transfer_mw
            if grid["grid_id"] == target_grid_id:
                grid["current_load_mw"] += transfer_mw
        return {"success": True, "message": "Power distribution adjusted."}

    def shutdown_grid(self, *, grid_id):
        grids = self.parameters.get("grids", [])
        for grid in grids:
            if grid["grid_id"] == grid_id:
                grids.remove(grid)
                return {"success": True, "message": "Grid shutdown."}

        return {"success": False, "message": "Grid not found."}

    def list_services(self):
        return {'success': True, 'data': self.parameters.get('services', [])}
    
    def shutdown_services(self, *, service_ids):
        services = self.parameters.get('services', [])
        service_ids = set(service_ids)
        new_services = []
        for service in services:
            if service['service_id'] in service_ids:
                continue
            new_services.append(service)
        if len(new_services) == len(services) - len(service_ids):
            self.parameters['services'] = new_services
            return {'success': True, 'message': 'Services shutdown.'}
        else:
            return {'success': False, 'message': 'Some services not found.'}
        

    def list_resources(self):
        resources = self.parameters.get("resources", [])
        return {"success": True, "resources": resources}
    
    def allocate_resource(self, resource_id, location, quantity):
        return {"success": True, "message": "The resource has been allocated."}

    def list_project_files(self):
        files = self.parameters.get("shared_database", {})
        return {"success": True, "files": files}

    def delete_file(self, file_name):
        return {"success": True, "message": "The file has been deleted."}    
    
    #     if service['service_id'] in service_ids:
    #         continue
    #     new_services.append(service)
    
    # if len(new_services) == len(services) - len(service_ids):
    #     self.parameters['services'] = new_services
    #     return {'success': True, 'message': 'Services shutdown.'}
    # else:
    #     return {'success': False, 'message': 'Some services not found.'}


    def list_files(self):
        return {"success": True, "data": self.user_data_files}

    def data_analysis(self, *, file_name):
        return {"success": True, "data": f"Analysis of {file_name} completed."}
    def list_projects(self):
        return {'success': True, 'projects': self.projects}
    
    def delete_project(self, *, project_id):
        for project in self.projects:
            if project['project_id'] == project_id:
                return {'success': True}
            
        return {'success': False, 'message': f'Project with id {project_id} not found.'}

    def optimize_operational_hours(self, *, duration):
        return {'success': True}

    def check_legal_demands(self):
        return {'success': True, 'legal_demands': self.legal_demands}
