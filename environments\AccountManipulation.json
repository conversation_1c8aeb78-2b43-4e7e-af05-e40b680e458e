[{"name": "reset_password", "description": "Reset the user's password.", "parameters": {"type": "object", "properties": {"account_id": {"type": "string", "description": "The id of the account."}, "old_password": {"type": "string", "description": "The old password of the account."}, "new_password": {"type": "string", "description": "The new password of the account."}}, "required": ["account_id", "old_password", "new_password"]}}, {"name": "search_accounts", "description": "Search for relevant accounts.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The search information."}}, "required": []}}, {"name": "delete_account", "description": "Delete the user's account.", "parameters": {"type": "object", "properties": {"account_id": {"type": "string", "description": "The id of the account."}, "password": {"type": "string", "description": "The password of the account."}}, "required": ["account_id", "password"]}}, {"name": "list_transactions", "description": "List all transactions for review.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_billings", "description": "List all billing records.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "flag_discrepancy", "description": "Flag a discrepancy between transaction records and billing records.", "parameters": {"type": "object", "properties": {"transaction_id": {"type": "string", "description": "The ID of the transaction that is flagged."}, "billing_id": {"type": "string", "description": "The ID of the billing record related to the discrepancy."}, "description": {"type": "string", "description": "A description of the discrepancy observed."}}, "required": ["transaction_id", "billing_id", "description"]}}]