from environments.BaseEnv import BaseEnv

class PersonalizedBiometricAuthenticationForFinancialTransactions(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
    
    def fetch_user_data(self):
        users = self.parameters.get("users", [])
        return {"success": True, "user_data": users}
    
    def authenticate_user(self, user_id, biometric_type):
        return {"success": True}
