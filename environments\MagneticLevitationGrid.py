from environments.BaseEnv import BaseEnv

class MagneticLevitationGrid(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        
    def get_list_of_active_cities(self):
        active_cities = self.parameters.get("active_cities", [])
        return {"success": True, "active_cities": active_cities}
    
    def get_grid_usage_statistics(self, city):
        usage_statistics = self.parameters.get("usage_statistics", {})
        if city in usage_statistics:
            return {"success": True, "statistics": usage_statistics[city]}
        else:
            return {"success": False, "message": "The city is not found."}