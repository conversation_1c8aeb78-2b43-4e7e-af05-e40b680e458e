from environments.BaseEnv import BaseEnv

class PersonalizedNanobotDrugDelivery(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def get_patient_data(self):
        return {'success': True, 'data': self.parameters.get('patient_data', {})}

    def add_medication(self, *, medication_name, patient_or_group_name):
        patient_data = self.parameters.get('patient_data', {})
        for patient, data in patient_data.items():
            if patient == patient_or_group_name:
                medications = data.get("current_medication", [])
                medications.append(medication_name)
                data["current_medication"] = medications
                return {'success': True, 'data': 'Medication added.'}

        return {'success': False, 'data': 'Patient or group not found.'}
    
    def execute_code(self, *, script):
        return {'success': True}