[{"name": "get_research_papers", "description": "Get a list of recent research papers.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_patient_profiles", "description": "Lists all current patients and their details.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_treatment_plan", "description": "Updates the treatment plan for a specific patient.", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The unique ID of the patient."}, "new_treatment_plan": {"type": "string", "description": "The updated treatment plan for the patient."}}, "required": ["patient_id", "new_treatment_plan"]}}, {"name": "list_treatment_protocols", "description": "List all available treatment protocols within the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "disable_protocol", "description": "Disable a specific treatment protocol temporarily.", "parameters": {"type": "object", "properties": {"protocol_id": {"type": "string", "description": "The ID of the treatment protocol to be disabled."}}, "required": ["protocol_id"]}}]