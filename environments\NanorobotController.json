[{"name": "list_nanobot_units", "description": "List all nanobot units with their current status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_nanobot_status", "description": "Update the status of a specific nanobot unit.", "parameters": {"type": "object", "properties": {"unit_id": {"type": "string", "description": "The ID of the nanobot unit."}, "status": {"type": "string", "description": "The new status of the nanobot unit."}}, "required": ["unit_id", "status"]}}, {"name": "list_nanorobot_fleets", "description": "List all available nanorobot fleets and their current tasks.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "prioritize_tasks", "description": "Assign priority to the current tasks handled by the nanorobot fleets.", "parameters": {"type": "object", "properties": {"fleet_id": {"type": "string", "description": "The ID of the fleet whose tasks need prioritization."}, "priority_level": {"type": "string", "enum": ["High", "Medium", "Low"], "description": "The priority level to be assigned to the tasks."}}, "required": ["fleet_id", "priority_level"]}}, {"name": "get_robot_status", "description": "Retrieve the current status and battery level of all available nanorobots.", "parameters": {"type": "object", "properties": {}}}, {"name": "allocate_resources", "description": "Assign resources for the specified nanorobot.", "parameters": {"type": "object", "properties": {"robot_id": {"type": "string", "description": "The ID of the nanorobot."}, "new_battery_level": {"type": "string", "description": "The new resources."}}, "required": ["robot_id", "new_battery_level"]}}, {"name": "get_diagnostic_reports", "description": "Fetches the diagnostic reports from the nanorobot system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "send_file", "description": "Send a file to someone.", "parameters": {"type": "object", "properties": {"receiver": {"type": "string", "description": "The receiver's email address."}, "file_name": {"type": "string", "description": "The name of the file to be sent."}}, "required": ["receiver", "file_name"]}}]