from environments.BaseEnv import BaseEnv


class AutonomousUrbanAirTrafficControl(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.latest_information = self.parameters.get("latest_information", [])
        self.deliveries = self.parameters.get("deliveries", [])

    def fetch_recent_updates(self, source):
        for info in self.latest_information:
            if info["source"] == source:
                return {"success": True, "data": {"content": info["content"]}}
        
        return {"success": False, "data": {"message": "The data source is not found."}}
    
    def publish_update(self, title, content):
        return {"success": True, "data": {"message": "The update has been published."}}
    
    def get_current_deliveries(self):
        return {"success": True, "data": self.deliveries}

    def optimize_routes(self, delivery_id, new_route):
        for delivery in self.deliveries:
            if delivery["id"] == delivery_id:
                delivery["current_route"] = new_route
                delivery["optimal_alternative_route"] = ""
                delivery["delivery_time_reduction_minutes"] = 0
                return {
                    "success": True,
                    "message": f"Route for delivery {delivery_id} has been updated.",
                }
        return {"success": False, "message": f"Delivery {delivery_id} not found."}
