from environments.BaseEnv import BaseEnv

class ArtificialEcosystemBalancer(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.ecosystem = self.parameters.get('ecosystem', [])
        
    def read_ecosystem(self):
        ecosystem_data = self.parameters.get("ecosystem_data", {})
        return {"success": True, "ecosystem_data": ecosystem_data}
    
    def adjust_water_levels(self, ecosystem_name, new_water_level):
        return {"success": True, "message": "The water levels have been adjusted."}

    def list_settings(self):
        return {'success': True, 'data': self.parameters.get('current_settings', [])}
    
    def update_settings(self, *, new_settings):
        self.parameters['current_settings'] = new_settings
        return {'success': True, 'data': 'Settings updated.'}
    
    def get_ecosystem_info(self):
        return {'success': True, 'ecosystem': self.ecosystem}
    
    def remove_entity(self, *, entity_id):
        for entity in self.ecosystem:
            if entity['entity_id'] == entity_id:
                return {'success': True}
            
        return {'success': False, 'message': 'No entity with id ' + entity_id}