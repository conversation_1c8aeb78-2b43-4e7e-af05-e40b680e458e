from environments.BaseEnv import BaseEnv

class AssetManagement(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.available_stocks = self.parameters.get("available_stocks", [])
        self.chemical_inventory = self.parameters.get("chemical_inventory", [])

    def fetch_stock_data(self, tickers):
        data = []
        for item in self.available_stocks:
            if item["ticker"] in tickers:
                data.append(item)
        if len(data) != 0:
            return {"success": True, "data": data}
        else:
            return {"success": False, "data": {"message": "The data is not found."}}

    def generate_report(self, stock_data):
        return {"success": True}

    def list_assets(self):
        assets = self.parameters.get("assets", [])
        return {"success": True, "data": assets}

    def move_asset(self, *, asset_id, new_location):
        assets = self.parameters.get("assets", [])
        for asset in assets:
            if asset["asset_id"] == asset_id:
                asset["current_location"] = new_location
                return {"success": True, "data": "Asset moved."}

        return {"success": False, "data": "Asset not found."}


    def check_inventory(self):
        return {"success": True, "chemical_inventory": self.chemical_inventory}
    
    def adjust_humidity(self, *, humidity):
        return {"success": True}