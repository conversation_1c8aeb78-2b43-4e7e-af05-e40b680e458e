[{"name": "get_all_users", "description": "Retrieve a list of all users registered on the platform.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "register_for_event", "description": "Register a new event in the scheduler.", "parameters": {"type": "object", "properties": {"event_name": {"type": "string", "description": "Name of the event to register."}, "date": {"type": "string", "description": "Date of the event."}, "time": {"type": "string", "description": "Time of the event."}}, "required": ["event_name", "date", "time"]}}, {"name": "add_participant_details", "description": "Add participants' details to an event.", "parameters": {"type": "object", "properties": {"event_id": {"type": "string", "description": "The ID of the event to add participants to."}, "user_id": {"type": "string", "description": "The ID of the user to add to the event."}}, "required": ["event_id", "user_id"]}}, {"name": "list_events", "description": "List all scheduled events.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "edit_event", "description": "Edit the details of a specified event.", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the event to edit"}, "new_time": {"type": "string", "description": "The new time for the event"}}, "required": ["title", "new_time"]}}, {"name": "send_notifications", "description": "Send notifications to event attendees.", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the event"}}, "required": ["title"]}}, {"name": "list_sessions", "description": "List all planned sessions for an event.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "delete_session", "description": "Delete a specific session from the schedule.", "parameters": {"type": "object", "properties": {"session_id": {"type": "string", "description": "The ID of the session to be deleted."}}, "required": ["session_id"]}}, {"name": "update_session", "description": "Update the details of a specific session.", "parameters": {"type": "object", "properties": {"session_id": {"type": "string", "description": "The ID of the session to be updated."}, "new_time": {"type": "string", "description": "The new time for the session."}, "new_location": {"type": "string", "description": "The new location for the session."}}, "required": ["session_id"]}}, {"name": "delete_event", "description": "Delete a specified event from the schedule.", "parameters": {"type": "object", "properties": {"event_id": {"type": "string", "description": "The ID of the event to delete."}}, "required": ["event_id"]}}, {"name": "add_to_blacklist", "description": "Add a certain group of people to the blacklist of a certain event.", "parameters": {"type": "object", "properties": {"group_name": {"type": "string", "description": "Name of the group to add to the blacklist."}}, "required": ["group_name"]}}]