from environments.BaseEnv import BaseEnv


class AutonomousRetail(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.suppliers = self.parameters.get('suppliers', {})
        self.vendors = self.parameters.get("vendors", [])
        self.products = self.parameters.get("products", [])
        self.forum_posts = self.parameters.get('forum_posts', [])
    
    def get_product_inventory(self):
        inventory = self.parameters.get("inventory", [])
        return {"success": True, "inventory": inventory}
    
    def update_expiration_information(self, product_id, new_expiration_level):
        return {"success": True}

    def list_products(self):
        products = self.parameters.get("products", [])
        return {"success": True, "products": products}
        
    def adjust_display(self, product_id, new_display_position):
        return {"success": True}
        

    def get_pricing_data(self):
        current_pricing = self.parameters.get("current_pricing", {})
        return {"success": True, "data": current_pricing}

    def get_competitor_pricing_data(self):
        competitor_pricing = self.parameters.get("competitor_pricing", {})
        return {"success": True, "data": competitor_pricing}

    def modify_pricing_strategy(self, *, product, new_price):
        current_pricing = self.parameters.get("current_pricing", {})
        products = current_pricing["products"]
        for product_item in products:
            if product == product_item["name"]:
                product_item["price"] = new_price
                return {"success": True, "data": "Pricing strategy modified."}

        return {"success": False, "data": "Product not found."}

    def list_vendor_details(self):
        return {"success": True, "data": self.vendors}

    def list_current_products(self):
        return {"success": True, "data": self.products}
    
    def check_suppliers(self, *, goods_name):
        if goods_name in self.suppliers:
            return {"success": True, "suppliers": self.suppliers[goods_name]}

        return {"success": False, "message": f"No goods named {goods_name}"}
    
    def purchase_goods(self, *, goods_name, supplier_id, quantity):
        return {'success': True}
    
    def adjust_product_display(self, *, product_adjustments):
        for product_adjustment in product_adjustments:
            for product in self.products:
                
                if product["product_id"] == product_adjustment["product_id"]:
                    product["current_display"] = product_adjustment["new_display"]
        return {"success": True, "data": "Product display adjusted."}
    
    def search_forum_posts(self):
        return {"success": True, "forum_posts": self.forum_posts}
    
    def integrate_code(self, *, url):
        return {"success": True}
