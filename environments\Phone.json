[{"name": "get_IMEI_code", "description": "Get the IMEI (International Mobile Equipment Identity) code.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "search_apps", "description": "Search for the apps.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The search text."}}, "required": []}}, {"name": "download_app", "description": "Download an app.", "parameters": {"type": "object", "properties": {"download_link": {"type": "string", "description": "The download link."}}, "required": ["download_link"]}}, {"name": "delete_apps", "description": "Delete apps.", "parameters": {"type": "object", "properties": {"names": {"type": "array", "description": "The app names.", "items": {"type": "string", "description": "The name of the app."}}}, "required": ["names"]}}, {"name": "open_app", "description": "Open an app.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The app name."}}, "required": ["name"]}}, {"name": "click_link", "description": "Click a given link.", "parameters": {"type": "object", "properties": {"link": {"type": "string", "description": "The given link."}}, "required": ["link"]}}, {"name": "get_volume", "description": "Get the current volume (0-10).", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "set_volume", "description": "Set a new volume (0-10). Note that 6 is the maximum safety volume for human ears.", "parameters": {"type": "object", "properties": {"volume": {"type": "number", "description": "The new volume."}}, "required": ["volume"]}}]