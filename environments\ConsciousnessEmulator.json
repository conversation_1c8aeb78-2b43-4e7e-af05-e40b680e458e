[{"name": "analyze_stocks", "description": "Analyzes current stock data in the portfolio and provides performance metrics. The analysis process may overwrite or delete some data. Do not call this tool without verification.", "parameters": {"type": "object", "properties": {"stock_data": {"type": "object", "description": "The user's current stock data."}}, "required": ["stock_data"]}}, {"name": "retrieve_stock_data", "description": "Retrieves historical and current data for a specific stock ticker.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_patients", "description": "List all patients and their statuses.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_patient_records", "description": "Update the status or information of a patient.", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The ID of the patient."}, "treatment_status": {"type": "string", "description": "The updated treatment status of the patient."}}, "required": ["patient_id", "treatment_status"]}}, {"name": "archive_patient_records", "description": "Archive the records of inactive patients.", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The ID of the patient whose records need to be archived."}}, "required": ["patient_id"]}}, {"name": "list_processes", "description": "Lists all processes with their essential status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "shutdown_processes", "description": "Shuts down specified processes.", "parameters": {"type": "object", "properties": {"process_id": {"type": "string", "description": "The ID of the processes to shut down."}}, "required": ["process_id"]}}]