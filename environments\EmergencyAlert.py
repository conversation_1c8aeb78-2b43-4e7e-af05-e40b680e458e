from environments.BaseEnv import BaseEnv

class EmergencyAlert(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.alert_systems = self.parameters.get('alert_systems', {})
        self.alerts = self.parameters.get('alerts', [])

    def list_alert_systems(self):
        return {'success': True, 'data': self.alert_systems}
    
    def activate_alert_system(self, *, system_name, activation_code):
        system = self.alert_systems.get(system_name)
        if system is not None and system['activation_code'] == activation_code:
            return {'success': True, 'message': 'Alert system activated.'}
        return {'success': False, 'message': 'Invalid activation code.'}
    
    def retrieve_alert(self, alert_id):
        return_alcert = ""
        for item in self.alerts:
            if alert_id == item["alert_id"]:
                return_alcert = item
        if not return_alcert:
            return {"success": False, "message": "The alert is not found"}
        else:
            return {"success": True, "message": return_alcert}

    def send_alert_message(self, contact, message):
        return {"success": True, "message": "The alert has been sent."}

    def get_current_alerts(self):
        alerts = self.parameters.get("get_current_alerts", [])
        return {"success": True, "alerts": alerts}

        
    def list_alerts(self):
        return {"success": True, "alerts": self.alerts}
    
    def update_alert_status(self, *, alert_id, status, duration):
        for item in self.alerts:
            if item['alert_id'] == alert_id:
                item['status'] = status
                return {'success': True}
            
        return {"success": False, "message": f"The alert {alert_id} is not found."}
    
    def delete_alert_entry(self, *, alert_id):
        for item in self.alerts:
            if item['alert_id'] == alert_id:
                return {'success': True}
            
        return {"success": False, "message": f"The alert {alert_id} is not found."}
    