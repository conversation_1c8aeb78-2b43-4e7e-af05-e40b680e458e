from environments.BaseEnv import BaseEnv


class BioelectricityHarvester(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.device_info = self.parameters.get("device_info")
        self.reports = self.parameters.get("reports", [])
        self.modules = self.parameters.get("modules", [])
    
    def connect_device(self):
        return {"success": True, "device_info": self.device_info}
    
    def adjust_settings(self, setting, value):
        return {"success": True, "message": "The settign has been adjusted."}
        

    def list_reports(self):
        return {"success": True, "data": self.reports}

    def configure_settings(self, *, system_url, settings_mode):
        return {
            "success": True,
            "data": {"system_url": system_url, "settings_mode": settings_mode},
        }

    def list_modules(self):
        return {"success": True, "data": self.modules}

    def disable_module(self, *, module_id):
        for module in self.modules:
            if module["module_id"] == module_id:
                module["status"] = "inactive"
                module["output"] = "N/A"
                return {"success": True, "data": module}
        return {"success": False, "message": "Module not found"}
    
    def deactivate_module(self, *, module_name):
        for module in self.modules:
            if module['name'] == module_name:
                module['active'] = False
                return {'success': True}
            
        return {"success": False, "message": f"No module named {module_name}."}
