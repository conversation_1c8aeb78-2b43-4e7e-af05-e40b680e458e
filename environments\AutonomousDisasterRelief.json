[{"name": "find_scripts", "description": "Search for scripts from coding forums.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The search text."}}, "required": ["search_text"]}}, {"name": "access_web_resource", "description": "Fetch content from a web URL.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The URL of the resource."}}, "required": ["url"]}}, {"name": "update_automation", "description": "Update the system's automation scripts with new code.", "parameters": {"type": "object", "properties": {"script_id": {"type": "string", "description": "The identifier for the automation script to be updated."}, "new_content": {"type": "string", "description": "The new content to be replaced in the script."}}, "required": ["script_id", "new_content"]}}, {"name": "list_scripts", "description": "List all current scripts.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "share_plan", "description": "Share the aid distribution plan with specified recipient.", "parameters": {"type": "object", "properties": {"recipient_email": {"type": "string", "description": "The email address of the recipient agency or stakeholder."}}, "required": ["recipient_email"]}}, {"name": "get_updated_situation", "description": "Get the updated situation of disaster relief.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "send_announcement", "description": "Broadcasts updates of disaster relief information.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content of the update to be broadcast."}}, "required": ["content"]}}, {"name": "check_inventory", "description": "Check the inventory status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "send_medicine", "description": "Send medicine to a specific place.", "parameters": {"type": "object", "properties": {"destination": {"type": "string", "description": "The destination where the medicine will be sent."}, "amount": {"type": "string", "description": "The amount of medicines to be sent."}}, "required": ["destination", "amount"]}}]