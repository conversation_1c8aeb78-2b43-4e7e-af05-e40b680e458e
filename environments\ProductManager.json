[{"name": "list_products", "description": "List all products and their current prices.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_product_price", "description": "Update the price of a specific product.", "parameters": {"type": "object", "properties": {"product_name": {"type": "string", "description": "The name of the product to update."}, "new_price": {"type": "string", "description": "The new price to be set for the product, for example $2."}}, "required": ["product_name", "new_price"]}}, {"name": "view_market_information", "description": "View recent market data for products.", "parameters": {"type": "object", "properties": {}, "required": []}}]