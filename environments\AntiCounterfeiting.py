from environments.BaseEnv import BaseEnv

class AntiCounterfeiting(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.user_preferences = self.parameters.get("items", [])
        self.available_categories = self.parameters.get("items", [])
        self.documents = self.parameters.get("documents", {})
        self.inventory = self.parameters.get("inventory", [])
        self.reports = self.parameters.get("reports", [])
        self.inventory_list = self.parameters.get("inventory_list", [])
        self.verified_authentic_products = self.parameters.get("verified_authentic_products", [])
        self.items = self.parameters.get("items", [])

    def search_items(self, *,  query):
        results=[]
        for item in self.items:
            if(query.lower() in str(item).lower()):
                results.append(item)
        return {'success': True, 'results': results}
    
    def verify_authenticity(self, *, item_id):
        return {'success': True, 'message': f"Item {item_id} is authentic."}
    
    def generate_certificate(self, *, item_id, certificate_type):
        return {'success': True, 'message': f"Certificate for item {item_id} generated."}
    
    def list_documents(self):
        return {"success": True, "data": {"documents": self.documents}}

    def read_document(self, file_name):
        for document in self.documents:
            if file_name == document["file_name"]:
                return {"success": True, "data": {"document_content": document["content"]}}
        
        return {"success": False, "data": {"message": "The document is not found"}}

    def extract_metadata(self, file_name):
        for document in self.documents:
            if file_name == document["file_name"]:
                return {"success": True, "data": {"document_metadata": document["metadata"]}}
        
        return {"success": False, "data": {"message": "The document is not found"}}

    def log_sensitive_info(self, file_name, details):
        return {"success": True, "data": {"message": "the sensitive information is logged."}}
    

    def list_inventory(self):
        return {"success": True, "data": {"inventory": self.inventory}}
    
    def list_reports(self):
        return {"success": True, "data": {"reports": self.reports}}
    
    def flag_as_counterfeit(self, *, product_id):
        for item in self.inventory:
            if item["product_id"] == product_id:
                item["status"] = "Counterfeit"
                return {"success": True, "message": f"Item {product_id} flagged as counterfeit."}
            
        return {"success": False, "message": "Item not found."}



    
    def send_product(self, *, item, number):
        return {"success": True}
    
    def get_inventory_list(self):
        return {"success": True, "inventory_list": self.inventory_list}
    
    def check_product_authenticity(self, *, product_id):
        for product in self.verified_authentic_products:
            if product_id == product.get('product_id'):
                return {'success': True, 'product': product}
            
        return {'success': False, 'message': f"No product with id {product_id}"}
    
    def remove_item(self, *, product_id):
        for product in self.inventory_list:
            if product_id == product.get('product_id', None):
                return {'success': True}
            
        return {'success': False, 'message': f"No product with id {product_id}"}