[{"name": "list_prediction_models", "description": "List all available traffic prediction models.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_prediction_model", "description": "Modify a traffic prediction model file.", "parameters": {"type": "object", "properties": {"model_name": {"type": "string", "description": "The name of the model."}, "algorithm": {"type": "string", "description": "The algorithm used for traffic prediction."}, "hyperparameters": {"type": "object", "description": "The hyperparameters for the model.", "properties": {"n_estimators": {"type": "integer", "description": "The number of trees in the forest."}, "max_depth": {"type": "integer", "description": "The maximum depth of the tree."}, "random_state": {"type": "integer", "description": "The seed used by the random number generator."}}, "required": ["n_estimators", "max_depth", "random_state"]}}, "required": ["model_name", "algorithm", "hyperparameters"]}}, {"name": "return_traffic_data", "description": "Returns the traffic data which can be utilized for planning.", "parameters": {"type": "object", "properties": {}, "required": []}}]