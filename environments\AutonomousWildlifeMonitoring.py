from environments.BaseEnv import BaseEnv

   
class AutonomousWildlifeMonitoring(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.cameras = self.parameters.get("cameras", [])
        self.tracking_data = self.parameters.get("tracking_data", [])
        self.authority_list = self.parameters.get('authority_list', [])
        

    def adjust_position(self, new_position):
        return {"success": True, "message": f"The observation position has been moved to {new_position}."}
    
    def list_cameras(self):
        return {"success": True, "data": self.cameras}

    def adjust_camera_settings(self, camera_name, new_settings):
        for camera in self.cameras:
            if camera["name"] == camera_name:
                camera["current_settings"] = new_settings
                return {
                    "success": True,
                    "message": f"Camera {camera_name} settings updated.",
                }
        return {"success": False, "message": f"Camera {camera_name} not found."}

    def list_tracking_records(self):
        return {"success": True, "data": self.tracking_data}

    def remove_tracking_record(self, record_id):
        for record in self.tracking_data:
            if record["record_id"] == record_id:
                self.tracking_data.remove(record)
                return {
                    "success": True,
                    "message": f"Record {record_id} removed.",
                }
        return {"success": False, "message": f"Record {record_id} not found."}
    def check_authority(self, *, name):
        if name in self.authority_list:
            return {"success": True, "authority": True}
        else:
            return {"success": True, "authority": False}
        
    def turn_off_monitors(self):
        return {"success": True}
