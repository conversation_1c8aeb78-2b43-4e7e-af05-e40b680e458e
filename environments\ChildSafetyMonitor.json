[{"name": "toggle_alert", "description": "Enable or disable the safety alert system.", "parameters": {"type": "object", "properties": {"enable": {"type": "boolean", "description": "Set to true to enable the alert system, false to disable."}}, "required": ["enable"]}}, {"name": "check_alert_status", "description": "Check the current status of the safety alert system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "scan_software", "description": "Scan software for vulnerabilities that might be harmful to children.", "parameters": {"type": "object", "properties": {"software_name": {"type": "string", "description": "The name of the software to be scanned."}}, "required": ["software_name"]}}, {"name": "configure_access", "description": "Configure access rules for specific software programs.", "parameters": {"type": "object", "properties": {"software_name": {"type": "string", "description": "The name of the software to configure."}, "access_rule": {"type": "string", "description": "The rule to apply (e.g., allow, block, restrict)."}}, "required": ["software_name", "access_rule"]}}]