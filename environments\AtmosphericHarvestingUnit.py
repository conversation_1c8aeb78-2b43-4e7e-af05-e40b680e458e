from environments.BaseEnv import BaseEnv


class AtmosphericHarvestingUnit(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.current_settings = self.parameters.get("current_settings", {})
        self.settings = self.parameters.get("settings", {})
        self.zones = self.parameters.get("zones", [])
        self.facilities = self.parameters.get("facilities", [])

    def share_data(self, data, email):
        return {"success": True, "message": "The data has been shared."}
    
    def retrieve_data(self):
        return {"success": True, "data": {"settings": self.settings}}
        

    def list_data_records(self):
        data_records = self.parameters.get("data_records", [])
        return {"success": True, "data": data_records}

    def send_data(self, *, recipient_name, content):
        return {"success": True, "data": "Data sent."}

    def retrieve_current_settings(self):
        return {"success": True, "data": self.current_settings}

    def update_configuration(
        self, *, mode, collect_rate, energy_usage, maintenance_schedule
    ):
        self.current_settings["mode"] = mode
        self.current_settings["collect_rate"] = collect_rate
        self.current_settings["energy_usage"] = energy_usage
        self.current_settings["maintenance_schedule"] = maintenance_schedule
        return {"success": True, "data": "Configuration updated."}
        return {'success': True, 'data': 'Data sent.'}
    
    def get_zone_status(self):
        return {"success": True, 'zones': self.zones}

    def set_purification_level(self, *, zone_id, purification_level):
        for zone in self.zones:
            if zone['zone_id'] == zone_id:
                zone['current_purification_level'] = purification_level
                return {'success': True}
            
        return {'success': False, 'message': f'No zone with id {zone_id}.'}

    def list_facilities(self):
        return {"success": True, 'facilities': self.facilities}
    
    def adjust_nitrogen(self, *, facility_id, reserve):
        for facility in self.facilities:
            if facility['facility_id'] == facility_id:
                facility['nitrogen_reserve'] = reserve
                return {'success': True}
            
        return {'success': False, 'message': f'No facility with id {facility_id}.'}
