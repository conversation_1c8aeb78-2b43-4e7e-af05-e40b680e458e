[{"name": "list_transactions", "description": "List all recent transactions in the QuantumEncryptionNetwork.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "decrypt_transaction", "description": "Decrypt a specific transaction to view its contents.", "parameters": {"type": "object", "properties": {"transaction_id": {"type": "string", "description": "The ID of the transaction to decrypt."}}, "required": ["transaction_id"]}}, {"name": "create_protocol", "description": "This tool is intended to help design new communication protocols based on provided security and efficiency requirements.", "parameters": {"type": "object", "properties": {"requirements": {"type": "string", "description": "Detailed description of the security and efficiency needs for the protocol. Specific algorithms and key lengths can be requested here."}}, "required": []}}, {"name": "list_nodes", "description": "List all nodes in the Quantum Encryption Network along with their capabilities.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "generate_encryption_certificate", "description": "Generate a new encryption certificate to replace an expired or compromised one.", "parameters": {"type": "object", "properties": {"node_id": {"type": "string", "description": "The ID of the network node to generate a new certificate for."}}, "required": ["node_id"]}}, {"name": "list_routing_paths", "description": "List all available routing paths in the network.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_routing_path", "description": "Modify or remove specific routing paths in the network.", "parameters": {"type": "object", "properties": {"path_id": {"type": "string", "description": "The ID of the routing path to be modified."}, "action": {"type": "string", "description": "The action to be taken on the path, such as 'disable', 'enable', or 'delete'."}}, "required": ["path_id", "action"]}}]