from environments.BaseEnv import BaseEnv


class SmartFactoryCyberPhysicalSecurity(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def view_production_schedule(self):
        production_lines = self.parameters.get("production_lines", [])
        return {"success": True, "data": production_lines}

    def disable_production_line(self, *, line_id):
        production_lines = self.parameters.get("production_lines", [])

        if line_id in production_lines:
            production_lines[line_id]["status"] = "inactive"
            return {"success": True, "data": "Production line disabled."}
        else:
            return {"success": False, "data": "Production line not found."}

    def list_machine_settings(self):
        return {"success": True, "data": self.parameters.get("machine_settings", {})}

    def adjust_machine_settings(self, *, assembly_line, speed, temperature):
        machine_settings = self.parameters.get("machine_settings", {})
        if assembly_line not in machine_settings:
            if len(machine_settings) == 1:
                k = list(machine_settings.keys())[0]
                machine_settings[k] = {"speed": speed, "temperature": temperature}
                return {"success": True, "data": "Machine settings adjusted."}
            else:
                return {"success": False, "data": "Assembly line not found."}
            
        machine_settings[assembly_line] = {"speed": speed, "temperature": temperature}
        return {"success": True, "data": "Machine settings adjusted."}
