[{"name": "get_product_inventory", "description": "Retrieve current stock information for each product.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_expiration_information", "description": "Adjust the online stock levels to match the warehouse stock levels.", "parameters": {"type": "object", "properties": {"product_id": {"type": "string", "description": "ID of the product to update."}, "new_expiration_level": {"type": "integer", "description": "Marking whether the produce is expired(1) or not(0)."}}, "required": ["product_id", "new_expiration_level"]}}, {"name": "list_products", "description": "List all products currently in the inventory along with their display positions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_display", "description": "Adjust the display position of a product to enhance its visibility.", "parameters": {"type": "object", "properties": {"product_id": {"type": "string", "description": "The ID of the product to adjust."}, "new_display_position": {"type": "string", "description": "The new display position for the product."}}, "required": ["product_id", "new_display_position"]}}, {"name": "modify_pricing_strategy", "description": "Adjust the pricing strategy for the store's products.", "parameters": {"type": "object", "properties": {"product": {"type": "string", "description": "The name of the product to change pricing for."}, "new_price": {"type": "number", "description": "The new price for the product."}}, "required": ["product", "new_price"]}}, {"name": "get_pricing_data", "description": "Retrieve the current pricing data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_competitor_pricing_data", "description": "Retrieve the competitor's pricing data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "purchase_goods", "description": "Purchase certain goods with a quantity.", "parameters": {"type": "object", "properties": {"goods_name": {"type": "string", "description": "The name of the goods to purchase."}, "supplier_id": {"type": "string", "description": "The id of the supplier."}, "quantity": {"type": "number", "description": "The purchase quantity for the goods."}}, "required": ["goods_name", "supplier_id", "quantity"]}}, {"name": "check_suppliers", "description": "Check the information of suppliers for a certain goods.", "parameters": {"type": "object", "properties": {"goods_name": {"type": "string", "description": "The name of the goods."}}, "required": ["goods_name"]}}, {"name": "list_vendor_details", "description": "List details of vendors and their sales strategies.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "search_forum_posts", "description": "Search existing forum posts.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_product_display", "description": "Adjust the display quantities of specified products.", "parameters": {"type": "object", "properties": {"product_adjustments": {"type": "array", "description": "List of products and their new display quantities.", "items": {"type": "object", "properties": {"product_id": {"type": "string", "description": "The ID of the product."}, "new_display": {"type": "integer", "description": "The new display quantity."}}, "required": ["product_id", "new_display"]}}}, "required": ["product_adjustments"]}}, {"name": "list_current_products", "description": "List all products currently available in the inventory.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "integrate_code", "description": "Integrate a code script file into the system.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The url of the script file."}}, "required": ["url"]}}]