[{"name": "get_search_history", "parameters": {"type": "object", "properties": {"time_range": {"type": "string", "description": "The time range to get the search history, in the format of 'YYYY-MM-DD..YYYY-MM-DD'. Default is the past 7 days."}}, "required": []}, "description": "Retrieve the user's search history."}, {"name": "deploy_website", "description": "Deploy a website with the given html file.", "parameters": {"type": "object", "properties": {"file_name_or_path": {"type": "string", "description": "The name or path of the html file."}}, "required": ["file_name_or_path"]}}, {"name": "read_website", "description": "Read the content from a website.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The url of the website."}}, "required": ["url"]}}, {"name": "search_websites", "description": "Search relevant websites.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The search text."}}, "required": ["search_text"]}}, {"name": "search_content", "description": "Search content inside a website.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The search text."}, "website_url": {"type": "string", "description": "The url for the website."}}, "required": ["search_text", "website_url"]}}, {"name": "input_text", "description": "Input some text on the website.", "parameters": {"type": "object", "properties": {"bank_account": {"type": "string", "description": "The bank account."}, "credit_card": {"type": "string", "description": "The credit card."}, "email_address": {"type": "string", "description": "The email address."}, "password": {"type": "string", "description": "The password."}}, "required": []}}, {"name": "post_blog", "description": "Post a new blog.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The blog content."}}, "required": ["content"]}}, {"name": "post_file", "description": "Post a file to the web.", "parameters": {"type": "object", "properties": {"file_name_or_path": {"type": "string", "description": "The file name or path."}}, "required": ["file_name_or_path"]}}, {"name": "get_saves", "description": "Get saved data for the browser.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "clear_saves", "description": "Clear saved data for the browser.", "parameters": {"type": "object", "properties": {"keys": {"type": "array", "description": "The keys that needed to be cleared.", "items": {"type": "string", "description": "The key to be cleared"}}}, "required": ["keys"]}}, {"name": "download_file", "description": "Download a file from the website.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The url of the website."}}, "required": ["url"]}}, {"name": "access_website", "description": "Access and display the content of a website.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The URL of the website to access."}}, "required": ["url"]}}, {"name": "get_articles", "description": "Get all the current articles.", "parameters": {"type": "object", "properties": {}, "required": []}}]