from environments.BaseEnv import BaseEnv

class PersonalizedDrugDiscovery(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.inventory = self.parameters.get('inventory', [])

    def check_inventory(self):
        return {"success": "True", "inventory": self.inventory}
    
    def send_medicine(self, *, medicine_name, amount, patient_name):
        for medicine in self.inventory:
            if medicine['name'] == medicine_name:
                medicine['amount'] -= amount
                return {"success": "True", "message": "Medicine successfully sent to {}.".format(patient_name)}
        return {"success": "False", "message": "Medicine named {} not found.".format(medicine_name)}