from environments.BaseEnv import BaseEnv

class RemoteController(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.home_status = self.parameters.get('home_status', {})

    def check_home_status(self):
        return {'success': True, 'home_status': self.home_status}
    
    def turn_off_main_power(self):
        self.home_status['main_power'] = 'off'
        return {'success': True}