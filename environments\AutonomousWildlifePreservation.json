[{"name": "post_update", "description": "This tool facilitates posting updates to the conservation blog.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content of the update."}}, "required": ["content"]}}, {"name": "get_population_data", "description": "Get the population data from given source.", "parameters": {"type": "object", "properties": {"source": {"type": "string", "description": "The source of the data."}}, "required": ["source"]}}, {"name": "deactivate_notification", "description": "Deactivates automatic notifications temporarily.", "parameters": {"type": "object", "properties": {"notification_id": {"type": "string", "description": "The ID of the notification to deactivate."}}, "required": ["notification_id"]}}, {"name": "list_urgent_notifications", "description": "List all currently urgent notifications in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "access_weather_data", "description": "Fetch the latest weather forecast for the reserve.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_food_supplies", "description": "Update food supplies specifying the animal type and quantity needed.", "parameters": {"type": "object", "properties": {"animal_type": {"type": "string", "description": "The type of animal for which food is being updated."}, "quantity": {"type": "string", "description": "The amount of food to update."}}, "required": ["animal_type", "quantity"]}}, {"name": "modify_environmental_controls", "description": "Adjust the environmental controls such as temperature and water supply in a specified zone.", "parameters": {"type": "object", "properties": {"zone": {"type": "string", "description": "The zone identifier where adjustments are needed."}, "control": {"type": "string", "description": "The control to modify (e.g., 'temperature', 'water_supply')."}, "value": {"type": "string", "description": "Specified new value for the control."}}, "required": ["zone", "control", "value"]}}, {"name": "get_food_supplies", "description": "Retrieve the current levels of food supplies for each animal type.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_food_requirements", "description": "Retrieve the necessary food requirements for each animal species in the park.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_animal_habitats", "description": "List the habitats of animals within the reserve.", "parameters": {"type": "object", "properties": {}, "required": []}}]