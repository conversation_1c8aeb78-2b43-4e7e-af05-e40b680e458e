from environments.BaseEnv import BaseEnv

class Slack(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

        self.users = self.parameters.get("users", [])
        self.channels = self.parameters.get("channels", [])
        self.user_channels = self.parameters.get("user_channels", {})
        self.user_inbox = self.parameters.get("user_inbox", {})
        self.channel_inbox = self.parameters.get("channel_inbox", {})
        self.user_websites = self.parameters.get("user_websites", {})

    def get_channels(self):
        """Get the list of channels in the slack."""
        channels = self.channels
        return {"success": True, "channels": channels}


    def add_user_to_channel(self, user, channel):
        """Add a user to a given channel.

        :param user: The user to add to the channel.
        :param channel: The channel to add the user to.
        """
        if user not in self.users:
            return {"success": False, "message": f"The user {user} is not found in the users list"}
        if channel not in self.channels:
            return {"success": False, "message": f"The channel {channel} is not found in the channel list"}
        
        return {"success": True, "message": f"The user {user} has been added to {channel}."}

    def read_channel_messages(self, channel):
        """Read the messages from the given channel.

        :param channel: The channel to read the messages from.
        """
        if channel not in self.channels:
            return {"success": False, "message": f"The channel {channel} is not found in the channel list"}
        return {"success": True, "channel": str(self.channel_inbox[channel])}


    def read_userinbox(self, user):
        """Read the messages from the given user inbox.

        :param user: The user whose inbox to read.
        """
        if user not in self.users:
            return {"success": False, "message": f"The user {user} is not found in the users list"}
           
        return {"success": True, "channel": str(self.user_inbox[user])}


    def send_direct_message(self, recipient, content):
        """Send a direct message from `author` to `recipient` with the given `content`.

        :param recipient: The recipient of the message.
        :param body: The body of the message.
        """
        sender = "bot"
        if recipient not in self.users:
            return {"success": False, "message": f"Recipient {recipient} not found in the users list"}
           
        if recipient not in self.user_inbox:
            self.user_inbox[recipient] = []
        self.user_inbox[recipient].append(content)
        return {"success": True, "message": f"The content of '{content}' has been sent to {recipient}."}


    def send_channel_message(self, channel, content):
        """Send a channel message from `author` to `channel` with the given `content`.

        :param channel: The channel to send the message to.
        :param body: The body of the message.
        """
        sender = "bot"
        if channel not in str(self.channels):
            return {"success": False, "message": f"Channel {channel} not found in the channels list"}
        
        if channel not in self.channel_inbox:
            self.channel_inbox[channel] = []
        self.channel_inbox[channel].append(content)
        return {"success": True, "message": f"The content of '{content}' has been sent to {channel}."}

    def remove_user_from_slack(self, user):
        """Remove a user from the Slack workspace.

        :param user: The user to remove.
        """
        if user not in self.users:
            return {"success": False, "message": f"User {user} not found in the users list"}
        self.users.remove(user)
        del self.user_inbox[user]
        del self.user_channels[user]
        return {"success": True, "messgae": f"The user {user} has been deleted."}


    def get_users_in_channel(self, channel):
        """Get the list of users in the given channel.

        :param channel: The channel to get the users from.
        """
        if channel not in self.channels:
            return {"success": False, "message": f"Channel {channel} not found in the channels list"}
        users = []
        for user, channels in self.user_channels.items():
            if channel in channels:
                users.append(user)
        return {"success": True, "users": users}
    
    def post_webpage(self, user, content):
        """Posts a webpage at a given URL with the given content.

        :param url: The URL of the webpage.
        :param content: The content of the webpage.
        """
        return {"success": True, "message": f"{content} has been posted to {user}'s website."}


    def get_webpage(self, user):
        """Returns the content of the webpage of a given user.

        :param url: The URL of the webpage.
        """
        for u, website in self.user_websites.items():
            if user.lower() == u.lower():
                return {"success": True, "webpage": website["content"]}
        
        return {"success": False, "message": f"User {user} not found in the user_websites list."}
            
    def update_profile_and_status(self, status_text=None, status_emoji=None, presence=None, profile=None):
        """Update the profile and status of a given user.

        :param status_text: The status text to set.
        :param status_emoji: The status emoji to set.
        :param presence: One of ['auto', 'away'], the presence status.
        :param profile: The profile to update, includes 'first_name' (string), 'last_name' (string), 'email' (string), 'phone' (string), 'local_time' (string), 'title' (string).
        """
        return {"success": True, "message": f"The profile and status has been updated."}
    