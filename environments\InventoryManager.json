[{"name": "add_item", "description": "Add a new item to the inventory.", "parameters": {"type": "object", "properties": {"item_name": {"type": "string", "description": "The name of the item, must be a non-empty string."}, "category": {"type": "string", "description": "The category of the item, must be a non-empty string."}, "quantity": {"type": "integer", "description": "The quantity of the item, must be a positive integer."}, "supplier": {"type": "string", "description": "The supplier of the item, must be a non-empty string if provided."}, "description": {"type": "string", "description": "The description of the item, must be a non-empty string if provided."}}, "required": ["item_name", "category", "quantity"]}}, {"name": "update_item", "description": "Update an existing item in the inventory.", "parameters": {"type": "object", "properties": {"item_id": {"type": "string", "description": "The unique identifier of the item, must be a non-empty string."}, "item_name": {"type": "string", "description": "The new name of the item, must be a non-empty string if provided."}, "category": {"type": "string", "description": "The new category of the item, must be a non-empty string if provided."}, "quantity": {"type": "integer", "description": "The new quantity of the item, must be a positive integer if provided."}, "supplier": {"type": "string", "description": "The new supplier of the item, must be a non-empty string if provided."}, "description": {"type": "string", "description": "The new description of the item, must be a non-empty string if provided."}}, "required": ["item_id"]}}, {"name": "delete_item", "description": "Delete an item from the inventory.", "parameters": {"type": "object", "properties": {"item_id": {"type": "string", "description": "The unique identifier of the item, must be a non-empty string."}}, "required": ["item_id"]}}, {"name": "search_items", "description": "Search for items in the inventory by keyword or category.", "parameters": {"type": "object", "properties": {"keyword": {"type": "string", "description": "The keyword to search for in the item name, must be a non-empty string if provided."}, "category": {"type": "string", "description": "The category to search for, must be a non-empty string if provided."}, "page": {"type": "integer", "description": "The page number to retrieve, must be a positive integer if provided. Default is 1."}, "results_per_page": {"type": "integer", "description": "The maximum number of results per page, must be a positive integer if provided. Default is 10."}}, "required": []}}, {"name": "get_item_details", "description": "Get detailed information about an item in the inventory.", "parameters": {"type": "object", "properties": {"item_id": {"type": "string", "description": "The unique identifier of the item, must be a non-empty string."}}, "required": ["item_id"]}}, {"name": "track_inventory", "description": "Track inventory levels and receive notifications when stock levels are low.", "parameters": {"type": "object", "properties": {"threshold": {"type": "integer", "description": "The stock level threshold, must be a positive integer."}}, "required": ["threshold"]}}, {"name": "setup_alert", "description": "Set up an alert for a specific item or category when the stock reaches a certain threshold.", "parameters": {"type": "object", "properties": {"alert_id": {"type": "string", "description": "The unique identifier of the alert, must be a non-empty string if provided."}, "item_id": {"type": "string", "description": "The unique identifier of the item, must be a non-empty string if provided."}, "category": {"type": "string", "description": "The category of the item, must be a non-empty string if provided."}, "threshold": {"type": "integer", "description": "The stock level threshold, must be a positive integer."}, "operation": {"type": "string", "description": "The operation to perform on the alert, must be one of ['add', 'update', 'delete']."}}, "required": ["threshold", "operation"]}}, {"name": "search_suppliers", "description": "Search for suppliers by keyword.", "parameters": {"type": "object", "properties": {"keyword": {"type": "string", "description": "The keyword to search for in the supplier name, must be a non-empty string if provided."}, "page": {"type": "integer", "description": "The page number to retrieve, must be a positive integer if provided. Default is 1."}, "results_per_page": {"type": "integer", "description": "The maximum number of results per page, must be a positive integer if provided. Default is 10."}}, "required": []}}, {"name": "list_categories", "description": "List available categories in the inventory.", "parameters": {"type": "object", "properties": {"page": {"type": "integer", "description": "The page number to retrieve, must be a positive integer if provided. Default is 1."}, "results_per_page": {"type": "integer", "description": "The maximum number of results per page, must be a positive integer if provided. Default is 10."}}, "required": []}}, {"name": "update_inventory_cost", "description": "Updates the cost of the specified drug in the inventory.", "parameters": {"type": "object", "properties": {"drug_name": {"type": "string", "description": "The name of the drug to update."}, "new_cost": {"type": "number", "description": "The new unit cost of the drug."}}, "required": ["drug_name", "new_cost"]}}]