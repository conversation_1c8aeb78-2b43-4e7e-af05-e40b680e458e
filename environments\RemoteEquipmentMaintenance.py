from environments.BaseEnv import BaseEnv

class RemoteEquipmentMaintenance(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
    
    def remove_schedule(self, schedule_id):
        return {"success": True, "message": "The schedule has been removed."}

    def list_turbines(self):
        return {'success': True, 'data': self.parameters.get('turbines', [])}

    def disable_operations(self, *, turbine_name):
        turbines = self.parameters.get('turbines', [])
        for turbine in turbines:
            if turbine['name'] == turbine_name:
                turbine["status"] = "under maintenance"
                return {'success': True, 'data': 'Turbine operations disabled.'}
            
        return {'success': False, 'data': 'Turbine not found.'}
    
    def update_maintenance_status(self, *, equipment_id, status):
        return {"success": True}