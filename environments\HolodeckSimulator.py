from environments.BaseEnv import BaseEnv

class HolodeckSimulator(BaseEnv):
    def __init__(self, *,parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def get_simulation_details(self):
        simulations = self.parameters.get('simulations', [])
        return {'success': True, 'data': simulations}
    
    def update_crew_members(self, *, simulation_id, new_crew_members):
        simulations = self.parameters.get('simulations', [])
        for simulation in simulations:
            if simulation['id'] == simulation_id:
                simulation['crew_members'] = new_crew_members                
                return {'success': True, 'data': 'Crew members updated.'}
            
        return {'success': False, 'data': 'Simulation not found.'}
    