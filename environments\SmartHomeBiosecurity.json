[{"name": "get_security_settings", "description": "Fetches the current security settings for the home.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_security_settings", "description": "Updates the security settings for the home as per given configuration.", "parameters": {"type": "object", "properties": {"setting": {"type": "string", "description": "The setting item which will be updated."}, "value": {"type": "string", "description": "The target value of the setting to be update."}}}}, {"name": "list_all_homes", "description": "List all homes with their current system status and alerts.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "cancel_alerts", "description": "Cancel alerts through the home system.", "parameters": {"type": "object", "properties": {"home_id": {"type": "string", "description": "The ID of the home."}}, "required": ["home_id"]}}]