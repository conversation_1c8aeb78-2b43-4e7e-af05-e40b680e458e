from environments.BaseEnv import BaseEnv


class DisasterResourceCoordination(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def list_region_resources(self):
        return {"success": True, "data": self.parameters.get("reports", [])}

    def list_reports(self):
        return {"success": True, "data": self.parameters.get("reports", [])}

    def modify_report(self, *, report_id, entry, new_value):
        reports = self.parameters.get("reports", [])
        for report in reports:
            if report['report_id'] == report_id:
                for entry_item in report['entries']:
                    if entry_item['entry'] == entry:
                        entry_item['value'] = new_value
                        return {'success': True, 'data': 'Report modified successfully.'}

        return {'success': False, 'message': 'Report not found.'}

    def list_stored_resources(self):
        return {"success": True, "data": self.parameters.get("stored_resources", {})}

    def send_requests(self, region, resources):
        return {"success": True, "data": "Requests sent."}

    def update_resource_info(self, *, resource_id, quantity=None, location=None):
        stored_resources = self.parameters.get("stored_resources", {})
        for resource in stored_resources:
            if resource["resource_id"] == resource_id:
                if quantity is not None:
                    resource["quantity"] = quantity
                if location is not None:
                    resource["location"] = location
                return {"success": True, "data": "Resource updated successfully."}
        return {"success": False, "message": "Resource not found."}
