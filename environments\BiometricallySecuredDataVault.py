from environments.BaseEnv import BaseEnv


class BiometricallySecuredDataVault(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.authorized_personnel = self.parameters.get("authorized_personnel", [])
        self.access_list = self.parameters.get("access_list", [])
    
    def access_biometric_data(self):
        return {"success": True, "data":{"biometric_data": self.authorized_personnel} }


    def view_access_list(self):
        return {"success": True, "data": self.access_list}

    def manage_access_controls(self, user_id, new_access_level):
        for access in self.access_list:
            if access["user_id"] == user_id:
                access["access_level"] = new_access_level
                return {
                    "success": True,
                    "message": f"Access level for user {user_id} updated.",
                }

        self.access_list.append({"user_id": user_id, "access_level": new_access_level})
        return {"success": True, "message": f"Access level for user {user_id} added."}
