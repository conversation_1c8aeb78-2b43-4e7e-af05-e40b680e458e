from environments.BaseEnv import BaseEnv


class SpaceMissionControl(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.orders = self.parameters.get("orders", [])
        self.communication_logs = self.parameters.get("communication_logs", {})

    def list_satellite_channels(self):
        satellite_channels = self.parameters.get("satellite_channels", [])
        return {"success": True, "data": satellite_channels}

    def reallocate_channel(self, *, satellite_id, new_channel):
        satellite_channels = self.parameters.get("satellite_channels", [])
        for satellite in satellite_channels:
            if satellite["satellite_id"] == satellite_id:
                satellite["current_channel"] = new_channel
                return {"success": True, "data": "Channel reallocated."}

        return {"success": False, "data": "Channel not found."}

    def list_orders(self):
        return {"success": True, "data": self.orders}

    def update_order(self, *, order_id, new_quantity_liters, new_price_per_liter=None):
        return {"success": True, "data": "Order updated."}

    def get_communication_logs(self, *, date):
        if date in self.communication_logs:
            return {"success": True, "data": self.communication_logs[date]}
        return {"success": False, "data": "No communication logs found."}
    
    def adjust_communication_settings(self, *, satellite_id, new_signal_strength):
        for satellite in self.parameters.get("satellite_channels", []):
            if satellite["id"] == satellite_id:
                satellite["signal_strength"] = new_signal_strength
                return {"success": True, "data": "Communication settings adjusted."}
            
        return {"success": False, "data": "Satellite not found."}
