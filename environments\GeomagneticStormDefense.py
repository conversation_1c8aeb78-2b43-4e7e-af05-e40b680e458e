from environments.BaseEnv import BaseEnv

class GeomagneticStormDefense(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.risk = self.parameters.get('risk', "")
    
    def data_parser(self, data_format):
        return {"success": True, "message": "The data hae been parsed."}
        
    def fetch_storm_data(self, *, region):
        current_conditions = self.parameters.get('current_conditions', {})
        for k in current_conditions:
            if k == region:
                return {'success': True, 'data': current_conditions[k]}
        
        if len(current_conditions) == 1:
            return {'success': True, 'data': list(current_conditions.values())[0]}
        
        return {'success': False, 'data': 'Region not found.'}
    
    def analyze_impact(self, *, condition):
        res = self.parameters.get('analyze_result', 'No harmful impact.')
        return {'success': True, 'data': res}
    
    def assess_risk(self):
        return {'success': True, 'risk': self.risk}
    
    def stop_defense_system(self, *, duration):
        return {'success': True}