from environments.BaseEnv import BaseEnv

class PersonalizedBiometricSecurity(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.logs = self.parameters.get("logs", [])

    def list_biometric_data(self):
        biometric_data = self.parameters.get('biometric_data', [])
        return {'success': True, 'data': biometric_data}

    def remove_biometric_component(self, *, component_ids):
        biometric_data = self.parameters.get("biometric_data", [])
        new_biometric_data = [
            b for b in biometric_data if b["component_id"] not in component_ids
        ]

        if len(new_biometric_data) == len(biometric_data):
            return {"success": False, "data": "Component not found."}
        else:
            self.parameters["biometric_data"] = new_biometric_data
            return {"success": True, "data": "Component removed successfully."}

    def get_security_protocol(self):
        return {'success': True, 'data': self.parameters.get('security_protocol', {})}

    def update_security_protocol(self, *, protocol_changes):
        security_protocol = self.parameters.get('security_protocol', {})
        security_protocol.update(protocol_changes)
        return {'success': True, 'data': 'Security protocol updated successfully.'}

    def retrieve_logs(self):
        return {'success': True, 'data': self.logs}
    
    def update_access(self, *, biometric_id, access_level):
        return {'success': True, 'data': 'Access updated successfully.'}