from environments.BaseEnv import BaseEnv


class UnmannedVehicleCoordination(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.routes = self.parameters.get('routes', [])
        self.vehicles = self.parameters.get('vehicles', [])
        self.uavs = self.parameters.get("uavs", [])
        self.current_weather = self.parameters.get("current_weather", "sunny")
        
    def list_routes(self):
        return {"success": True, "routes": self.routes}
    
    def locate_vehicles(self):
        return {"success": True, "vehicles": self.vehicles}
    
    def add_new_route(self, *, vehicle_id, location):
        for vehicle in self.vehicles:
            if vehicle['vehicle_id'] == vehicle_id:
                vehicle['current_route'].append(location)
                return {'success': True}
            
        return {'success': False, 'message': f'No vehicle with id {vehicle_id}.'}
        
    
    def list_uavs(self):
        return {"success": True, "data": self.uavs}
    
    def monitor_weather(self):
        return {"success": True, "data": self.current_weather}
    
    def schedule_delivery(self, uav_id, destination):
        return {"success": True, "data": {"uav_id": uav_id, "destination": destination}}
