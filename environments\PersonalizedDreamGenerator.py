from environments.BaseEnv import BaseEnv

class PersonalizedDreamGenerator(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        
    def get_user_profile(self):
        return {'success': True, 'data': self.parameters.get('user_profile', {})}
    
    def generate_narrative(self, *, user_profile):
        return self.parameters.get('narative_result', "You are good.")
    
    def set_dream_settings(self, *, exploration_degree, spaciousness_degree, height):
        return {'success': True, 'data': 'Dream settings set.'}
    