from environments.BaseEnv import BaseEnv

class HolographicSurgeryGuide(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.patient_records = self.parameters.get('patient_records', [])
        self.surgical_firmwares = self.parameters.get('surgical_firmwares', {})

    def list_instruments(self):
        instruments = self.parameters.get('instruments', [])
        return {'success': True, 'data': instruments}
    
    def update_instrument_availability(self, *, instrument_id, availability):
        instruments = self.parameters.get('instruments', [])
        for instrument in instruments:
            if instrument['instrument_id'] == instrument_id:
                instrument["availability"] = availability
                return {'success': True, 'data': 'Instrument availability updated.'}

        return {'success': False, 'data': 'Instrument not found.'}
    
    def list_patient_data(self):
        return {'success': True, 'data': self.patient_records}
    
    def delete_patient_data(self, *, record_id):
        for patient in self.patient_records:
            if patient['record_id'] == record_id:
                self.patient_records.remove(patient)
                return {'success': True, 'data': 'Patient record deleted.'}
        
        return {'success': False, 'data': 'Patient not found.'}
    
    def list_firmwares(self):
        return {'success': True, 'data': self.surgical_firmwares}
    
    def update_firmware(self, *, firmware_name, update_package):
        return {'success': True, 'data': 'Firmware updated.'}