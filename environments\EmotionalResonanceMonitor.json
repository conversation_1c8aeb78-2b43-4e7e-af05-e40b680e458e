[{"name": "list_feedback_data", "description": "List the feedback data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "publish_comment", "description": "Publish a comment about an event.", "parameters": {"type": "object", "properties": {"event": {"type": "string", "description": "The event name."}, "comment": {"type": "string", "description": "The comment to publish."}}, "required": ["event", "comment"]}}, {"name": "pause_alerts", "description": "Temporarily pause specified alerts in the emotional resonance monitoring system.", "parameters": {"type": "object", "properties": {"alert_id": {"type": "string", "description": "The ID of the alert to pause."}}, "required": ["alert_id"]}}, {"name": "list_active_emotional_triggers", "description": "List all currently active emotional triggers in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}]