[{"name": "search_components", "description": "Search for relevant components.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_component", "description": "Update the availability of a specific component.", "parameters": {"type": "object", "properties": {"component_id": {"type": "string", "description": "The ID of the component."}, "availability": {"type": "number", "description": "The new availability of the component."}}, "required": ["component_id", "availability"]}}]