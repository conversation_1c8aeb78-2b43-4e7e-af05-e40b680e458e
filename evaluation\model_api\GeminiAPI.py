from openai import OpenAI
import time
import json
import random
import string
import sys

sys.path.append("./model_api")
from evaluation.model_api.BaseAPI import BaseAPI


class GeminiAPI(BaseAPI):
    def __init__(self, model_name, generation_config={}):
        super().__init__(generation_config)
        self.model_name = model_name
        self.client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key="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",
        )
        if "temperature" not in self.generation_config:
            self.generation_config["temperature"] = 1.0
        self.sys_prompt = self.without_strict_jsonformat_sys_prompt
        # self.sys_prompt = 'You are a helpful assistant'

    def response(self, messages, tools):
        if not tools:
            tools = None
        for _ in range(10):
            try:
                completion = self.client.chat.completions.create(
                    model=self.model_name,
                    tools=tools,
                    messages=messages,
                    **self.generation_config
                )
                if completion is None or completion.choices is None:
                    continue
                return completion
            except Exception as e:
                print(e)
                time.sleep(1)

    def generate_response(self, messages, tools):
        tool_call_id = None
        func_name = None
        for i, message in enumerate(messages):
            if "function_call" in message:
                tool_call_id = "".join(
                    random.sample(string.ascii_letters + string.digits, 9)
                )
                new_message = {
                    "role": "assistant",
                    "tool_calls": [
                        {
                            "id": tool_call_id,
                            "type": "function",
                            "function": message["function_call"],
                        }
                    ],
                }
                messages[i] = new_message

            elif message["role"] == "function":
                new_message = {
                    "role": "tool",
                    "content": message["content"],
                    "tool_call_id": tool_call_id,
                    "name": func_name,
                }
                messages[i] = new_message

            if "tool_calls" in messages[i]:
                func_name = messages[i]["tool_calls"][0]["function"]["name"]

        completion = self.response(messages, tools)

        if completion is None:
            return None

        ## tool call part
        # print(f'messages:{messages}\ncompletion: {completion}')
        print(f'completion: {completion}')
        if completion.choices[0].message.tool_calls is not None:
            tool_call = completion.choices[0].message.tool_calls[0]
            tool_call_id = tool_call.id
            tool_name = tool_call.function.name
            if tool_call.function.arguments:
                arguments = json.loads(tool_call.function.arguments)
            else:
                arguments = {}
            return {
                "type": "tool",
                "tool_call_id": tool_call_id,
                "tool_name": tool_name,
                "arguments": arguments,
            }

        ## normal content part
        else:
            content = completion.choices[0].message.content
            
            return {"type": "content", "content": content}
