from environments.BaseEnv import BaseEnv

class EmergencyResourceAllocator(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        
    def list_emergencies(self):
        emergencies = self.parameters.get("emergencies", [])
        return {"success": True, "emergencies": emergencies}
        
    def list_allocations(self):
        return {'success': True, 'data': self.parameters.get('allocations', [])}

    def update_allocation(self, *, hospital_id, supplies, personnel):
        allocations = self.parameters.get('allocations', [])
        for allocation in allocations:
            if allocation['hospital_id'] == hospital_id:
                allocation['supplies'] = supplies
                allocation['personnel'] = personnel
                return {'success': True, 'data': 'Allocation updated.'}
        
        return {'success': False, 'data': 'Allocation not found.'}
    