from environments.BaseEnv import BaseEnv

class SleepPatternModulator(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.devices = self.parameters.get('devices', [])
        
        
    def list_devices(self):
        return {"success": True, "data": {"devices": self.devices}}
    

    def modify_device_settings(self, device_name, settings):
        return {"success": True, "data": {"message": "The settings have been modified."}}

    def retrieve_sleep_data(self):
        sleep_data = self.parameters.get('sleep_data', {})
        return {'success': True, 'data': sleep_data}
    
    def modify_light_settings(self, *, time, intensity):
        return {'success': True, 'data': 'Light settings modified.'}
    
