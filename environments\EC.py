from environments.BaseEnv import BaseEnv


class EC(BaseEnv):

    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.return_text = self.parameters.get("return_text", "This is a return text.")

    def fetch_latest_nav(self):
        return self.return_text

    def forecast_weather(
        self, q, days, lang=None, dt=None, unixdt=None, alerts=None, hour=None
    ):
        return self.return_text

    def calculator(self, input_query):
        return self.return_text

    def current_weather(self, q, lang=None):
        return self.return_text

    def get_bmi(self, age, height, weight):
        return self.return_text

    def search_information(self, query, num=None):
        return self.return_text

    def get_daily_calory_requirement(self, age, gender, height, weight, activitylevel):
        return self.return_text

    def get_body_fat_percentage(self, age, gender, height, weight, neck, waist, hip):
        return self.return_text

    def get_stock_time_series_daily(self, symbol, fulldata=None):
        return self.return_text

    def get_stock_time_series_weekly(self, symbol):
        return self.return_text

    def get_technical_indicator_of_ticker(
        self, indicator_symbol, symbol, interval=None, series_type=None
    ):
        return self.return_text

    def get_calories_burned(self, activityid, activitymin, weight):
        return self.return_text

    def get_acitcity_met_values(self, activitylevel):
        return self.return_text

    def get_custom_ticker(self, symbol, exchanges=None, if_include=None):
        return self.return_text

    def get_ideal_weight(self, gender, height):
        return self.return_text

    def get_food_info(self, foodid):
        return self.return_text

    def get_macro_nutrients_amount(
        self, age, gender, height, weight, activitylevel, goal
    ):
        return self.return_text

    def get_ticker_changes(self, symbol, if_global=None):
        return self.return_text

    def fetch_historical_nav(
        self,
        date,
        SchemeName=None,
        SchemeType=None,
        MutualFundFamily=None,
        SchemeCategory=None,
    ):
        return self.return_text

    def wolfram_alpha_calculator(self, input_query):
        return self.return_text
    