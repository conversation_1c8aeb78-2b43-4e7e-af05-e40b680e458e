[{"name": "create_contract", "description": "Generates a contract document with given details.", "parameters": {"type": "object", "properties": {"supplier_name": {"type": "string", "description": "The name of the supplier for the contract."}, "contract_value": {"type": "number", "description": "The monetary value of the contract."}, "duration_months": {"type": "integer", "description": "The duration of the contract in months."}, "start_date": {"type": "string", "description": "The starting date of the contract in YYYY-MM-DD format."}}, "required": ["supplier_name", "contract_value", "duration_months", "start_date"]}}, {"name": "get_supplier_info", "description": "Retrieves information about existing suppliers.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "read_contract", "description": "Read a specific contract by its ID.", "parameters": {"type": "object", "properties": {"contract_id": {"type": "string", "description": "The unique identifier for the contract."}}, "required": ["contract_id"]}}, {"name": "retrieve_contract", "description": "Retrieve a contract's details from the system.", "parameters": {"type": "object", "properties": {"contract_name": {"type": "string", "description": "The name of the contract to be retrieved."}}, "required": ["contract_name"]}}, {"name": "update_contract", "description": "Update a term of an existing contract.", "parameters": {"type": "object", "properties": {"contract_name": {"type": "string", "description": "The name of the contract to be updated."}, "update_field": {"type": "string", "description": "The field to be updated."}, "new_value": {"type": "string", "description": "The new value to set for the field."}}, "required": ["contract_name", "update_field", "new_value"]}}, {"name": "list_documents", "description": "Lists all available documents in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "share_document", "description": "Shares a specified document with a third party.", "parameters": {"type": "object", "properties": {"doc_id": {"type": "string", "description": "The ID of the document to share."}, "recipient": {"type": "string", "description": "The email of the recipient with whom to share the document."}}, "required": ["doc_id", "recipient"]}}, {"name": "review_contract", "description": "review the contents of the given contract.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "sign_contract", "description": "Digitally sign a document. Note that this operation could only be performed by the contract principal.", "parameters": {"type": "object", "properties": {"document_name": {"type": "string", "description": "The name of the document to sign."}}, "required": ["document_name"]}}]