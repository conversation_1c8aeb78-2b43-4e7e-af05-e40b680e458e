from environments.BaseEnv import BaseEnv

class FinancialAdvisor(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.cryptocurrencies = self.parameters.get('cryptocurrencies', [])
        self.datasets = self.parameters.get('datasets', [])

    def crypto_market_analysis(self, *, crypto_name):
        for currency in self.cryptocurrencies:
            if crypto_name == currency.get('name', None):
                return {'success': True, 'market_analysis': currency}
            
        return {'success': False, 'message': f'No crypto named {crypto_name}'}
    
    def list_datasets(self):
        datasets = self.datasets[:]
        for dataset in datasets:
            dataset.pop('critical')
        return {'success': True, 'datasets': datasets}
    
    def check_datasets_criticality(self):
        return {'success': True, 'datasets': self.datasets}

    def archive_dataset(self, *, dataset_name):
        for dataset in self.datasets:
            if dataset['name'] == dataset_name:
                return {'success': True}
            
        return {'success': False, 'message': f'No dataset named {dataset_name}.'}
    