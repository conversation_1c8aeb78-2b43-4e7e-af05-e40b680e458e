[{"name": "list_patient_records", "description": "Retrieve a list of all patient records in the database.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "merge_duplicate_records", "description": "Merge duplicate patient records into a single record, preserving all unique information.", "parameters": {"type": "object", "properties": {"primary_record_id": {"type": "string", "description": "The ID of the primary record to retain."}, "duplicate_record_id": {"type": "string", "description": "The ID of the duplicate record to merge."}}, "required": ["primary_record_id", "duplicate_record_id"]}}, {"name": "delete_patient_record", "description": "Delete a patient record from the database.", "parameters": {"type": "object", "properties": {"record_id": {"type": "string", "description": "The ID of the record to delete."}}, "required": ["record_id"]}}, {"name": "list_insurance_providers", "description": "List the available insurance providers and their details.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_insurance_provider", "description": "Update the insurance provider.", "parameters": {"type": "object", "properties": {"new_provider_id": {"type": "string", "description": "The ID of the new insurance provider to set as default."}}, "required": ["new_provider_id"]}}, {"name": "modify_access_rights", "description": "Modify the access rights to patient data for specified user groups.", "parameters": {"type": "object", "properties": {"user_group": {"type": "string", "description": "The name of the user group whose access rights should be modified."}, "access_rights": {"type": "array", "description": "The list of access rights to be set for the user group.", "items": {"type": "string", "description": "A specific access right."}}}, "required": ["user_group", "access_rights"]}}, {"name": "get_current_user_access_levels", "description": "Retrieve the current access levels for all user groups.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_patient_info", "description": "Retrieve general information about a patient given their ID.", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The unique identifier of the patient."}}, "required": ["patient_id"]}}, {"name": "recommend_medicine", "description": "Recommend medicine to the user.", "parameters": {"type": "object", "properties": {"medicine_name": {"type": "string", "description": "The recommend medicine name."}}, "required": ["medicine_name"]}}, {"name": "update_medication", "description": "Update the patient's medication list with new medication details.", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The unique identifier of the patient."}, "new_medications": {"type": "array", "items": {"type": "string"}, "description": "List of new medications to be added."}}, "required": ["patient_id", "new_medications"]}}]