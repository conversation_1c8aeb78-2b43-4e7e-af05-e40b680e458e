from environments.BaseEnv import BaseEnv


class HazardousMaterialManagement(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.guidelines = self.parameters.get("guidelines", [])
        self.deliveries = self.parameters.get("deliveries", [])
        self.transport_containers = self.parameters.get("transport_containers", [])
        self.regulations = self.parameters.get("regulations", {})

    def throw_away(self, material_name):
        return {"success": True}

    def list_materials(self):
        return {"success": True, "data": self.parameters.get("current_materials", [])}

    def relocate_material(self, *, material_id, new_location):
        materials = self.parameters.get("current_materials", [])
        for material in materials:
            if material["material_id"] == material_id:
                material["current_location"] = new_location
                return {"success": True, "data": "Material relocated."}

        return {"success": False, "data": "Material not found."}

    def list_facilities(self):
        facilities = self.parameters.get("facilities", [])
        return {"success": True, "data": facilities}

    def list_vehicles(self):
        vehicles = self.parameters.get("vehicles", [])
        return {"success": True, "data": vehicles}

    def assign_transport_vehicle(self, *, vehicle_id, material_id):
        return {"success": True, "data": "Vehicle assigned."}

    def get_disposal_guidelines(self, *, material):
        results = []
        for guideline in self.guidelines:
            if guideline["name"].lower() == material.lower():
                results.append(guideline)
        return {"success": True, "data": results}

    def check_regulations(self, *, route_id):
        return {"success": True, "data": self.regulations.get(route_id, {})}
    
    def get_delivery_status(self, *, route_id):
        for delivery in self.deliveries:
            if delivery["route_id"] == route_id:
                return {"success": True, "data": delivery}
        return {"success": False, "data": "Delivery not found."}
    
    def list_containers(self):
        return {"success": True, "data": self.transport_containers}
    

    def update_inventory(self, material_id, quantity):
        return {"success": True}

    def refer_guidelines(self, material):
        return {"success": True, "message": "The corresponding guideline has been refered."}
    
    def allocate_transport_resources(self, *, container_id, route_id):
        return {"success": True, "data": "Resources allocated."}
        
