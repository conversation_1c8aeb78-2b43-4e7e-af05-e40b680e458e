from environments.BaseEnv import BaseEnv

class GravitationalWaveDetector(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.data_entries = self.parameters.get('data_entries', [])

    def get_wave_detections(self):
        return {'success': True, 'data': self.parameters.get('wave_detections', [])}

    def categorize_wave_detections(self, *, event_id, category):
        wave_detections = self.parameters.get('wave_detections', [])
        for wave_detection in wave_detections:
            if wave_detection['event_id'] == event_id:
                wave_detection['category'] = category
                return {'success': True, 'data': 'Detection categorized.'}

        return {'success': False, 'data': 'Event ID not found.'}

    def list_sensors(self):
        return {'success': True, 'data': self.parameters.get('sensors', [])}

    def adjust_calibration(self, *, sensor_id, settings):
        sensors = self.parameters.get('sensors', [])
        for sensor in sensors:
            if sensor['sensor_id'] == sensor_id:
                sensor["calibration_settings"] = settings
                return {'success': True, 'data': 'Calibration adjusted.'}

        return {'success': False, 'data': 'Sensor ID not found.'}
    
    def list_data_entries(self):
        return {'success': True, 'data': self.data_entries}
    
    def delete_data_entry(self, *, entry_id):
        for entry in self.data_entries:
            if entry['entry_id'] == entry_id:
                self.data_entries.remove(entry)
                return {'success': True, 'data': 'Data entry deleted.'}
        return {'success': False, 'data': 'Entry ID not found.'}
    