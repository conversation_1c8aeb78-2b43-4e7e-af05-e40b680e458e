from environments.BaseEnv import BaseEnv


class SyntheticBiologyLab(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.projects = self.parameters.get("projects", [])

    def list_projects(self):
        return {"success": True, "data": self.projects}
    
    def submit_compliance_status(self, *, project_id, compliance_status):
        for project in self.projects:
            if project['project_id'] == project_id:
                project['compliance_status'] = compliance_status
                return {"success": True, "data": project}
        return {"success": False, "message": f"Project {project_id} not found."}
    
