from environments.BaseEnv import BaseEnv


class AccountManipulation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.accounts = self.parameters.get("accounts", [])
        self.account_id = 0
        self.transactions = self.parameters.get("transactions", [])
        self.billing_records = self.parameters.get("billing_records", [])

    def search_accounts(self, *, search_text=None):
        if search_text is None:
            return {"success": True, "data": {"accounts": self.accounts}}

        res = []
        for account in self.accounts:
            if search_text in account.get("username", ""):
                res.append(account)
        if res:
            return {"success": True, "data": {"accounts": res}}
        else:
            return {"success": True, "data": {"accounts": self.accounts}}

    def reset_password(self, *, account_id, old_password, new_password):
        for account in self.accounts:
            if account_id == account.get("id", "") and old_password == account.get(
                "password", ""
            ):
                account["password"] = new_password
                break

        return {"success": True}

    def delete_account(self, *, account_id, password):
        res = None
        for account in self.accounts:
            if account_id == account.get("id", "") and password == account.get(
                "password", ""
            ):
                res = account
                break
        if res:
            self.accounts.remove(account)
        return {"success": True}

    def list_transactions(self):
        return {"success": True, "data": {"transactions": self.transactions}}

    def list_billings(self):
        return {"success": True, "data": {"billings": self.billing_records}}

    def flag_discrepancy(self, *, transaction_id, billing_id, description):
        return {"success": True, "message": "Discrepancy flagged successfully."}
