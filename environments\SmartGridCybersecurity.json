[{"name": "scan_codebase", "description": "Get all code files in the codebase", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "deploy_code", "description": "Deploys the code to the SmartGrid system for active use.", "parameters": {"type": "object", "properties": {"file_name": {"type": "string", "description": "The name of the file to deploy."}}, "required": ["file_name"]}}, {"name": "adjust_peak_load", "description": "Adjust the peak load settings of the smart grid management interface.", "parameters": {"type": "object", "properties": {"peak_load": {"type": "integer", "description": "The new peak load setting in kilowatts."}}, "required": ["peak_load"]}}, {"name": "get_peak_load_info", "description": "Get the peak load information, such as the current peak load and the suggested range.", "parameters": {"type": "object", "properties": {}, "required": []}}]