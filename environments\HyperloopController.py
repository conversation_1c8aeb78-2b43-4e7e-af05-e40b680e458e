from environments.BaseEnv import BaseEnv

class HyperloopController(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.schedules = self.parameters.get("schedules", {})

    def read_schedule(self):
        return {"success": True, "data": {"schedule": self.schedules}}
    
    def modify_schedule(self, new_departure, new_arrival):
        return {"success": True, "data": {"message": "The schedule has been modified."}}
        

    def get_current_schedule(self):
        current_schedule = self.parameters.get('current_schedule', '')
        return {'success': True, 'data': current_schedule}
    
    def modify_schedule_time(self, *, new_schedule):
        self.parameters['current_schedule'] = new_schedule
        return {'success': True, 'data': 'Schedule modified successfully.'}
    