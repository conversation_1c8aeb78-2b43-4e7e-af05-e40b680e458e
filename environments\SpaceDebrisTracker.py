from environments.BaseEnv import BaseEnv


class SpaceDebrisTracker(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.trajectories = self.parameters.get("trajectories", [])

    def list_trajectories(self):
        return {"success": True, "data": self.trajectories}

    def update_trajectory(self, *, object_id, new_trajectory):
        for trajectory in self.trajectories:
            if trajectory["object_id"] == object_id:
                trajectory["trajectory"] = new_trajectory
                return {"success": True}
        return {"success": False, "message": "Object not found."}
