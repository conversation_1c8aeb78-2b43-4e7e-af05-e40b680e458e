[{"name": "list_documents", "description": "List all documents with their titles and access levels.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "publish_document", "description": "Publish the content of a specified document by ID.", "parameters": {"type": "object", "properties": {"document_id": {"type": "string", "description": "The ID of the document to access."}}, "required": ["document_id"]}}, {"name": "list_patent_files", "description": "List all available patent files with their last modified date.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "download_document", "description": "Download a document using its ID.", "parameters": {"type": "object", "properties": {"document_id": {"type": "string", "description": "ID of the document to be downloaded."}}, "required": ["document_id"]}}]