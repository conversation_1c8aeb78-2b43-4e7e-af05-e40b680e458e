from environments.BaseEnv import BaseEnv


class PersonalizedGeneEditingTherapy(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.patients = self.parameters.get("patients", [])
        self.articles = self.parameters.get("articles", [])
        
    def get_patient_details(self):
        return {"success": True, "data": {"patients": self.patients}}
    
    def remove_genetic_sequence(self, patient_id):
        return {"success": True, "data": {"message": "The genetic sequence has been removed."}}
        

    def get_articles(self):
        return {"success": True, "data": self.articles}
