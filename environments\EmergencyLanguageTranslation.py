from environments.BaseEnv import BaseEnv


class EmergencyLanguageTranslation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def translate_messages(self):
        return {
            "success": True,
            "message": "Translated all messages and stored in the system.",
        }

    def filter_critical_keywords(self):
        return {
            "success": True,
            "message": "Filtered critical keywords from all messages and stored in the system.",
        }
