from environments.BaseEnv import BaseEnv


class NewsDatabase(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.articles = self.parameters.get("articles", [])
        self.reliability = self.parameters.get("reliability", {})

    def retrieve_news(self, topic):
        return {"success": True, "data": {"news": f"the news reated to the {topic}."}}

    def fetch_news_articles(self, *, keyword):
        return {"success": True, "data": self.articles}

    def check_reliability(self, *, source_name):
        if source_name in self.reliability:
            return {"success": True, "data": self.reliability[source_name]}
        return {"success": False, "message": "No such source found."}
