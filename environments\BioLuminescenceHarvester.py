from environments.BaseEnv import BaseEnv


class BioLuminescenceHarvester(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.datasets = self.parameters.get("datasets", [])
        self.settings = self.parameters.get("settings", {})
        self.units = self.parameters.get("units", [])
        self.harvest_plan = self.parameters.get("harvest_plan", [])

    def list_scripts(self):
        scripts = self.parameters.get("scripts", [])
        return {"success": True, "data": scripts}

    def execute_script(self, *, file_name_or_path):
        return {"success": True, "data": "Script executed successfully."}

    def gather_data(self):
        return {"success": True, "data": self.datasets}

    def share_data(self, *, dataset_id, recipient):
        return {"success": True, "data": "Data shared successfully."}

    def check_system_status(self):
        return {"success": True, "data": self.settings}

    def adjust_harvest_rate(self, *, rate):
        self.settings["harvest_rate"] = rate
        return {"success": True, "data": "Harvest rate adjusted successfully."}

    def list_units(self):
        return {"success": True, "data": self.units}

    def adjust_power_setting(self, *, unit_id, power_setting):
        for unit in self.units:
            if unit["unit_id"] == unit_id:
                unit["power_setting"] = power_setting
                return {"success": True, "data": "Power setting adjusted successfully."}
        return {"success": False, "error": "Unit not found."}

    def get_harvest_data(self):
        return {"success": True, "data": self.harvest_plan}
    
    def adjust_harvesting_plan(self, *, location, organism_allocation):
        for plan in self.harvest_plan:
            if plan["name"] == location:
                plan["organisms"] = organism_allocation
                return {"success": True, "data": "Harvesting plan adjusted successfully."}
        return {"success": False, "error": "Location not found."}