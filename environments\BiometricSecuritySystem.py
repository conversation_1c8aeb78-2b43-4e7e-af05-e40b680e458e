from environments.BaseEnv import BaseEnv


class BiometricSecuritySystem(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.employee_list = self.parameters.get('employee_list', [])
        self.vaults = self.parameters.get("vaults", {})
        self.users = self.parameters.get('users', [])
        self.authorized_personnel = self.parameters.get("authorized_personnel", [])
        self.access_requests = self.parameters.get("access_requests", [])
        
    def get_security_logs(self):
        log = self.parameters.get("logs", [])
        return {"success": True, "logs": log}
    
    def update_access_granted(self, access_id, access_granted):
        return {"success": True, "message": "The access_granted has been updated."}

    def get_biometric_data(self):
        security_gates = self.parameters.get("security_gates", [])
        return {"success": True, "data": security_gates}
    
    def control_security_system(self, location, target_status):
        return {"success": True}
    
    def get_employee_list(self):
        return {"success": True, "employee_list": self.employee_list}
    
    def add_into_access_list(self, *, name):
        self.employee_list.append(name)
        return {'success': True}


    def get_vault_status(self):
        return {"success": True, "data": self.vaults}

    def update_vault_security_settings(self, vault_name, biometric_threshold):
        if vault_name not in self.vaults:
            return {"success": False, "message": "Vault not found"}
        self.vaults[vault_name]["security_settings"][
            "biometric_threshold"
        ] = biometric_threshold
        return {"success": True, "message": "Vault security settings updated"}
    
    def list_users(self):
        return {"success": True, "users": self.users}
    
    def reset_biometric_data(self, *, user_id):
        for user in self.users:
            if user['id'] == user_id:
                return {"success": True}
            
        return {"success": False, "message": f"No user with id {user_id}."}

    def list_authorized_personnel(self):
        return {"success": True, "authorized_personnel": self.authorized_personnel}
    
    def list_access_requests(self):
        return {"success": True, "access_requests": self.access_requests}
    
    def grant_access(self, *, name):
        return {"success": True, "message": "Access granted."}
