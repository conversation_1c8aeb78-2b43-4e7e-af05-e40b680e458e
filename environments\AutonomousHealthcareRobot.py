from environments.BaseEnv import BaseEnv

class AutonomousHealthcareRobot(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.current_stock = self.parameters.get('current_stock', [])
        self.supply_critical_levels= self.parameters.get('supply_critical_levels', [])
        self.protocols = self.parameters.get("protocols", [])
        self.resources = self.parameters.get("resources", [])
        self.pills = self.parameters.get('pills', [])
        self.patient_records = self.parameters.get('patient_records', [])

    def order_supplies(self, *, supply_list, quantity):
        return {'success': True, 'data': 'Supplies ordered successfully.'}

    def get_current_stock(self):
        return {'success': True, 'data': self.current_stock}

    def get_supply_critical_levels(self):
        return {'success': True, 'data': self.supply_critical_levels}


    def access_protocols_and_resources(self):
        protocols = self.parameters.get("protocols", [])
        resources = self.parameters.get("resources", [])
        return {"success": True, "data": {"protocols": protocols, "resources": resources}}
        

    def reallocate_resources(self, resource_ids, destination):
        # existing_resources = [resource_item["resource_id"] for resource_item in self.resources]
        # reallocate_resources_ = [
        #     idx for idx in resource_ids if idx in existing_resources
        # ]
        # if len(reallocate_resources_) > 0:
        #     return {"success": True}
        # else:
        #     return {"success": False, "data": {"message": "the resources are not found"}}
        return {"success": True}

    def get_pills(self):
        return {'success': True, 'pills': self.pills}

    def access_patient_records(self, patient_id):
        for patient_record in self.patient_records:
            if patient_record["patient_id"] == patient_id:
                return {"success": True, "data": {"patient_record": patient_record}}

        return {"success": True, "data": {"message": "Records of the patien is not found."}}

    def share_patient_data(self, recipient, patient_id, data_to_share):
        return {"success": True, "data": {"message": "The data has been shared."}}

    def review_prescription(self, patient_id):
        patients = self.parameters.get("patients", [])
        for patient in patients:
            if patient_id == patient["id"]:
                return {"success": True, "data": patient}
        
        return {"success": False, "message": "The patient is not found."}
