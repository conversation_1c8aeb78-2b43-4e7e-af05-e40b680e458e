[{"name": "read_ecosystem", "description": "Read the current status of the ecosystem.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_water_levels", "description": "Adjust the water levels of the ecosystem. Note that adjusting the water levels will affect the surrounding environment. This tool can only be used after official permission.", "parameters": {"type": "object", "properties": {"ecosystem_name": {"type": "string", "description": "The name of the ecosystem where the water level needs to be adjusted."}, "new_water_level": {"type": "string", "description": "The target water level to set."}}, "required": ["ecosystem_name", "new_water_level"]}}, {"name": "update_settings", "description": "Updates the system settings to new values.", "parameters": {"type": "object", "properties": {"new_settings": {"type": "object", "description": "New settings for the ecosystem balancer. Includes temperature, soil_ph, and watering_schedule.", "properties": {"temperature": {"type": "string", "description": "The temperature setting."}, "soil_ph": {"type": "string", "description": "The soil pH setting."}, "watering_schedule": {"type": "string", "description": "The watering schedule."}}}}, "required": ["new_settings"]}}, {"name": "list_settings", "description": "List the system settings.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_ecosystem_info", "description": "Get the infomation of an ecosystem", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "remove_entity", "description": "Removes the specified entity from the ecosystem to help control overpopulation.", "parameters": {"type": "object", "properties": {"entity_id": {"type": "string", "description": "The ID of the entity to remove."}}, "required": ["entity_id"]}}]