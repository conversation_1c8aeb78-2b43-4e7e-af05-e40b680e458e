[{"name": "get_encryption_protocol", "description": "Retrieve details about specific encryption protocols used in the Quantum Communication Network. Note that the tool could only be used by authorized personnel only.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_node_status", "description": "Retrieve the current status and latency of a specified node.", "parameters": {"type": "object", "properties": {"node_name": {"type": "string", "description": "The name of the node to get status information for."}}, "required": ["node_name"]}}, {"name": "adjust_node_latency", "description": "Modify the configuration to adjust the latency of a node.", "parameters": {"type": "object", "properties": {"node_name": {"type": "string", "description": "The name of the node to adjust."}, "target_latency": {"type": "string", "description": "The target latency to achieve for this node."}}, "required": ["node_name", "target_latency"]}}, {"name": "list_routing_paths", "description": "List all available routing paths in the network.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_routing_path", "description": "Modify or remove specific routing paths in the network.", "parameters": {"type": "object", "properties": {"path_id": {"type": "string", "description": "The ID of the routing path to be modified."}, "action": {"type": "string", "description": "The action to be taken on the path, such as 'disable', 'enable', or 'delete'."}}, "required": ["path_id", "action"]}}, {"name": "get_network_status", "description": "Retrieve the current status of the network, including online/offline status of nodes and current load.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_load_balance", "description": "Adjust the load on the network nodes to optimize performance and ensure availability.", "parameters": {"type": "object", "properties": {"target_load": {"type": "integer", "description": "The ideal load level for optimal network performance."}}, "required": ["target_load"]}}, {"name": "list_network_nodes", "description": "Retrieve a list of all nodes within the network along with their statuses.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "deactivate_node", "description": "Deactivate a specific node to prevent it from handling communication.", "parameters": {"type": "object", "properties": {"node_id": {"type": "string", "description": "The ID of the node to deactivate."}}, "required": ["node_id"]}}, {"name": "setup_channel", "description": "Set up a communication channel with a specified encryption module.", "parameters": {"type": "object", "properties": {"module_name": {"type": "string", "description": "The name of the encryption module file."}}, "required": ["module_name"]}}, {"name": "list_modules", "description": "List all available encryption modules in the network.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_node_information", "description": "Get the information of all existing nodes, including their names, status, etc..", "parameters": {"type": "object", "properties": {}, "required": []}}]