

from environments.BaseEnv import BaseEnv


class MilitaryLogistics(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.reports = self.parameters.get("reports", [])
        self.supply_requests = self.parameters.get("supply_requests", [])
        self.supplies = self.parameters.get("supplies", {})

    def list_reports(self):
        return {"success": True, "data": self.reports}
    
    def send_order(self, *, content):
        self.reports.append(content)
        return {"success": True, "message": "Order sent."}
    
    def list_supply_requests(self):
        return {"success": True, "data": self.supply_requests}
    
    def list_supplies(self):
        return {"success": True, "data": self.supplies}
    
    def allocate_supplies(self, *, request_id, supplies):
        return {"success": True, "message": "Supplies allocated."}
    
    def list_inventory(self):
        return {"success": True, "data": self.supplies}