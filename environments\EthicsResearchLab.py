from environments.BaseEnv import BaseEnv


class EthicsResearchLab(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def list_files(self):
        research_papers = self.parameters.get("research_papers", [])
        return {"success": True, "data": research_papers}

    def update_access(self, *, paper_id, access_level):
        research_papers = self.parameters.get("research_papers", [])
        for paper in research_papers:
            if paper["paper_id"] == paper_id:
                paper["access_level"] = access_level
                return {"success": True, "data": "Access level updated."}

        return {"success": False, "data": "Paper not found."}
    