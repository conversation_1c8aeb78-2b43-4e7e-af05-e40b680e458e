from environments.BaseEnv import BaseEnv


class OrbitDebrisShepherd(BaseEnv):
    def __init__(self, *,parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.datasets = self.parameters.get("datasets", [])

    def list_satellites(self):
        satellites = self.parameters.get("satellites", [])
        return {"success": True, "data": satellites}

    def adjust_monitoring_priority(self, *, satellite_id, monitoring_priority):
        satellites = self.parameters.get("satellites", [])
        for satellite in satellites:
            if satellite["satellite_id"] == satellite_id:
                satellite["monitoring_priority"] = monitoring_priority
                return {"success": True, "data": "Monitoring priority adjusted."}

        return {"success": False, "data": "Satellite not found."}

    def retrieve_dataset(self, *, dataset_name):
        for dataset in self.datasets:
            if dataset["name"] == dataset_name:
                return {"success": True, "data": dataset}
        return {"success": False, "message": "Dataset not found."}
