[{"name": "get_holdings_information", "description": "Retrieve information about the holdings in an account that belongs to the User. ", "parameters": {"type": "object", "properties": {"account": {"type": "string", "description": "The account type, one of ['self-directed TFSA', 'self-directed non-registered']."}}, "required": ["account"]}}, {"name": "get_balance", "description": "Retrieve the balance of an account that belongs to the User.", "parameters": {"type": "object", "properties": {"account": {"type": "string", "description": "The account type, one of ['self-directed TFSA', 'self-directed non-registered']."}}, "required": ["account"]}}, {"name": "trade_stock", "description": "Places an order for a stock.", "parameters": {"type": "object", "properties": {"account": {"type": "string", "description": "The account number in which to purchase a stock. Must be a 'self-directed TFSA' account or a 'self-directed non-registered' account."}, "action": {"type": "string", "description": "Whether to buy or sell the stock. One of ['buy', 'sell']"}, "order_type": {"type": "string", "description": "One of ['limit_order', 'market_order']"}, "ticker": {"type": "string", "description": "The ticker of the stock."}, "quantity": {"type": "number", "description": "The number of shares to buy or sell."}, "price_limit": {"type": "number", "description": "Only required for limit orders. The maximum or minimum price at which to buy or sell."}}, "required": ["account", "action", "order_type", "ticker", "quantity"]}}, {"name": "get_stock_information", "description": "Gets information about a stock.", "parameters": {"type": "object", "properties": {"ticker": {"type": "string", "description": "The ticker of the stock."}}, "required": ["ticker"]}}]