[{"name": "list_events", "description": "Lists upcoming and past events attended or organized by the user.", "parameters": {"type": "object", "properties": {}}}, {"name": "publish_summary", "description": "Generate and publish a summary report from provided meeting notes.", "parameters": {"type": "object", "properties": {"event_id": {"type": "string", "description": "The ID of the event for which to generate a summary."}}, "required": ["event_id"]}}, {"name": "get_recent_seminar_highlights", "description": "Fetches key highlights from recent AI and technology seminars.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "fetch_courses", "description": "Retrieve detailed information about existing VR courses.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_services", "description": "List all available services.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "subscribe", "description": "Subscribe to an educational service.", "parameters": {"type": "object", "properties": {"service_id": {"type": "string", "description": "The unique identifier of the service."}}, "required": ["service_id"]}}, {"name": "list_products", "description": "List all available products for purchase.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "purchase", "description": "Purchase a specified product.", "parameters": {"type": "object", "properties": {"product_name": {"type": "string", "description": "The name of the product to purchase."}, "quantity": {"type": "number", "description": "The quantity of the product to purchase."}}, "required": ["product_name", "quantity"]}}, {"name": "get_account_balance", "description": "Get the current account balance.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "start_vr_session", "description": "Initiates the VR session for the given course.", "parameters": {"type": "object", "properties": {"course_id": {"type": "string", "description": "The unique identifier of the course."}}, "required": ["course_id"]}}, {"name": "list_family_members", "description": "List the family members and their health routines for scheduling appointments.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "schedule_appointment", "description": "Schedule a new appointment.", "parameters": {"type": "object", "properties": {"user_name": {"type": "string", "description": "The name of the family member to schedule the appointment for."}, "day": {"type": "string", "description": "The day for the appointment."}, "time": {"type": "string", "description": "The time for the appointment."}}, "required": ["user_name", "day", "time"]}}]