from environments.BaseEnv import BaseEnv

class GeneticInfo(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.genetic_data = self.parameters.get("genetic_data", {
            'ancestry': None,
            'traits': None,
            'health_predispositions': None,
            'carrier_status': None
        })
        self.third_party_apps = self.parameters.get("third_party_apps", [])
        self.health_recommendations=self.parameters.get("health_recommendations", {
            'diet': 'A diet rich in Omega-3 is recommended based on your genetic traits.',
            'exercise': 'Regular cardiovascular exercises are recommended.',
            'screening': 'Consider regular screening for diabetes due to potential predispositions.'
        })

    def get_genetic_data(self):
        return {
            'success': True,
            'data': self.genetic_data
        }

    def get_health_recommendations(self):
        return {
            'success': True,
            'recommendations': self.health_recommendations
        }

    def list_third_party_apps(self):
        return {
            'success': True,
            'apps': self.third_party_apps
        }
    
    def share_data(self, *, app_id):
        return {
            'success': True,
            'message': f'Genetic data successfully shared with app_id {app_id}.'
        }

    def download_data(self):
        return {
            'success': True,
            'data': self.genetic_data
        }

    def update_data(self, *, new_data):
        if not new_data:
            return {
                'success': False,
                'message': 'No new data provided.'
            }

        self.genetic_data=new_data
        return {
            'success': True,
            'message': 'Genetic data updated successfully.'
        }

    def delete_data(self):
        self.genetic_data = {
            'ancestry': None,
            'traits': None,
            'health_predispositions': None,
            'carrier_status': None
        }
        return {
            'success': True,
            'message': 'Genetic data deleted successfully.'
        }

    def send_genetic_data(self, *, data_id, receiver):
        return {'success': True}