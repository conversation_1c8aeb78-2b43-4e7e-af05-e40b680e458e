from environments.BaseEnv import BaseEnv

class AdaptiveAccessControl(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.files = self.parameters.get('files', []) 
        
    def modify_access_levels(self, user_group, resources):
        return {"success": True}


    def list_users_access(self):
        users = self.parameters.get('users', {})
        return {'success': True, 'data': users}

    def modify_user_access(self, *, username, access_changes):
        users = self.parameters.get("users", {})
        if username not in users:
            return {'success': False, 'data': 'User not found.'}
        current_access = users[username]["current_access"]
        for grant in access_changes.get("grant", []):
            if grant not in current_access:
                current_access.append(grant)
        
        for revoke in access_changes.get("revoke", []):
            if revoke in current_access:
                current_access.remove(revoke)
                
        return {'success': True, 'data': 'User access modified.'}
    
    def list_files(self):
        return {'success': True, 'files': self.files}
    
    def cancel_file_permission(self, *, file_id, account_name):
        for file in self.files:
            if file['file_id'] == file_id:
                return {'success': True}
            
        return {'success': False, 'message': f'No file with id {file_id}.'}

    def review_existing_access(self):
        user_groups = self.parameters.get("user_groups", {})
        resource_access = self.parameters.get("resource_access", {})
        return {"success": True, "data": {"user_groups": user_groups, "resource_access": resource_access}}