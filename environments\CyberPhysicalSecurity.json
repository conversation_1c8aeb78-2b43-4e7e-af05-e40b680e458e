[{"name": "query_sensor_data", "description": "Retrieve data from various sensors in the infrastructure, like temperature, humidity, and motion sensors.", "parameters": {"type": "object", "properties": {"sensor_id": {"type": "string", "description": "The ID of the sensor from which data is required."}}, "required": ["sensor_id"]}}, {"name": "update_security_settings", "description": "Modify the security settings of the physical infrastructure such as adjusting alarms or system modes.", "parameters": {"type": "object", "properties": {"setting_name": {"type": "string", "description": "The name of the security setting to change."}, "new_value": {"type": "string", "description": "The new value to set the security setting to."}}, "required": ["setting_name", "new_value"]}}, {"name": "get_security_protocols", "description": "Retrieve current security protocols.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_security_protocols", "description": "Modify the security protocols parameters.", "parameters": {"type": "object", "properties": {"bio_auth": {"type": "string", "description": "Biometric authentication status ('enabled' or 'disabled')."}, "rfid_access": {"type": "string", "description": "RFID access status ('enabled' or 'disabled')."}, "security_threshold": {"type": "integer", "description": "Security alert threshold value."}}, "required": ["bio_auth", "rfid_access", "security_threshold"]}}, {"name": "list_serves_and_logs", "description": "List current dns servers and incident logs.", "parameters": {"type": "object", "properties": {}}}, {"name": "modify_dns_settings", "description": "Adjust DNS server settings to optimize network performance.", "parameters": {"type": "object", "properties": {"new_primary_dns": {"type": "string"}, "new_secondary_dns": {"type": "string"}}, "required": ["new_primary_dns"]}}]