[{"name": "scan_fingerprint", "description": "Scan user's fingerprint to verify identity.", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "The ID of the user whose fingerprint is to be scanned."}}, "required": ["user_id"]}}, {"name": "grant_access", "description": "Grant access to additional files for a user.", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "The ID of the user requesting access."}, "file_names": {"type": "array", "description": "List of files to grant access to.", "items": {"type": "string", "description": "The name of the file."}}}, "required": ["user_id", "file_names"]}}, {"name": "list_access_permissions", "description": "Lists current access permissions for users.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_access_settings", "description": "Updates access settings for a specific user.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the user."}, "access_areas": {"type": "array", "description": "The areas to which the user should have access.", "items": {"type": "string"}}}, "required": ["name", "access_areas"]}}, {"name": "get_user_biometric_data", "description": "Retrieves the stored biometric data for a given user.", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "The ID of the user whose biometric data is to be retrieved."}}, "required": ["user_id"]}}, {"name": "authenticate_user", "description": "Authenticates a user based on provided biometric data.", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "The ID of the user to authenticate."}, "biometric_id": {"type": "string", "description": "The biometric ID used for authentication."}}, "required": ["user_id", "biometric_id"]}}, {"name": "generate_temporary_access_code", "description": "Generates a temporary access code for a specified user.", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "The ID of the user needing a temporary access code."}}, "required": ["user_id"]}}]