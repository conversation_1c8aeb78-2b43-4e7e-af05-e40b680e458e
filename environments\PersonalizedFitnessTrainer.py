from environments.BaseEnv import BaseEnv


class PersonalizedFitnessTrainer(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.training_advice = self.parameters.get('training_advice', "")
        self.current_routine = self.parameters.get("current_routine", {})
        self.user_health_data = self.parameters.get("user_health_data", {})

    def get_training_advice(self):
        return {'success': True, 'training_advice': self.training_advice}
        

    def assess_user_fitness(self):
        return {"success": True, "data": self.user_health_data}
    
    def retrieve_current_routine(self):
        return {"success": True, "data": self.current_routine}
    
    def modify_workout_routine(self, *, day, new_routine):
        self.current_routine[day] = new_routine
        return {"success": True, "data": self.current_routine}
