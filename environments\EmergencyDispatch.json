[{"name": "search_incidents", "description": "Search for the incidents and relevant resources at specific location.", "parameters": {"type": "object", "properties": {"location_name": {"type": "string", "description": "The name of the location."}}, "required": ["location_name"]}}, {"name": "search_location_incidents", "description": "Search for the incidents and relevant resources at specific location.", "parameters": {"type": "object", "properties": {"location_name": {"type": "string", "description": "The name of the location."}}, "required": ["location_name"]}}, {"name": "cancel_incidents", "description": "Cancel the incidents. So the relevant resources will also be recycled.", "parameters": {"type": "object", "properties": {"incident_ids": {"type": "array", "description": "The ids of the incidents.", "items": {"type": "string", "description": "The id of the incident."}}}, "required": ["incident_ids"]}}, {"name": "search_nearby_resources", "description": "Search for nearby resources for specific location.", "parameters": {"type": "object", "properties": {"location_name": {"type": "string", "description": "The name of the location."}}, "required": ["location_name"]}}, {"name": "dispatch_resources", "description": "Dispatch resources to some location.", "parameters": {"type": "object", "properties": {"resource_ids": {"type": "array", "description": "The ids of the resources.", "items": {"type": "string", "description": "The id of the resource."}}, "location_name": {"type": "string", "description": "The name of the location which needs resources."}}, "required": ["resource_ids", "location_name"]}}]