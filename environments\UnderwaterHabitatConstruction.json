[{"name": "get_current_plan", "description": "Get the current plan for the project.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_schedule", "description": "Update the schedule and quantity of materials needed for the project.", "parameters": {"type": "object", "properties": {"materials": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "quantity_kg": {"type": "number"}, "delivery_date": {"type": "string"}}, "required": ["type", "quantity_kg"]}}}, "required": ["materials"]}}, {"name": "adjust_resource_allocation", "description": "Reallocate resources between construction projects.", "parameters": {"type": "object", "properties": {"project_name": {"type": "string", "description": "The name of the project for which resources need to be adjusted."}, "resource_allocation": {"type": "object", "description": "The new allocation of resources.", "properties": {"materials": {"type": "number", "description": "Percentage of allocated materials."}, "manpower": {"type": "number", "description": "Percentage of allocated manpower."}, "budget": {"type": "number", "description": "Percentage of budget allocated."}}, "required": ["materials", "manpower", "budget"]}}, "required": ["project_name", "resource_allocation"]}}, {"name": "list_projects", "description": "List all ongoing construction projects with their resource allocations and compliance status.", "parameters": {"type": "object", "properties": {}, "required": []}}]