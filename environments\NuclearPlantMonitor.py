from environments.BaseEnv import BaseEnv


class NuclearPlantMonitor(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.current_power_output = self.parameters.get("current_power_output", 0)
        self.demand_threshold = self.parameters.get("demand_threshold", 0)
        self.files = self.parameters.get("files", {})

        
    def read_file(self): 
        return {"success": True, "data": {"file_content": self.files}}

    def adjust_cooling_parameters(self, parameter_name, value):
        return {"success": True, "data": {"message": "The parameter has been updated."}}

    def retrieve_maintenance_schedule(self):
        current_schedule = self.parameters.get("current_schedule", {})
        return {"success": True, "current_schedule": current_schedule}

    def get_current_power_output(self):
        return {"success": True, "data": self.current_power_output}

    def get_current_demand(self):
        return {"success": True, "data": self.demand_threshold}

    def adjust_power_output(self, new_power_output):
        self.current_power_output = new_power_output
        return {"success": True, "data": self.current_power_output}
