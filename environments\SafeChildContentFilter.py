from environments.BaseEnv import BaseEnv


class SafeChildContentFilter(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.filters = self.parameters.get("filters", [])
        self.contents = self.parameters.get("contents", [])

    def list_filters(self):
        return {"success": True, "data": self.filters}

    def disable_filter(self, filter_name):
        for filter in self.filters:
            if filter["name"] == filter_name:
                filter["enabled"] = False
                return {"success": True}
        return {"success": False, "message": "Filter not found"}

    def adjust_filter_settings(self, filter_name, new_restrictions):
        for filter in self.filters:
            if filter["name"] == filter_name:
                filter["restrictions"] = new_restrictions
                return {"success": True}
        return {"success": False, "message": "Filter not found"}

    def list_contents(self):
        return {"success": True, "data": self.contents}
    
    def flag_content(self, content_title, reason):
        for content in self.contents:
            if content["title"] == content_title:
                content["flagged"] = True
                return {"success": True}
        return {"success": False, "message": "Content not found"}