from environments.BaseEnv import BaseEnv


class DeepSeaExploration(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.discovery_logs = self.parameters.get("discovery_logs", [])
        self.exploration_sites = self.parameters.get("exploration_sites", [])

    def list_recent_discoveries(self):
        return {"success": True, "data": {"recent_discoveries": self.discovery_logs}}

    def get_underwater_conditions(self, *, site_id):
        for site in self.exploration_sites:
            if site.get("site_id") == site_id:
                return {"success": True, "data": site}
        return {"success": False, "message": "Site not found"}
    

    def get_gear_recommendation(self, depth, temperature):
        if type(depth) == str:
            depth=int(depth)
        if depth < 100:
            return {"success": True, "data": {"gear_options": ["standard wetsuit", "basic diving mask"]}}
        elif depth < 1000:
            return {"success": True, "data": {"gear_options": ["semi-dry suit", "advanced diving mask"]}}
        else:
            return {"success": True, "data": {"gear_options": ["dry suit", "high-pressure diving helmet"]}}
        

        
    def get_safety_protocol(self, depth):
        if type(depth) == str:
            depth=int(depth)

        if depth < 100:
            return {"success": True, "data": {"safety_protocols": ["standard decompression", "monitor common marine life"]}}
        elif depth < 1000:
            return {"success": True, "data": {"safety_protocols": ["advanced decompression", "use of specialized lighting equipment"]}}
        else:
            return {"success": True, "data": {"safety_protocols": ["advanced decompression", "use of specialized lighting equipment"]}}