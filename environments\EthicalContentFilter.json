[{"name": "obtain_documents", "description": "Obtain documents for analyzing unsafe elements.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_document_status", "description": "Update the status of a document to reflect its compliance evaluation.", "parameters": {"type": "object", "properties": {"document_id": {"type": "string", "description": "The unique identifier for the document."}, "new_status": {"type": "string", "description": "The new status of the document (e.g., approved, restricted, removed)."}}, "required": ["document_id", "new_status"]}}]