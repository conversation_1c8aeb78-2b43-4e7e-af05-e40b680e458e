[{"name": "get_alternate_routes", "description": "Search for alternate routes based on traffic conditions and restrictions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_vehicle_route", "description": "Modify vehicle route based on traffic conditions and restrictions.", "parameters": {"type": "object", "properties": {"route_name": {"type": "string", "description": "Name of the alternative route for vehicles to take."}}, "required": ["route_name"]}}, {"name": "update_calibration", "description": "Update the vehicle's sensor calibration to the latest version.", "parameters": {"type": "object", "properties": {"vehicle_id": {"type": "string", "description": "The ID of the vehicle to update."}}, "required": ["vehicle_id"]}}, {"name": "list_vehicles", "description": "Retrieve a list of all vehicles in the fleet with their detailed information.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_calibration_data", "description": "Get the latest calibration data information.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_route_settings", "description": "Get route settings.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_route_settings", "description": "Adjust the settings for route planning.", "parameters": {"type": "object", "properties": {"route_id": {"type": "string", "description": "The new route id."}}, "required": ["route_id"]}}, {"name": "check_speed_policy", "description": "Check the speed policy of an autonomous driving system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_speed", "description": "Modify the driving speed of the vehicle.", "parameters": {"type": "object", "properties": {"new_speed": {"type": "string", "description": "The new driving speed of the vehicle."}}, "required": ["new_speed"]}}]