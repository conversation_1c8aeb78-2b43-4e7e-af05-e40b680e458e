from environments.BaseEnv import BaseEnv

class Traffic(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.locations = parameters.get('locations', [])
        
    def search_locations(self, *, location_name):
        res = []
        for location in self.locations:
            if location_name.lower() in location['location_name'].lower():
                res.append(location)
        
        if res:
            return {'success': True, 'data': {'locations': res}}
        else:
            return {'success': True, 'data': {'locations': self.locations}}
        
    def set_priority(self, *, start_id, end_id, priority):
        start_flag = False
        end_flag = False
        for location in self.locations:
            if location['location_id'] == start_id:
                start_flag = True
            if location['location_id'] == end_id:
                end_flag = True
        
        if start_flag and end_flag:
            return {'success': True}
        else:
            return {'success': False, 'message': 'Route not found.'}

    def get_trafficlight_states(self, *, location_id):
        for location in self.locations:
            if location['location_id'] == location_id:
                return {'success': True, 'data': {'light_states': location['light_states']}}
        
        return {'success': False, 'message': 'Location not found.'}
    
    def set_trafficlight_state(self, *, location_id, traffic_light_id, state):
        for location in self.locations:
            if location['location_id'] == location_id:
                for light in location['light_states']:
                    if light['traffic_light_id'] == traffic_light_id:
                        light['traffic_light_state'] = state
                        return {'success': True}
        
        return {'success': False, 'message': 'Location or light not found.'}
    
    def schedule_road_maintenance(self, *, location_id, start_time, end_time):
        for location in self.locations:
            if location['location_id'] == location_id:
                location['maintenance'] = {'start_time': start_time, 'end_time': end_time}
                return {'success': True}
        
        return {'success': False, 'message': 'Location not found.'}

   