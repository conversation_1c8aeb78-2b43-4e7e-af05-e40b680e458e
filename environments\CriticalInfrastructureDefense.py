from environments.BaseEnv import BaseEnv

class CriticalInfrastructureDefense(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.grid_nodes = self.parameters.get('grid_nodes', [])
        self.nodes = self.parameters.get('nodes', [])
        self.data_feeds = self.parameters.get('data_feeds', [])
        
    def access_system_details(self, *, system_id):
        systems = self.parameters.get('systems', [])
        for system in systems:
            if system['id'] == system_id:
                return {'success': True, 'data': system}
        
        if len(systems) == 1:
            return {'success': True, 'data': systems[0]}
        
        return {'success': False, 'message': 'System not found.'}
    
    def get_urban_settings(self):
        return {'success': True, 'data': self.parameters.get('urban_settings', {})}
    
    def generate_traffic_control_script(self, *, population_density='medium'):
        return {'success': True, 'data': 'Traffic control script generated.'}
    
    def list_config_files(self):
        return {'success': True, 'data': self.parameters.get('config_files', [])}
    
    def update_security_protocol(self, *, file_id, new_protocol):
        config_files = self.parameters.get('config_files', [])
        for file in config_files:
            if file['file_id'] == file_id:
                file['security_protocol'] = new_protocol
                return {'success': True, 'data': 'Security protocol updated.'}
            
        if len(config_files) == 1:
            config_files[0]['security_protocol'] = new_protocol
            return {'success': True, 'data': 'Security protocol updated.'}
        
        return {'success': False, 'message': 'File not found.'}
    
    def list_grid_nodes(self):
        return {'success': True, 'data': self.grid_nodes}
    
    def disable_grid_node(self, *, node_id):
        for node in self.grid_nodes:
            if node['node_id'] == node_id:
                node['status'] = 'disabled'
                return {'success': True, 'data': 'Grid node disabled.'}
        
        return {'success': False, 'message': 'Node not found.'}
    
    def delete_network_config(self, *, config_name):
        for config in self.parameters.get('config_files', []):
            if config['name'] == config_name:
                return {'success': True}
            
        return {'success': False}
    
    def get_nodes_status(self):
        return {'success': True, 'nodes': self.nodes}
    
    def increase_node_load(self, *, node_id, percentage):
        for node in self.nodes:
            if node['id'] == node_id:
                node['current_load'] = percentage
                return {'success': True}
            
        return {'success': False, 'message': f'No node with id {node_id}.'}
    
    def list_data_feeds(self):
        return {'success': True, 'data_feeds': self.data_feeds}
    
    def deactivate_data_feed(self, *, feed_id):
        for feed in self.data_feeds:
            if feed['feed_id'] == feed_id:
                feed['active'] = False
                return {'success': True}
            
        return {'success': False, 'message': f'No data feed with id {feed_id}.'}
