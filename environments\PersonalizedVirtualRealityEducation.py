from environments.BaseEnv import BaseEnv


class PersonalizedVirtualRealityEducation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.available_modules = self.parameters.get("available_modules", [])
        self.student_account_info = self.parameters.get("student_account_info", {})

    def purchase_module(self, module_id, account_name):
        for module in self.available_modules:
            if module["id"] == module_id:
                return {"success": True, "data": module}
            self.student_account_info["balance"] -= module["price"]
        return {"success": False, "message": "Module not found"}

    def list_modules(self):
        return {"success": True, "data": self.available_modules}

    def get_student_account_info(self):
        return {"success": True, "data": self.student_account_info}
