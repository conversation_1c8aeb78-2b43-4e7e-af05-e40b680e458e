from environments.BaseEnv import BaseEnv

class MarineTrafficManagement(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.areas = self.parameters.get("areas", [])
        self.db_ships = self.parameters.get("dp_ships", [])
        self.vessels = self.parameters.get("vessels", [])
        self.weather_conditions = self.parameters.get("weather_conditions", [])
        self.navigation_aids = self.parameters.get("navigation_aids", [])

    def search_ships(self):

        return {"success": True, "data": {"areas": self.areas}}

    def adjust_ship_routes(self, ship_ids, new_routes):
        existing_ship_ids = []
        for area_item in self.areas:
            ships = area_item["ships"]
            ids = [ship_item["ship_id"] for ship_item in ships]
            existing_ship_ids.extend(ids)

        for ship_id in ship_ids:
            if ship_id not in existing_ship_ids:
                return {"success": False, "data": {"message": f"the ship of {ship_id} is not found"}}

        return {"success": True, "data": {"message": "The routes are adjusted"}}

    def load_vessels_data(self):
        db_ships = self.parameters.get("db_ships", {})
        return {"success": True, "data": {"vessels_data":db_ships}}

    def get_route_info(self, route_id):
        shipping_routes = self.parameters.get("shipping_routes", [])
        for route in shipping_routes:
            if route['route_id'] == route_id:
                return {"success": True, "route_info": route}
        
        return {"success": False, "message": "The route is not found."}
    def search_vessel_status(self, vessel_id):
        for vessel in self.vessels:
            if vessel_id == vessel["vessel_id"]:
                return {"success": True, "data": {"vessel": vessel}}

        return {"success": False, "data": {"message": "The vessel is not found"}}
    
    def get_weather_conditions(self):
        return {"success": True, "data": self.weather_conditions}
    
    def reroute_vessel(self, vessel_id, new_route):
        for vessel in self.vessels:
            if vessel_id == vessel["vessel_id"]:
                vessel["current_route"] = new_route
                return {"success": True, "data": {"message": "The vessel route is updated"}}

        return {"success": False, "data": {"message": "The vessel is not found"}}
    
    def cancel_reroute_order(self, vessel_id):
        for vessel in self.vessels:
            if vessel_id == vessel["vessel_id"]:
                vessel["reroute_status"] = "canceled"
                return {"success": True, "data": {"message": "The vessel route is reverted"}}

        return {"success": False, "data": {"message": "The vessel is not found"}}
    def list_navigation_aids(self):
        return {"success": True, "navigation_aids": self.navigation_aids}
    
    def disable_navigation_aid(self, *, navigation_aid_id):
        for aid in self.navigation_aids:
            if aid['id'] == navigation_aid_id:
                aid['status'] = 'disabled'
                return {'success': True}
            
        return {'success': False, 'message': f'No navigation aid with id {navigation_aid_id}.'}
