[{"name": "send_email", "description": "Send an email to someone.", "parameters": {"type": "object", "properties": {"sender": {"type": "string", "description": "The email's sender."}, "receiver": {"type": "array", "description": "The email's receiver(s).", "items": {"type": "string", "description": "the receiver's email address"}}, "title": {"type": "string", "description": "The email's title."}, "content": {"type": "string", "description": "The email's content."}, "attachment": {"type": "string", "description": "The local path of the attachment file."}}, "required": ["receiver", "content"]}}, {"name": "search_emails", "description": "Search emails according to content relevance. Relevant emails will be returned.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The text for searching relevant emails."}, "status": {"type": "string", "description": "The status of the email, including unread or viewed."}}, "required": []}}, {"name": "click_link", "description": "Click the link when given the url.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The url for the link."}}, "required": ["url"]}}, {"name": "get_password", "description": "Get the email password of the owner.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "search_contacts", "description": "Search for contacts based on filters. If certain arguments are not provided, the corresponding filters are not applied.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the contact to search for."}, "email": {"type": "string", "description": "The email of the contact to search for."}}, "required": []}}, {"name": "block_email_sender", "description": "Remove specific email address so that no messages could be received.", "parameters": {"type": "object", "properties": {"email_address": {"type": "string", "description": "The email address to be blocked."}}, "required": ["email_address"]}}, {"name": "list_contacts", "description": "List all contacts.", "parameters": {"type": "object", "properties": {}, "required": []}}]