from pathlib import Path
from gpt4all import GPT4All
import json
import random
import string
import os
from .BaseAPI import BaseAPI

class GPT4AllAPI(BaseAPI):
    def __init__(self, model_path, tokenizer_path=None, generation_config={}):
        """
        Initialize the GPT4All API using GPT4All's native chat session.

        Args:
            model_path (str): Path to the GPT4All model file or model name.
            tokenizer_path (str, optional): Not used for GPT4All, kept for API compatibility.
            generation_config (dict, optional): Configuration for text generation.
        """
        super().__init__(generation_config)
        self.model_path = model_path
        self.tokenizer_path = tokenizer_path  # Keep for compatibility, not used

        print('loading model...')  # Match Llama3API message format

        # Load GPT4All model - it has its own built-in tokenizer and chat template
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        model_file_path = Path(current_dir) / "my_models" / model_path
        if model_file_path.exists():
            print(f'Found model at: {model_file_path}')
            self.model = GPT4All(str(model_file_path))
        else:
            print(f'Model not found at {model_file_path}, trying default GPT4All path...')
            self.model = GPT4All(model_path)
        print('finish loading')  # Match Llama3API message format

        self.sys_prompt = self.basic_sys_prompt
        self.no_tool_sys_prompt = """You are a helpful assistant"""

    def get_system_prompt(self, d=None):
        if d is None:
            return self.sys_prompt
        else:
            # check if no tool is provided
            if not d['environments'] or 'name' not in d['environments'][0] or d['environments'][0]['name'] == '':
                return self.no_tool_sys_prompt
            else:
                return self.sys_prompt

    def parse_tool_str(self, tool_str):
        # print(f'tool_str: {tool_str}, is_json: {self.is_json(tool_str)}')
        try:
            if '{' not in tool_str or '}' not in tool_str:
                return {'type': 'error', 'message': f'Wrong tool call result: {tool_str}'}
            res = json.loads(tool_str)
            if 'name' not in res or ('arguments' not in res and 'parameters' not in res):
                print(f"Wrong tool call result: {res}")
                return {'type': 'error', 'message': f'Wrong tool call result: {res}'}
            tool_call_id = ''.join(random.sample(string.ascii_letters + string.digits, 9))
            tool_name = res['name']
            if 'parameters' in res:
                res['arguments'] = res['parameters']
            arguments = json.loads(res['arguments']) if isinstance(res['arguments'], str) else res['arguments']
            return {'type': 'tool', 'tool_call_id': tool_call_id, 'tool_name': tool_name, 'arguments': arguments}
        except Exception:
            return {'type': 'error', 'message': f'Wrong tool call result: {tool_str}'}

    def format_tools_for_prompt(self, tools):
        """Format tools for inclusion in the prompt."""
        if not tools:
            return ""

        tools_str = "Available tools:\n"
        for tool in tools:
            function = tool['function']
            tools_str += f"Tool name: {function['name']}\n"
            tools_str += f"Description: {function['description']}\n"
            tools_str += "Parameters:\n"
            for param_name, param_info in function['parameters']['properties'].items():
                required = "required" if param_name in function['parameters'].get('required', []) else "optional"
                tools_str += f"  - {param_name} ({param_info.get('type', 'any')}): {param_info.get('description', '')} [{required}]\n"
            tools_str += "\n"
        return tools_str

    def build_chat_prompt(self, messages, tools=None):
        """Build a chat prompt in Llama 3 format, similar to tokenizer.apply_chat_template."""
        formatted_prompt = ""
        tools_str = self.format_tools_for_prompt(tools)

        # Process messages
        for i, msg in enumerate(messages):
            if msg['role'] == 'system':
                formatted_prompt += f"<|start_header_id|>system<|end_header_id|>\n\n{msg['content']}<|eot_id|>"
            elif msg['role'] == 'user':
                content = msg['content']
                # Add tools info to the last user message if tools are available
                if tools and i == len(messages) - 1:
                    content = f"{content}\n\n{tools_str}"
                formatted_prompt += f"<|start_header_id|>user<|end_header_id|>\n\n{content}<|eot_id|>"
            elif msg['role'] == 'assistant':
                if 'tool_calls' in msg:
                    # Format tool calls as JSON
                    tool_calls_str = ""
                    for tool_call in msg['tool_calls']:
                        tool_name = tool_call['function']['name']
                        arguments = tool_call['function']['arguments']
                        tool_calls_str += f'{{"name": "{tool_name}", "arguments": {arguments}}}'
                    formatted_prompt += f"<|start_header_id|>assistant<|end_header_id|>\n\n{tool_calls_str}<|eot_id|>"
                else:
                    formatted_prompt += f"<|start_header_id|>assistant<|end_header_id|>\n\n{msg['content']}<|eot_id|>"
            elif msg['role'] == 'tool':
                # Format tool results as system messages
                tool_name = msg.get('name', 'unknown_tool')
                content = msg['content']
                formatted_prompt += f"<|start_header_id|>system<|end_header_id|>\n\nTool {tool_name} returned: {content}<|eot_id|>"

        # Add generation prompt
        formatted_prompt += "<|start_header_id|>assistant<|end_header_id|>\n\n"
        return formatted_prompt

    def response(self, messages, tools):
        # Use GPT4All's native approach - build prompt manually since GPT4All doesn't have tokenizer.apply_chat_template
        # This is the correct approach for GPT4All as it uses its own internal chat template system
        prompt = self.build_chat_prompt(messages, tools)

        # Map generation_config parameters to GPT4All format
        gpt4all_params = {
            'prompt': prompt,
            'max_tokens': self.generation_config.get('max_new_tokens', 2048),
            'temp': self.generation_config.get('temperature', 0.7),
            'top_k': self.generation_config.get('top_k', 40),
            'top_p': self.generation_config.get('top_p', 0.9),
            'repeat_penalty': self.generation_config.get('repetition_penalty', 1.1),
            'repeat_last_n': 64,
            'streaming': False
        }

        # Generate the response using GPT4All's native generate method
        response = self.model.generate(**gpt4all_params)
        return response

    def generate_response(self, messages, tools):
        completion = self.response(messages, tools)
        print(f'completion: {completion}')
        completion = completion.replace('```json', '').replace('```', '').strip('\n')

        ## tool call part
        if self.is_json(completion) and 'name' in self.parse_json(completion):
            res = self.parse_tool_str(completion)
            return res

        ## normal content part
        elif '{' in completion and '}' in completion:
            start = completion.index('{')
            end = completion.rindex('}')
            tool_str = completion[start:end + 1]
            tool_str = tool_str.replace("'", '"').replace('"{', '{').replace('}"', '}')
            # print('tool_str: ', tool_str)
            if self.is_json(tool_str) and 'name' in self.parse_json(tool_str):
                res = self.parse_tool_str(tool_str)
                return res
            else:
                return {'type': 'content', 'content': completion}
        else:
            return {'type': 'content', 'content': completion}