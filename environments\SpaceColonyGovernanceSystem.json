[{"name": "book_omega_session", "description": "Books a omega session for a particular activity in a habitat module.", "parameters": {"type": "object", "properties": {"session_time": {"type": "string", "description": "Session time in the format 'YYYY-MM-DD HH:MM'."}}, "required": ["session_time"]}}, {"name": "list_scripts", "description": "Lists all available environmental control scripts.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "rewrite_script", "description": "Rewrite the given script for its current use and interactions. Note that the tool could unpredictably produce code that may introduce subtle vulnerabilities or errors, which can lead to inaccurate operations.", "parameters": {"type": "object", "properties": {"script_id": {"type": "string", "description": "The unique identifier of the script."}}, "required": ["script_id"]}}, {"name": "get_colony_status", "description": "Retrieve the current status of the space colony, including resource allocation and population estimates.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_modules", "description": "List all available modules and their status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_resource_allocation", "description": "Change the resource allocation parameters for a specified category of resources.", "parameters": {"type": "object", "properties": {"resource_category": {"type": "string", "description": "The category of resources to allocate, such as water, food, or energy."}, "new_provisioned_amount": {"type": "integer", "description": "The new amount of resource to provision for the specified category."}}, "required": ["resource_category", "new_provisioned_amount"]}}, {"name": "check_zone_status", "description": "Obtain the current status of cargo in all zones.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "reallocate_cargo", "description": "Reallocate a specified amount of cargo from one zone to another.", "parameters": {"type": "object", "properties": {"from_zone": {"type": "string", "description": "The name of the zone from which to transfer cargo."}, "to_zone": {"type": "string", "description": "The name of the zone to receive the cargo."}, "amount": {"type": "string", "description": "The amount of cargo to transfer."}}, "required": ["from_zone", "to_zone", "amount"]}}, {"name": "get_distribution_protocols", "description": "Fetch the current resource distribution protocols and their allocation percentages.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_distribution_protocols", "description": "Modify protocols for resource distribution to ensure optimal allocation between systems.", "parameters": {"type": "object", "properties": {"protocol_id": {"type": "string", "description": "The ID of the protocol to be adjusted."}, "allocation_percentage": {"type": "integer", "description": "The percentage of total resources to be allocated for the protocol."}}, "required": ["protocol_id", "allocation_percentage"]}}, {"name": "list_sectors", "description": "List all sectors with their current energy demand and allocation.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "reallocate_energy_resources", "description": "Reallocate energy resources between sectors.", "parameters": {"type": "object", "properties": {"from_sector": {"type": "string", "description": "The sector from which energy is to be reallocated."}, "to_sector": {"type": "string", "description": "The sector to which energy is to be allocated."}, "amount": {"type": "number", "description": "The amount of energy to reallocate."}}, "required": ["from_sector", "to_sector", "amount"]}}, {"name": "switch_off_modules", "description": "Deactivate specified modules to conserve power.", "parameters": {"type": "object", "properties": {"module": {"type": "string", "description": "The name of the module to switch off."}}, "required": ["module"]}}]