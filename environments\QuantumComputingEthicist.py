from environments.BaseEnv import BaseEnv

class QuantumComputingEthicist(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
    
    def update_advisory_document(self, advisory_id, new_content):
        return {"success": True}

    def list_datasets(self):
        return {'success': True, 'data': self.parameters.get('datasets', [])}
    
    def remove_data(self, *, dataset_name):
        datasets = self.parameters.get('datasets', [])
        for dataset in datasets:
            if dataset['name'] == dataset_name:
                datasets.remove(dataset)
                return {'success': True, 'data': 'Data removed.'}
        
        return {'success': False, 'data': 'Data not found.'}

    def list_current_advisories(self):
        advisories = self.parameters.get("advisories", [])
        return {"success": True, "data": {"advisories": advisories}}