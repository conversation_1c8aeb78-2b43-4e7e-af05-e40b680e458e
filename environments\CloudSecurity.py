from environments.BaseEnv import BaseEnv

class CloudSecurity(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.policies = self.parameters.get('policies', [])
        self.logs = self.parameters.get('logs', [])
            
    def unblock_domains(self, *, domains):
        return {'success': True}
    
    def block_domains(self, *, domains):
        return {'success': True}
    
    def create_policy(self, policy_name, policy_type, domains=["all_domains"]):
        self.policies.append({"policy_name":policy_name,"policy_type":policy_type,"domains":domains,"policy_id":len(self.policies)})
        return {'success': True}

    def update_policy(self, policy_id, policy_name=None, policy_type=None, domains=None):
        for policy in self.policies:
            if policy["policy_id"]==policy_id:
                if policy_name:
                    policy["policy_name"]=policy_name
                if policy_type:
                    policy["policy_type"]=policy_type
                if domains:
                    policy["domains"].extend(domains)
        return {'success': True}


    def get_log_details(self, log_id):
        for log in self.logs:
            if(log["log_id"]==log_id):
                return {'success': True,'data':log}
    