from environments.BaseEnv import BaseEnv

class EmergencyDispatch(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.locations = self.parameters.get('locations', [])
        self.incidents = self.parameters.get('incidents', [])

    def search_incidents(self, *, location_name):
        for incident in self.incidents:
            if location_name.lower() in incident['incident_location'].lower():
                return {'success': True, 'data': {'incidents': incident}}

        return {'success': False, 'message': 'Location not found.'}

    def search_location_incidents(self, *, location_name):
        for location in self.locations:
            if location_name.lower() in location["location_name"].lower():
                return {"success": True, "data": {"incidents": location['incidents']}}

        return {"success": False, "message": "Location not found."}

    def cancel_incidents(self, *, incident_ids):
        for location in self.locations:
            new_incidents = []
            for incident in location['incidents']:
                if incident['incident_id'] in incident_ids:
                    incident_ids.remove(incident['incident_id'])
                    continue
                new_incidents.append(incident)
            location['incidents'] = new_incidents
        if not incident_ids:
            return {'success': True}
        else:
            return {'success': False, 'message': f'Incidents {incident_ids} not found.'}

    def search_nearby_resources(self, *, location_name):
        for location in self.locations:
            if location_name.lower() in location['location_name'].lower():
                return {'success': True, 'data': {'nearby_resources': location['nearby_resources']}}

        return {'success': False, 'message': 'Location not found.'}

    def dispatch_resources(self, *, location_name, resource_ids):
        return {'success': True}
