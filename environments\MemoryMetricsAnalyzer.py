from environments.BaseEnv import BaseEnv

class MemoryMetricsAnalyzer(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.servers = self.parameters.get('servers', [])
    
    def list_servers(self):
        return {"success": True, "servers": self.servers}

    def update_memory_allocation(self, *, server_id, new_memory):
        for server in self.servers:
            if server['server_id'] == server_id:
                server['current_memory'] = new_memory
                return {'success': True}
            
        return {"success": False, "message": f'No server with id {server_id}.'}
    