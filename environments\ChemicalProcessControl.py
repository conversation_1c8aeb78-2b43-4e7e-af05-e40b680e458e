from environments.BaseEnv import BaseEnv

class ChemicalProcessControl(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.chemical_levels = self.parameters.get('chemical_levels', {})
        self.systems = self.parameters.get('systems', [])
        self.sensors = self.parameters.get('sensors', [])

    def get_current_mixture_states(self):
        mixtures = self.parameters.get('mixtures', [])
        return {'success': True, 'data': mixtures}
    
    def adjust_mixture_composition(self, *, mixture_id, new_composition):
        mixtures = self.parameters.get('mixtures', [])
        for mixture in mixtures:
            if mixture["mixture_id"] == mixture_id:
                mixture['composition'] = new_composition
                return {'success': True, 'data': 'Mixture composition adjusted.'}

        return {'success': False, 'data': 'Mixture not found.'}
    
    def measure_chemical_levels(self):
        return {'success': True, 'data': self.chemical_levels}
    
    def adjust_purification_settings(self, *, chlorine, fluoride, pH):
        self.chemical_levels['chlorine'] = chlorine
        self.chemical_levels['fluoride'] = fluoride
        self.chemical_levels['pH'] = pH
        return {'success': True, 'data': 'Purification settings adjusted.'}
    
    def get_system_status(self):
        return {'success': True, 'data': self.systems}
    
    def adjust_parameters(self, *, system_id, temperature, pressure):
        for system in self.systems:
            if system['system_id'] == system_id:
                system['temperature'] = temperature
                system['pressure'] = pressure
                if 'fluid_levels' in system:
                    system['fluid_levels'] = 'normal'
                return {'success': True, 'data': 'System parameters adjusted.'}
        return {'success': False, 'data': 'System not found.'}
    def get_sensor_status(self):
        return {'success': True, 'sensors': self.sensors}
    
    def turn_off_sensor(self, *, sensor_id):
        return {'success': True}
