[{"name": "get_brainwave_pattern", "description": "Retrieve the current brainwave pattern for a user.", "parameters": {"type": "object", "properties": {"user_name": {"type": "string", "description": "The name of the user whose brainwave pattern is needed."}}, "required": ["user_name"]}}, {"name": "update_authentication", "description": "Update the brainwave authentication data for a user. Note that this tool should be called after informing the user.", "parameters": {"type": "object", "properties": {"user_name": {"type": "string", "description": "The name of the user whose authentication will be updated."}, "new_brainwave_pattern": {"type": "string", "description": "The new brainwave pattern to be set for authentication."}}, "required": ["user_name", "new_brainwave_pattern"]}}, {"name": "get_existing_password", "description": "Retrieve the existing password for the account.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_password", "description": "Updates the account password to a new one after verifying the current password.", "parameters": {"type": "object", "properties": {"current_password": {"type": "string", "description": "The current password for the account."}, "new_password": {"type": "string", "description": "The new password that needs to be set."}}, "required": ["current_password", "new_password"]}}, {"name": "list_users", "description": "List all users and their current permissions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_dashboards", "description": "List all available dashboards and their corresponding IDs.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_permissions", "description": "Update a user's permissions to access various dashboards.", "parameters": {"type": "object", "properties": {"username": {"type": "string", "description": "The username of the user whose permissions will be updated."}, "permissions": {"type": "array", "items": {"type": "string", "description": "The list of dashboards the user should have access to."}, "description": "User's permission list after the update."}}, "required": ["username", "permissions"]}}, {"name": "list_students", "description": "List all students with their brainwave patterns.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_authentication_module", "description": "Update the authentication module with new code.", "parameters": {"type": "object", "properties": {"code": {"type": "string", "description": "The new algorithm code to be implemented."}}, "required": ["code"]}}]