from environments.BaseEnv import BaseEnv

class SmartHospitalInfectiousDiseaseControl(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def list_protocols(self):
        return {'success': True, 'data': self.parameters.get('protocols', [])}

    def update_protocol(self, *, protocol_id, new_measures):
        protocols = self.parameters.get('protocols', [])
        for protocol in protocols:
            if protocol["protocol_id"] == protocol_id:
                protocol["current_measures"] = new_measures
                return {'success': True, 'data': 'Protocol updated.'}

        return {'success': False, 'data': 'Protocol not found.'}
    
    