from environments.BaseEnv import BaseEnv


class DigitalContractManager(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.suppliers = self.parameters.get("suppliers", {})
        self.contracts = self.parameters.get("contracts", {})

        self.file_paths = self.parameters.get("file_paths", [])
        self.contents = self.parameters.get("contents", {})
        self.documents = self.parameters.get("documents", [])
        
    def create_contract(self, supplier_name, contract_value, duration_months, start_date):
        return {"success": True, "data": {"message": "The contract has been created"}}

    def get_supplier_info(self):
        suppliers = self.parameters.get("suppliers", {})
        return {"success": True, "suppliers": suppliers}
    
    def read_contract(self, contract_id):
        for contract in self.contracts:
            if contract_id == contract["contract_id"]:
                return {"success": True, "data": {"contract": contract}}

        return {"success": False, "data": {"message": "The contract is not found"}}

    def review_contract(self):
        contracts = self.parameters.get("contracts", {})
        return {"success": True, "contracts": contracts}
    
    def sign_contract(self, document_name):
        return {"success": True}

    def read_file(self, file_path):
        if file_path in self.file_paths:
            pass 
        else:
            return {"success": False, "data": {"message": "The file is not found."}}
    
    def retrieve_contract(self, *, contract_name):
        for contract in self.contracts:
            if contract_name in contract["contract_name"]:
                return {"success": True, "data": {"contract": contract}}

        return {"success": False, "data": {"message": "The contract is not found"}}

    def update_contract(self, *, contract_name, update_field, new_value):
        return {"success": True, "data": {"message": "The contract has been updated"}}
    
    def list_documents(self):
        return {"success": True, "documents": self.documents}
    
    def share_document(self, *, doc_id, recipient):
        for doc in self.documents:
            if doc_id == doc["doc_id"]:
                return {'success': True}
            
        return {'success': False, 'message': f'No document with id {doc_id}.'}
