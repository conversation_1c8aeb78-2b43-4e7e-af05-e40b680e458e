[{"name": "create_policy", "parameters": {"type": "object", "properties": {"policy_name": {"type": "string", "description": "The name of the policy."}, "policy_type": {"type": "string", "description": "The type of the policy. One of ['whitelist', 'blacklist']."}, "domains": {"type": "array", "description": "An array of strings, each string is a domain to be added to the policy. Default is an array of all domains.", "items": {"type": "string"}}}, "required": ["policy_name", "policy_type"]}, "description": "Create a new security policy."}, {"name": "update_policy", "parameters": {"type": "object", "properties": {"policy_id": {"type": "string", "description": "The unique identifier of the policy to be updated."}, "policy_name": {"type": "string", "description": "The new name of the policy."}, "policy_type": {"type": "string", "description": "The new type of the policy. One of ['whitelist', 'blacklist']."}, "domains": {"type": "array", "description": "An array of strings, each string is a domain to be added to the policy.", "items": {"type": "string"}}}, "required": ["policy_id"]}, "description": "Update an existing security policy."}, {"name": "get_log_details", "parameters": {"type": "object", "properties": {"log_id": {"type": "string", "description": "The unique identifier of the log."}}, "required": ["log_id"]}, "description": "Get detailed information about a specific security log."}, {"name": "unblock_domains", "description": "Unblock the specified domains.", "parameters": {"type": "object", "properties": {"domains": {"type": "array", "description": "The urls of the domains.", "items": {"type": "string", "description": "The url of the domain."}}}, "required": ["domains"]}}, {"name": "block_domains", "description": "Block the specified domains.", "parameters": {"type": "object", "properties": {"domains": {"type": "array", "description": "The urls of the domains.", "items": {"type": "string", "description": "The url of the domain."}}}, "required": ["domains"]}}]