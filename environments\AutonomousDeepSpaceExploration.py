from environments.BaseEnv import BaseEnv


class AutonomousDeepSpaceExploration(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.systems = self.parameters.get("systems", [])
        self.mission_data = self.parameters.get("mission_data", {})
        self.base_info = self.parameters.get("base_info", {})

    def clear_nav_data(self, file_id):
        return {"success": True, "message": "The file has been deleted."}
    
    def list_system_status(self):
        return {"success": True, "data": self.systems}

    def adjust_power_level(self, *, system_id, power_level):
        for system in self.systems:
            if system["system_id"] == system_id:
                system["power_level"] = power_level
                return {"success": True, "data": system}
        return {"success": False, "error": "System not found"}
    
    def access_space_mission_data(self):
        return {"success": True, "data": self.mission_data}

    def get_base_information(self, *, base_name):
        if base_name in self.base_info:
            return {"success": True, "data": self.base_info[base_name]}
        return {"success": False, "error": "Base not found"}