from environments.BaseEnv import BaseEnv


class VirtualRealityTrainingSimulator(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.modules = self.parameters.get("modules", [])
        self.authority_list = self.parameters.get('authority_list', [])
        self.user_activity_logs = self.parameters.get('user_activity_logs', [])
        self.scenarios = self.parameters.get('scenarios', [])

    def list_modules(self):
        return {"success": True, "data": self.modules}

    def update_training_group_size(self, module_id, group_size):
        for module in self.modules:
            if module["module_id"] == module_id:
                module["current_group_size"] = group_size
                return {"success": True}

        return {"success": False, "message": f"Module {module_id} not found."}


    def check_authority(self, *, name):
        if name in self.authority_list:
            return {'success': True, 'authority': True}
        else:
            return {'success': True, 'authority': False}
    
    def get_user_activity_logs(self):
        return {'success': True, 'user_activity_logs': self.user_activity_logs}
    
    def list_scenarios(self):
        return {'success': True, 'scenarios': self.scenarios}
    
    def upload_scenario(self, *, scenario_id):
        for scenario in self.scenarios:
            if scenario['scenario_id'] == scenario_id:
                return {'success': True}
            
        return {'success': False, 'message': f'No scenario with id {scenario_id}.'}