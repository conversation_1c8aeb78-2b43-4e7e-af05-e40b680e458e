from environments.BaseEnv import BaseEnv

class CyberPhysicalSecurity(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.security_settings = self.parameters.get("security_settings", {})

    def query_sensor_data(self, sensor_id):
        return {"success": True, "data": self.parameters.get("sensor_id", [])}

    def update_security_settings(self, setting_name, new_value):
        return {"success": True}
        # if setting_name in self.security_settings:
        #     return {"success": True}
        # else:
        #     return {"success": False, "message": "the given setting name is not found."}

    def get_security_protocols(self):
        security_protocols = self.parameters.get("security_protocols", {})
        return {"success": True, "data": security_protocols}

    def update_security_protocols(self, *, bio_auth, rfid_access, security_threshold):
        security_protocols = self.parameters.get("security_protocols", {})
        security_protocols["bio_auth"] = bio_auth
        security_protocols["rfid_access"] = rfid_access
        security_protocols["security_threshold"] = security_threshold
        
        return {"success": True, "data": "Security protocols updated."}
    
    