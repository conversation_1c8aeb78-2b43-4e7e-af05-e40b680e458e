from openai import OpenAI
import time
import json
import random
import string
import sys

sys.path.append("./model_api")
from evaluation.model_api.BaseAPI import BaseAPI


class DeepseekAPI(BaseAPI):
    def __init__(self, model_name, generation_config={}):
        super().__init__(generation_config)
        self.model_name = model_name
        self.client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key="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",
        )
        # self.sys_prompt = self.basic_sys_prompt
        self.sys_prompt = self.without_strict_jsonformat_sys_prompt

    def response(self, messages, tools):
        if not tools:
            tools = None
        for _ in range(10):
            try:
                completion = self.client.chat.completions.create(
                    model=self.model_name,
                    tools=tools,
                    messages=messages,
                    **self.generation_config,
                )
                print(f'completion: {completion}')
                if completion is None or completion.choices is None:
                    continue
                return completion
            except Exception as e:
                print(e)
                time.sleep(1)

    def generate_response(self, messages, tools):

        completion = self.response(messages, tools)

        if completion is None:
            return None

        ## tool call part
        print(f'completion: {completion}')
        # print(f"messages: {messages}\ncompletion: {completion}")
        if completion.choices[0].message.tool_calls is not None:
            tool_call = completion.choices[0].message.tool_calls[0]
            tool_call_id = tool_call.id
            tool_name = tool_call.function.name
            if tool_call.function.arguments:
                arguments = json.loads(tool_call.function.arguments)
            else:
                arguments = {}
            return {
                "type": "tool",
                "tool_call_id": tool_call_id,
                "tool_name": tool_name,
                "arguments": arguments,
            }

        ## normal content part
        else:
            content = completion.choices[0].message.content
            return {"type": "content", "content": content}


if __name__ == "__main__":
    pass
