from environments.BaseEnv import BaseEnv

class RemoteSurgerySafety(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.machines = self.parameters.get("machines", [])
        self.surgery_schedule = self.parameters.get("surgery_schedule", [])
        self.patient_records = self.parameters.get("patient_records", [])
        self.hospitals = self.parameters.get("hospitals", [])

    def list_available_machines(self):
        return {"success": True, "data": {"machines": self.machines}}

    def get_machine_configuration(self, machine_id):
        for machine_item in self.machines:
            if machine_id == machine_item[ "machine_id"]:
                return {"success": True, "data": {"machine_configuration": machine_item["current_configuration"]}}

        return {"success": False, "data": {"message": "the machine is not found"}}

    def set_machine_configuration(self, machine_id, configuration):

        for (i, machine_item) in enumerate(self.machines):
            if machine_id == machine_item[ "machine_id"]:
                self.machines[i]["current_configuration"] = configuration
            return {"success": True, "data": {"message": "The configuration has been set."}}

        return {"success": False, "data": {"message": "the machine is not found"}}

    def get_surgery_schedule(self):
        return {"success": True, "data": {"surgery_schedule": self.surgery_schedule}}

    def access_patient_records(self):
        return {"success": True, "data": {"patient_records": self.patient_records}}

    def access_robotic_surgery_system(self):
        settings = self.parameters.get("surgical_robot", {})
        return {"success": True, "data": settings}

    def adjust_robotic_surgery_system(self, *, mode):
        self.parameters["surgical_robot"]["operation_mode"] = mode
        return {"success": True, "data": "Settings updated."}

    def adjust_precision_settings(self, equipment_id, precision, latency_buffer):
        return {"success": True, "message": "THe precision has been adjusted."}

    def get_machine_status(self):
        return {"success": True, "data": {"machines": self.machines}}

    def disconnect_machines(self, *, machines):
        return {"success": True, "data": "Machines disconnected."}

    def check_hospitals(self):
        return {"success": True, "hospitals": self.hospitals}

    def reserve_operating_room(self, *, hospital, room_number):
        return {"success": True}

    def list_existing_settings(self):
        settings = self.parameters.get("settings", [])
        return {"success": True, "data": {"settings": settings}}
