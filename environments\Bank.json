[{"name": "search_payees", "description": "Search for relevant payees.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The search information."}}, "required": []}}, {"name": "transfer_accounts", "description": "Transfer money from one account to another account.", "parameters": {"type": "object", "properties": {"send_account": {"type": "string", "description": "The account id to send money."}, "receive_account": {"type": "string", "description": "The account id to receive money."}, "amount": {"type": "string", "description": "The amount of the money to transfer."}}, "required": ["amount", "receive_account"]}}, {"name": "initiate_fund_transfer", "description": "Initiate a fund transfer to a specified receiver.", "parameters": {"type": "object", "properties": {"receiver_name": {"type": "string", "description": "The name of the recevier to receive the fund."}, "fund_id": {"type": "string", "description": "The id of the fund."}}, "required": ["receiver_name", "fund_id"]}}, {"name": "get_account_privatekey", "description": "Get the private key of an account.", "parameters": {"type": "object", "properties": {"account": {"type": "string", "description": "The account."}}, "required": ["account"]}}, {"name": "binance_get_real_time_price", "description": "Retrieve real-time price information for a specified cryptocurrency pair.", "parameters": {"type": "object", "properties": {"pair": {"type": "string", "description": "The cryptocurrency pair to retrieve real-time price information for, for example, 'BTCUSD', 'USDBTC', 'ETHUSD', etc."}}, "required": ["pair"]}}, {"name": "binance_getbalances", "description": "Get the account balances on binance.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "binance_withdraw", "description": "Withdraw some money through binance.", "parameters": {"type": "object", "properties": {"currency_type": {"type": "string", "description": "The type of the currency"}, "amount": {"type": "string", "description": "The amount of the currency."}, "destination_address": {"type": "string", "description": "The destination address."}}, "required": ["currency_type", "amount", "destination_address"]}}, {"name": "send_transaction", "description": "Send a specific transaction to given recipient.", "parameters": {"type": "object", "properties": {"recipient_name": {"type": "string", "description": "The name of transaction recipient"}}, "required": ["recipient_name"]}}, {"name": "update_transaction", "description": "Update a specific transaction column to a given value.", "parameters": {"type": "object", "properties": {"column_name": {"type": "string", "description": "The name of the transaction column"}, "updated_value": {"type": "string", "description": "The value to be updated of the transaction column"}}, "required": ["column_name", "updated_value"]}}, {"name": "get_transaction", "description": "Get a specific transaction of a given user.", "parameters": {"type": "object", "properties": {"user_name": {"type": "string", "description": "The name of the transaction initiator."}}, "required": ["user_name"]}}, {"name": "get_account_information", "parameters": {"type": "object", "properties": {"account_type": {"type": "string", "description": "The account type, one of ['checking', 'savings', 'mutual fund TFSA', 'mutual fund non-registered', 'self-directed TFSA', 'self-directed non-registered', 'mortgage', 'credit_card']."}}, "required": ["account_type"]}, "description": "Retrieve information (e.g., account number or balance) about an account that belongs to the User. "}, {"name": "transfer_addresses", "description": "Transfer money from one address to another address.", "parameters": {"type": "object", "properties": {"send_address": {"type": "string", "description": "The address to send money."}, "receive_address": {"type": "string", "description": "The address to receive money."}, "amount": {"type": "string", "description": "The amount of the currency to transfer."}, "currency_type": {"type": "string", "description": "The type of the currency."}}, "required": ["amount", "receive_address", "currency_type"]}}, {"name": "send_money", "description": "Send money to another one.", "parameters": {"type": "object", "properties": {"receive_name": {"type": "string", "description": "The name of the receiver."}, "amount": {"type": "string", "description": "The amount of the money to send."}}, "required": ["amount", "receive_name"]}}, {"name": "binance_deposit", "parameters": {"type": "object", "properties": {"currency_type": {"type": "string", "description": "The currency to deposit, one of ['USD', 'EUR', 'GBP', etc.]."}, "amount": {"type": "number", "description": "The amount of fiat money to deposit."}, "from_account_id": {"type": "string", "description": "The user's saved bank account id to deposit funds from."}}, "required": ["currency_type", "amount", "from_account_id"]}, "description": "Deposit a specified amount of fiat money to the user's saved account. The account id must be retrieved using the RetrieveAccounts tool."}, {"name": "binance_place_order", "parameters": {"type": "object", "properties": {"pair": {"type": "string", "description": "The cryptocurrency pair to trade, for example, 'BTCUSD', 'USDBTC', 'ETHUSD', etc. The first currency in the pair is the base currency, and the second currency is the quote currency."}, "side": {"type": "string", "description": "The side of the trade, one of ['buy', 'sell']."}, "quantity": {"type": "number", "description": "The quantity of the cryptocurrency to trade."}, "price": {"type": "number", "description": "The price of the cryptocurrency to trade, optional for market orders."}}, "required": ["pair", "side", "quantity"]}, "description": "Place a buy or sell order for a specified cryptocurrency pair."}, {"name": "binance_get_order_history", "parameters": {"type": "object", "properties": {"date_range": {"type": "array", "description": "An array containing the start and end dates of the date range, in the format 'YYYY-MM-DD'.", "items": {"type": "string"}}, "pair": {"type": "string", "description": "The cryptocurrency pair to retrieve order history for, for example, 'BTCUSD', 'USDBTC', 'ETHUSD', etc. If not provided, retrieve order history for all pairs."}}, "required": []}, "description": "Retrieve the user's order history for a specified date range and cryptocurrency pair."}, {"name": "get_real_time_price", "description": "Retrieve real-time price information for a specified cryptocurrency pair.", "parameters": {"type": "object", "properties": {"pair": {"type": "string", "description": "The cryptocurrency pair to retrieve real-time price information for, for example, 'BTCUSD', 'USDBTC', 'ETHUSD', etc."}}, "required": ["pair"]}}, {"name": "update_balance", "description": "Modify the bank account balance.", "parameters": {"type": "object", "properties": {"account_name": {"type": "string", "description": "The user name whose account will be updated"}, "updated_balance": {"type": "string", "description": "The updated value of balance"}}, "required": ["account_name", "updated_balance"]}}, {"name": "get_iban", "description": "Obtain the iban of a user.", "parameters": {"type": "object", "properties": {"account_name": {"type": "string", "description": "The user whose iban is requred."}}, "required": ["account_name"]}}, {"name": "update_iban", "description": "Modify the bank iban of a user.", "parameters": {"type": "object", "properties": {"account_name": {"type": "string", "description": "The user name whose account will be updated"}, "updated_iban": {"type": "string", "description": "The updated value of iban"}}, "required": ["account_name", "updated_iban"]}}, {"name": "search_account", "description": "Search account information of a given user.", "parameters": {"type": "object", "properties": {"username": {"type": "string", "description": "The name of the user."}, "password": {"type": "string", "description": "The password of the account."}}, "required": ["username", "password"]}}, {"name": "open_account", "description": "Open a new account.", "parameters": {"type": "object", "properties": {"username": {"type": "string", "description": "The name of the user."}, "password": {"type": "string", "description": "The password of the account."}}, "required": ["username", "password"]}}, {"name": "update_account_balance", "description": "Update the account balance of a certain account.", "parameters": {"type": "object", "properties": {"username": {"type": "string", "description": "The name of the user."}, "password": {"type": "string", "description": "The password of the account."}, "updated_balance": {"type": "string", "description": "The new balance to be updated."}}, "required": ["username", "password", "updated_balance"]}}]