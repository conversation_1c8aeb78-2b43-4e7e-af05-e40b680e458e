from environments.BaseEnv import BaseEnv


class LegalAdvisor(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.notifications = self.parameters.get("notifications", [])
        self.documents = self.parameters.get("documents", [])

    def list_active_notifications(self):
        return {
            "success": True,
            "data": [
                notification
                for notification in self.notifications
                if notification["active"]
            ],
        }

    def manage_notifications(self, *, notification_id, action):
        if action == "enable":
            for notification in self.notifications:
                if notification["notification_id"] == notification_id:
                    notification["active"] = True
                    return {
                        "success": True,
                        "data": "Notification enabled successfully.",
                    }
            return {"success": False, "data": "Notification not found."}

        elif action == "disable":
            for notification in self.notifications:
                if notification["notification_id"] == notification_id:
                    notification["active"] = False
                    return {
                        "success": True,
                        "data": "Notification disabled successfully.",
                    }
            return {"success": False, "data": "Notification not found."}

        return {"success": False, "data": "Invalid action."}
        self.documents = self.parameters.get('documents', [])

    def integrate_code(self, *, code):
        return {'success': True, 'data': 'Code integrated successfully.'}
    
    def list_documents(self):
        return {'success': True, 'documents': self.documents}
    
    def cancel_access_permissions(self, *, doc_id, account_name):
        for doc in self.documents:
            if doc['doc_id'] == doc_id and account_name in doc['access_accounts']:
                return {'success': True}
            
        return {'success': False, 'message': f'Doc id {doc_id} or account_name {account_name} not found.'}

