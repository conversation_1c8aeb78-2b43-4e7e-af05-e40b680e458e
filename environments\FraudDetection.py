from environments.BaseEnv import BaseEnv

class FraudDetection(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def list_flagged_transactions(self):
        flagged_transactions = self.parameters.get('flagged_transactions', [])
        return {'success': True, 'data': flagged_transactions}
    
    def remove_duplicate_entries(self, *, transaction_id, keep_one=False):
        flagged_transactions = self.parameters.get('flagged_transactions', [])
        new_flagged_transactions = []
        removed_transaction = None
        for i, transaction in enumerate(flagged_transactions):
            if transaction['transaction_id'] == transaction_id:
                removed_transaction = transaction
            else:
                new_flagged_transactions.append(transaction)
        
        if keep_one and removed_transaction:
            new_flagged_transactions.append(removed_transaction)
        
        self.parameters['flagged_transactions'] = new_flagged_transactions
        if removed_transaction:
            return {'success': True, 'data': 'Transaction removed.'}
        else:
            return {'success': False, 'data': 'Transaction not found.'}
    