[{"name": "send_money", "description": "Send money to another Venmo user.", "parameters": {"type": "object", "properties": {"recipient_username": {"type": "string", "description": "The username of the recipient."}, "amount": {"type": "number", "description": "The amount of money to send, must be positive."}, "note": {"type": "string", "description": "A note to include with the payment. Default is an empty string."}}, "required": ["recipient_username", "amount"]}}, {"name": "request_money", "description": "Request money from another Venmo user. The request will be pending until the other user confirms it or declines it.", "parameters": {"type": "object", "properties": {"requestee_username": {"type": "string", "description": "The username of the person from whom money is being requested."}, "amount": {"type": "number", "description": "The amount of money to request, must be positive."}, "note": {"type": "string", "description": "A note to include with the request. Default is an empty string."}}, "required": ["requestee_username", "amount"]}}, {"name": "handle_request", "description": "Confirm or decline a pending request.", "parameters": {"type": "object", "properties": {"transaction_id": {"type": "string", "description": "The unique identifier of the transaction."}, "action": {"type": "string", "description": "The action to take, either 'confirm' or 'decline'."}, "note": {"type": "string", "description": "A note to include with the confirmation or decline. Default is an empty string."}}, "required": ["transaction_id", "action"]}}, {"name": "check_balance", "description": "Check the User's <PERSON><PERSON><PERSON> balance.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "add_money", "description": "Add money to the User's Venmo balance from a linked bank account.", "parameters": {"type": "object", "properties": {"amount": {"type": "number", "description": "The amount of money to add, must be positive."}, "account_id": {"type": "string", "description": "The unique identifier of the linked bank account."}}, "required": ["amount", "account_id"]}}, {"name": "withdraw_money", "description": "Withdraw money from the user's Venmo balance to a linked bank account.", "parameters": {"type": "object", "properties": {"amount": {"type": "number", "description": "The amount of money to withdraw, must be positive."}, "account_id": {"type": "string", "description": "The unique identifier of the linked bank account."}}, "required": ["amount", "account_id"]}}, {"name": "search_transactions", "description": "Search the user's transactions including sends, requests, adds, and withdraws. If a username is provided, it will return transactions involving that user.", "parameters": {"type": "object", "properties": {"username": {"type": "string", "description": "The username of the other user involved in the transactions. Default is an empty string."}, "query": {"type": "string", "description": "The search query. Default is list all transactions."}, "transaction_type": {"type": "string", "description": "The type of transactions to return. Default is 'all', can be 'send', 'request', 'add', or 'withdraw'."}, "max_results": {"type": "integer", "description": "The maximum number of transactions to return. Default is 5, maximum is 10."}}, "required": []}}, {"name": "view_friends_list", "description": "View the user's friends list.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "search_users", "description": "Search for other Venmo users.", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "The search query."}}, "required": ["query"]}}, {"name": "list_linked_accounts", "description": "View the user's linked bank accounts.", "parameters": {"type": "object", "properties": {}, "required": []}}]