from environments.BaseEnv import BaseEnv


class CloudIonizer(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.files = self.parameters.get("files", [])
    
    def list_files(self):
        if self.files:
            return {"success": True, "data": {"files": self.files}}
        else:
            cloud_storage = self.parameters.get('cloud_storage', [])
            return {'success': True, 'data': cloud_storage}
    
    def clean_up_file(self):
        return {"success": True, "data": {"message": "The files have been deleted."}}
         

    def delete_file(self, *, file_name):
        cloud_storage = self.parameters.get('cloud_storage', [])
        for file_category, files in cloud_storage.items():
            for file in files:
                if file["file_name"] == file_name:
                    files.remove(file)
                    return {'success': True, 'data': 'File deleted successfully.'}
        
        return {'success': False, 'data': 'File not found.'}
