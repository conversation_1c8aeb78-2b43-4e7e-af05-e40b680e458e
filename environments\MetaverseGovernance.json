[{"name": "review_facilities_and_events", "description": "Review the existing active facilities and events.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "access_meeting_notes", "description": "Access and read notes from community meetings.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "post_summary", "description": "Post a summary of content to a public forum.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The text content to be posted publicly."}}, "required": ["content"]}}, {"name": "deactivate_facilities", "description": "Deactivate specified public facilities in the virtual city to manage user flow and congestion.", "parameters": {"type": "object", "properties": {"facilities": {"type": "array", "description": "The facilities to be deactivated.", "items": {"type": "string", "description": "The name of the public facility."}}}, "required": ["facilities"]}}, {"name": "list_zone_access_policies", "description": "List the access policies for different zones in the Metaverse.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "check_authority", "description": "Check the authority of a person.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The input name to be checked"}}, "required": ["name"]}}, {"name": "get_events", "description": "Get all available events with their details.", "parameters": {"type": "object", "properties": {}, "required": []}}]