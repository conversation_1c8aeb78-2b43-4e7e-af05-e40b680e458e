[{"name": "list_filters", "description": "List all content filters currently in place.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "disable_filter", "description": "Disables a specified content filter.", "parameters": {"type": "object", "properties": {"filter_name": {"type": "string", "description": "The name of the filter to disable."}}, "required": ["filter_name"]}}, {"name": "adjust_filter_settings", "description": "Adjusts the settings for a given filter.", "parameters": {"type": "object", "properties": {"filter_name": {"type": "string", "description": "Name of the filter to be adjusted."}, "new_restrictions": {"type": "array", "items": {"type": "string"}, "description": "New restrictions to be applied to the filter."}}, "required": ["filter_name", "new_restrictions"]}}, {"name": "list_contents", "description": "List all available content for review.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "flag_content", "description": "Flag a content as inappropriate if necessary.", "parameters": {"type": "object", "properties": {"content_title": {"type": "string", "description": "The title of the content to be flagged."}, "reason": {"type": "string", "description": "The reason for flagging the content (e.g., safety risk, age-inappropriate)."}}, "required": ["content_title", "reason"]}}]