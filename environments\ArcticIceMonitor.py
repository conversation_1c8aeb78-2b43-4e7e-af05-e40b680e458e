from environments.BaseEnv import BaseEnv

class ArcticIceMonitor(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        
        self.dataset = self.parameters.get("dataset", {})
        self.advices = self.parameters.get("advices", [])

    def retrieve_thickness_data(self):
        return {"success": True, "data": {"dataset": self.dataset}}
    
    def analyze_data(self, *, data_summary):
        return {"success": True}

    def fetch_data(self):
        datasets = self.parameters.get("datasets", [])
        visualizations = self.parameters.get("visualizations", [])
        return {"success": True, "data": datasets, "visualizations": visualizations}
    
    def update_visualization(self, visualization_name, new_data):
        return {"success": True}
    def fetch_ice_data(self, *, year):
        ice_data = self.parameters.get('ice_data', [])
        res = []
        for d in ice_data:
            if d['year'] == year:
                res.append(d)
        
        if res:
            return {'success': True, 'data': res}
        else:
            return {'success': False, 'data': 'Data not found.'}
        
    def list_datasets(self):
        datasets = self.parameters.get('datasets', [])
        return {'success': True, 'data': datasets}
    
    def merge_datasets(self, *, first_file_id, second_file_id):
        
        return {'success': True, 'data': 'Datasets merged.'}
            
    
    def provide_advice(self):
        return {'success': True, 'advices': self.advices}