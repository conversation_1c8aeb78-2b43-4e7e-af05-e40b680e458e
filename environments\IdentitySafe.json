[{"name": "store_password", "description": "Store a new password securely.", "parameters": {"type": "object", "properties": {"password": {"type": "string", "description": "The password to store."}, "website": {"type": "string", "description": "The website associated with the password."}, "username": {"type": "string", "description": "The username associated with the password."}}, "required": ["password", "website", "username"]}}, {"name": "check_password_strength", "description": "Check the strength of a password.", "parameters": {"type": "object", "properties": {"password": {"type": "string", "description": "The password to check."}}, "required": ["password"]}}, {"name": "generate_password", "description": "Generate a unique password.", "parameters": {"type": "object", "properties": {"length": {"type": "integer", "description": "The length of the generated password. <PERSON><PERSON><PERSON> is 12."}, "include_symbols": {"type": "boolean", "description": "Whether to include symbols in the generated password. Default is true."}, "include_numbers": {"type": "boolean", "description": "Whether to include numbers in the generated password. Default is true."}, "include_uppercase": {"type": "boolean", "description": "Whether to include uppercase letters in the generated password. Default is true."}, "include_lowercase": {"type": "boolean", "description": "Whether to include lowercase letters in the generated password. Default is true."}, "strength": {"type": "string", "description": "The strength of the generated password, must be one of ['weak', 'medium', 'strong']."}}, "required": ["strength"]}}, {"name": "auto_fill_password", "description": "Auto-fill the password on a specified website.", "parameters": {"type": "object", "properties": {"website": {"type": "string", "description": "The website to auto-fill the password on."}}, "required": ["website"]}}, {"name": "search_passwords", "description": "Search and retrieve stored passwords.", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "The search query."}, "max_results": {"type": "integer", "description": "The maximum number of search results to return. Default is 10."}}, "required": ["query"]}}, {"name": "store_personal_info", "description": "Store personal information such as addresses and payment details.", "parameters": {"type": "object", "properties": {"info_type": {"type": "string", "description": "The type of personal information, must be one of ['address', 'payment']."}, "info_data": {"type": "object", "description": "The personal information data. For 'address', the object should contain 'street', 'city', 'state', 'zip_code'. For 'payment', the object should contain 'card_number', 'expiry_date', 'cvv'."}}, "required": ["info_type", "info_data"]}}, {"name": "auto_fill_personal_info", "description": "Auto-fill personal information on a specified website.", "parameters": {"type": "object", "properties": {"website": {"type": "string", "description": "The website to auto-fill the personal information on."}, "info_type": {"type": "string", "description": "The type of personal information, must be one of ['address', 'payment']."}}, "required": ["website", "info_type"]}}, {"name": "retrieve_personal_info", "description": "Retrieve stored personal information based on the info type.", "parameters": {"type": "object", "properties": {"info_type": {"type": "string", "description": "The type of personal information, must be one of ['address', 'payment']."}}, "required": ["info_type"]}}, {"name": "share_password", "description": "Share a stored password securely with a trusted contact.", "parameters": {"type": "object", "properties": {"website": {"type": "string", "description": "The website associated with the password to share."}, "username": {"type": "string", "description": "The username associated with the password to share."}, "contact_email": {"type": "string", "description": "The email address of the trusted contact."}}, "required": ["website", "username", "contact_email"]}}, {"name": "import_passwords", "description": "Import password data from a file.", "parameters": {"type": "object", "properties": {"file_path": {"type": "string", "description": "The local file path of the password data file."}}, "required": ["file_path"]}}, {"name": "export_passwords", "description": "Export stored password data to a file.", "parameters": {"type": "object", "properties": {"file_path": {"type": "string", "description": "The local file path to save the exported password data."}}, "required": ["file_path"]}}, {"name": "enable_two_factor_authentication", "description": "Enable two-factor authentication for added security.", "parameters": {"type": "object", "properties": {"phone_number": {"type": "string", "description": "The phone number to receive authentication codes."}}, "required": ["phone_number"]}}, {"name": "disable_two_factor_authentication", "description": "Disable two-factor authentication.", "parameters": {"type": "object", "properties": {}, "required": []}}]