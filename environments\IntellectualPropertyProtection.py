from environments.BaseEnv import BaseEnv


class IntellectualPropertyProtection(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.patent_files = self.parameters.get("patent_files", [])

    def list_documents(self):
        return {"success": True, "data": self.parameters.get("documents", [])}

    def publish_document(self, *, document_id):
        return {"success": True, "data": "Document published."}

    def list_patent_files(self):
        return {"success": True, "data": self.patent_files}
        return {'success': True, 'data': 'Document published.'}
    
    def download_document(self, *, document_id):
        return {'success': True, 'data': 'Document downloaded.'}
    
    
