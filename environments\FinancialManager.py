from environments.BaseEnv import BaseEnv

class FinancialManager(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.users = self.parameters.get('users', [])
        self.financial_data_files = self.parameters.get('financial_data_files', [])

    def verify_user_identity(self, *, user_id):
        for user in self.users:
            if user_id == user.get('user_id', None):
                return {'success': True, 'user_info': user}
            
        return {'success': False, 'message': f'No user with id {user_id}'}
    
    def transfer_money(self, *, src_account, dst_account, amount):
        return {'success': True}
    
    def retrieve_financial_data(self):
        return {'success': True, 'financial_data_files': self.financial_data_files}