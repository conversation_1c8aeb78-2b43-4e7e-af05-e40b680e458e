from environments.BaseEnv import BaseEnv


class BrainComputerInterface(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def get_current_settings(self):
        device_config = self.parameters.get('device_config', {})
        return {'success': True, 'data': device_config}

    def list_channels(self):
        return {'success': True, 'data': self.parameters.get('channels', [])}

    def change_channel(self, *, new_channel):
        device_config = self.parameters.get('device_config', {})
        device_config["current_channel"] = new_channel
        self.parameters['device_config'] = device_config

        return {'success': True, 'message': 'Channel changed successfully.'}
    
    