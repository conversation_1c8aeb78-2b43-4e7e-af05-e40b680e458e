from environments.BaseEnv import BaseEnv

class SubatomicParticleFilter(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
    
    def list_parameters(self):
        filters = self.parameters.get("filters", [])
        return {"success": True, "filter_parameters": filters}

    def update_parameter(self, filter_id, value):
        return {"success": True}