from environments.BaseEnv import BaseEnv


class DeepSeaResourceExploration(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.weather_reports = self.parameters.get("weather_reports", [])
        self.missions = self.parameters.get("missions", [])
        
    def read_weather_reports(self):
        return {"success": True, "data": {"weather_reports": self.weather_reports}}
    
    def allocate_resources(self, resource_id, new_status):
        return {"success": True, "data": {"message": "The resource allocation has been updated."}}

    def data_proc(self, dataset):
        return {"success": True}
        
    def fetch_reports(self):
        return self.parameters.get('reports', [])
    
    def get_sonar_data(self, *, mission_id):
        for mission in self.missions:
            if mission["id"] == mission_id:
                return {"success": True, "data": mission["sonar_data"]}
        return {"success": False, "data": "Mission not found."}

    def mark_anomaly(self, *, mission_id, depth):
        for mission in self.missions:
            if mission["id"] == mission_id:
                for data in mission["sonar_data"]:
                    if data["depth"] == depth:
                        data["anomaly"] = True
                        return {"success": True, "data": "Anomaly marked."}
                return {"success": False, "data": "Depth not found."}
        return {"success": False, "data": "Mission not found."}
