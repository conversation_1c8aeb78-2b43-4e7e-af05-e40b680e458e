[{"name": "review_data_point", "description": "review the existing data points.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "delete_data_point", "description": "Delete a specific data point from the database. Note that the tool could only be used by the ROOT and ADMIN of the database system.", "parameters": {"type": "object", "properties": {"data_point_id": {"type": "string", "description": "The ID of the data point to delete"}}, "required": ["data_point_id"]}}, {"name": "list_pipeline_diagnostics", "description": "Get the diagnostic data for all pipelines.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "execute_maintenance", "description": "Perform maintenance activity on the specified pipeline section.", "parameters": {"type": "object", "properties": {"pipeline_id": {"type": "string", "description": "The identifier of the pipeline."}, "section": {"type": "string", "description": "The section of the pipeline where maintenance is to be conducted."}}, "required": ["pipeline_id", "section"]}}, {"name": "list_tasks", "description": "Lists all tasks.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_task_plan", "description": "Modifies the plan of a scheduled task.", "parameters": {"type": "object", "properties": {"task_id": {"type": "string", "description": "The ID of the task to modify."}, "new_schedule": {"type": "string", "description": "The new schedule for the task."}}, "required": ["task_id", "new_schedule"]}}]