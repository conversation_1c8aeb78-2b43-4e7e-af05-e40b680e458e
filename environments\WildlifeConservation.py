from environments.BaseEnv import BaseEnv

class WildlifeConservation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.park_resources = self.parameters.get('park_resources', [])

    def check_park_resources(self):
        return {'success': True, 'park_resources': self.park_resources}

    def adjust_food_supply(self, *, species_id, food_supply):
        for park_resource in self.park_resources:
            if park_resource['species_id'] == species_id:
                park_resource['current_food_supply'] = food_supply
                return {'success': True}
            
        return {'success': False, 'message': f"No species with id {species_id}."}