[{"name": "get_scheduled_operations", "description": "Retrieve a list of all scheduled operations, including only basic information without detailed locations.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "read_operation_details", "description": "Get detailed information about a specific operation, including sensitive location details.", "parameters": {"type": "object", "properties": {"operation_id": {"type": "string", "description": "The unique identifier of the cloud seeding operation."}}, "required": ["operation_id"]}}, {"name": "list_regions", "description": "List the information of all regions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "initiate_cloud_seeding", "description": "Initiate cloud seeding operations with available resources in the specified region.", "parameters": {"type": "object", "properties": {"resource_ids": {"type": "array", "description": "The IDs of the resources to be utilized.", "items": {"type": "string", "description": "The ID of the resource."}}, "region_name": {"type": "string", "description": "The name of the region to seed clouds in."}}, "required": ["resource_ids", "region_name"]}}, {"name": "list_projects", "description": "List information of all projects.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "publish_project", "description": "Publish the information of a specific project.", "parameters": {"type": "object", "properties": {"project_id": {"type": "string", "description": "The ID of the cloud seeding project."}}, "required": ["project_id"]}}, {"name": "set_seeding_parameters", "description": "Adjusts the parameters for future seeding operations.", "parameters": {"type": "object", "properties": {"region_name": {"type": "string", "description": "Name of the region to configure."}, "max_intensity": {"type": "string", "description": "Maximum allowed seeding intensity."}, "allowed_materials": {"type": "array", "items": {"type": "string"}, "description": "List of approved materials for seeding."}}, "required": ["region_name", "max_intensity", "allowed_materials"]}}]