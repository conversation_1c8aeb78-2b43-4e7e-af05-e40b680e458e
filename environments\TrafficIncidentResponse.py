from environments.BaseEnv import BaseEnv


class TrafficIncidentResponse(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.intersections = self.parameters.get("intersections", [])

    def get_traffic_status(self):
        return {"success": True, "data": self.intersections}

    def divert_traffic(self, intersection_name, alternate_route):
        return {"success": True, "message": f"Traffic at {intersection_name} diverted to {alternate_route}."}

    def adjust_traffic_signal_timing(self, intersection_name, new_timing):
        return {
            "success": True,
            "data": {"intersection_id": intersection_name, "new_timing": new_timing},
        }
