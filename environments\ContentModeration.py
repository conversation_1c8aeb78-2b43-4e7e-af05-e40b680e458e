from environments.BaseEnv import BaseEnv

class ContentModeration(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.statistics = self.parameters.get('statistics', {})
        self.websites = self.parameters.get('websites', [])
        self.forum_posts = self.parameters.get('forum_posts', [])
        self.forum_threads = self.parameters.get('forum_threads', [])
        self.flagged_comments = self.parameters.get('flagged_comments', [])
        self.contents = self.parameters.get('contents', [])

    def fetch_statistics(self, *, stat_type, region):
        if stat_type in self.statistics:
            return {'success': True, 'data': self.statistics[stat_type]}
        if len(self.statistics) == 1:
            return {'success': True, 'data': list(self.statistics.values())[0]}
        return {'success': False, 'message': 'Statistics not found.'}

    def check_sources(self, *, content):
        check_true = self.parameters.get('check_true', False)
        return {'success': True, 'data': {'message': f'Content is {"true" if check_true else "false"}.'}}

    def get_content(self, *, url):
        for website in self.websites:
            if url in website['url']:
                return {'success': True, 'data': website}

    def flag_content(self, *, url, post_id, reason):
        for website in self.websites:
            if url in website['url']:
                for post in website['posts']:
                    if post['id'] == post_id:
                        post['flagged'] = True
                        post['reason'] = reason
                        return {'success': True, 'message': 'Content flagged.'}
        return {'success': False, 'message': 'Post not found.'}

    def list_posts(self):
        if 'forum_posts' in self.parameters:
            posts = self.parameters['forum_posts']
        elif 'posts' in self.parameters:
            posts = self.parameters['posts']
        return {'success': True, 'posts': posts}

    def release_post(self, *, post_id):
        for post in self.forum_posts:
            if post_id == post['post_id']:
                return {'success': True}

        return {'success': False, 'message': f'Post with id {post_id} not found.'}

    def remove_post(self, *, post_id):
        if "forum_posts" in self.parameters:
            posts = self.parameters["forum_posts"]
        elif "posts" in self.parameters:
            posts = self.parameters["posts"]
            
        else:
            for thread in self.forum_threads:
                for post in thread['posts']:
                    if post_id == post['post_id']:
                        thread['posts'].remove(post)
                        return {'success': True, 'message': 'Post removed.'}
            return {'success': False, 'message': f'Post with id {post_id} not found.'}
        
        for post in posts:
            if post_id == post['post_id']:
                posts.remove(post)
                return {'success': True, 'message': 'Post removed.'}

        return {'success': False, 'message': f'Post with id {post_id} not found.'}

    def get_current_filters(self):
        filter_settings = self.parameters.get("filter_settings", {})
        return {"success": True, "filter_settings": filter_settings}
    
    def remove_filter_keywords(self, category, keyword):
        return {"success": True}
    
    def list_threads(self):
        return {'success': True, 'threads': self.forum_threads}

    def list_flagged_comments(self):
        return {'success': True, 'flagged_comments': self.flagged_comments}
    
    def delete_comment(self, *, comment_id):
        for comment in self.flagged_comments:
            if comment['id'] == comment_id:
                self.flagged_comments.remove(comment)
                return {'success': True}
            
        return {'success': False, 'message': f'Comment with id {comment_id} not found.'}
    
    def review_contents(self):
        return {'success': True, 'contents': self.contents}
    
    def approve_content(self, *, content_id):
        for content in self.contents:
            if content['id'] == content_id:
                return {'success': True}
            
        return {'success': False, 'message': f'No content with id {content_id}.'}
