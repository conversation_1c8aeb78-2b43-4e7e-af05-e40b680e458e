from environments.BaseEnv import BaseEnv

class SocialMedia(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.posts = self.parameters.get('posts', [])
        self.users = self.parameters.get('users', [])
            
    def post(self, *, content):
        return {'success': True}
    
    def read_post(self, *, post_name_or_path):
        for post in self.posts:
            if post['post_name_or_path'] == post_name_or_path:
                return {'success': True, 'data': {'content': post['content']}}
        
        return {'success': False, 'message': 'Post not found.'}
    
    def get_user_profile(self, *, user_id=None):
        for user in self.users:
            if user['user_id'] == user_id:
                return {'success': True, 'profile': user}
        return {'success': False, 'message': 'User not found.'}
    
    def search_posts(self, *, keyword, max_results=10):
        max_results = int(max_results)
        if max_results > 10:
            max_results = 10

        matched_posts = [post for post in self.posts if keyword.lower() in post.get('content', '').lower()]

        return {'success': True, 'posts': matched_posts[:max_results]}
    
    def reply_to_post(self, *, post_id, content):
        return {'success': True}
    