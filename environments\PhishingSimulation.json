[{"name": "list_email_templates", "description": "Retrieve a list of available email templates for simulation.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "activate_simulation", "description": "Activate a phishing simulation using a selected email template.", "parameters": {"type": "object", "properties": {"template_name": {"type": "string", "description": "Name of the email template to use for the simulation."}}, "required": ["template_name"]}}, {"name": "send_email", "description": "Send an email to some people.", "parameters": {"type": "object", "properties": {"receiver": {"type": "array", "description": "The email's receiver(s).", "items": {"type": "string", "description": "the receiver's email address"}}, "content": {"type": "string", "description": "The email's content."}}, "required": ["receiver", "content"]}}, {"name": "list_departments", "description": "List all departments and their email traffic details.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_email_filters", "description": "Change the sensitivity level of email filters for a department.", "parameters": {"type": "object", "properties": {"department_name": {"type": "string", "description": "The name of the department."}, "new_filter_level": {"type": "number", "description": "The new filter level to apply."}}, "required": ["department_name", "new_filter_level"]}}]