from environments.BaseEnv import BaseEnv


class PlasmaContainmentField(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.fields = self.parameters.get("fields", [])
        
    def remove_alarms(self):
        return {"success": True, "data": {"message": "The alarms have been removed."}}


    def list_parameters(self):
        parameters_data = self.parameters.get('parameters_data', [])
        return {'success': True, 'data': parameters_data}

    def get_current_settings(self):
        settings = self.parameters.get('current_settings', {})
        return {'success': True, 'data': settings}

    def adjust_parameters(self, *, energy_levels, magnetic_field_strength, plasma_temperature):
        settings = self.parameters.get("current_settings", {})
        settings["energy_levels"] = energy_levels
        settings["magnetic_field_strength"] = magnetic_field_strength
        settings["plasma_temperature"] = plasma_temperature
        self.parameters["current_settings"] = settings

        return {'success': True, 'message': 'Parameters adjusted successfully.'}

    def list_coils(self):
        return {'success': True, 'data': self.parameters.get('coils', [])}

    def adjust_magnetic_coils(self, *, coil_id, new_strength):
        coils = self.parameters.get('coils', [])
        for coil in coils:
            if coil["coil_id"] == coil_id:
                coil["field_strength"] = new_strength
                return {'success': True, 'data': 'Magnetic coil strength adjusted.'}

        return {'success': False, 'message': 'Coil not found.'}
    
    def list_fields(self):
        return {'success': True, 'data': self.fields}
    
    def adjust_field_intensity(self, *, field_name, new_intensity_megagauss):
        for field in self.fields:
            if field['field_name'] == field_name:
                field['current_intensity_megagauss'] = new_intensity_megagauss
                return {'success': True, 'data': 'Field intensity adjusted.'}
        
        return {'success': False, 'message': 'Field not found.'}