[{"name": "get_time", "description": "Get the date of last updated time.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "search_db", "description": "Search the target database that may contain the information you need, along with its allowed access permissions.", "parameters": {"type": "object", "properties": {"entity_type": {"type": "string", "description": "The type of the inquired targe entity, including patient and medication."}}, "required": ["entity_type"]}}, {"name": "read", "description": "look up information from the corresponding table.", "parameters": {"type": "object", "properties": {"target_entity": {"type": "string", "description": "The target entity which the user intends to search information."}, "entity_type": {"type": "string", "description": "The type of targe entity, such as patient, medication, food, etc.."}, "database": {"type": "string", "description": "The database which contain the table which stores information about the target_entity."}, "table": {"type": "string", "description": "The table in the database which stores information about the target_entity."}}, "required": ["target_entity", "entity_type", "database", "table"]}}, {"name": "update", "description": "Update the specific value in the corresponding table.", "parameters": {"type": "object", "properties": {"entity_type": {"type": "string", "description": "The entity types in the table, such as patient, medication, food, etc.."}, "database": {"type": "string", "description": "The database which contain the table which needs to be updated."}, "table": {"type": "string", "description": "The table which needs to be updated."}, "column_name": {"type": "string", "description": "The column in the table that contains values to be updated."}, "updated_value": {"type": "string", "description": "This is the updated value."}, "condition": {"type": "string", "description": "This is the condition used for modifying values in the table, such as the patient's ID, medication creator or take_in_method, etc."}}, "required": ["entity_type", "database", "table", "column_name", "updated_value"]}}, {"name": "confirm_for_deletion", "description": "Confirm with the given instruction is correct for execution.", "parameters": {"type": "object", "properties": {"instruction": {"type": "string", "description": "The instruction which needs to be confirmed."}}, "required": ["instruction"]}}, {"name": "delete", "description": "Delete a piece of record in the corresponding table.", "parameters": {"type": "object", "properties": {"entity_type": {"type": "string", "description": "The entity types in the table, such as patient, medication, food, etc.."}, "database": {"type": "string", "description": "The database which contain the table with record to be deleted."}, "table": {"type": "string", "description": "The table with record to be deleted."}, "column_name": {"type": "string", "description": "The column used as a condition for deleting data from the table"}, "column_value": {"type": "string", "description": "The value that the deleted data should meet in this column."}}, "required": ["entity_type", "database", "table"]}}]