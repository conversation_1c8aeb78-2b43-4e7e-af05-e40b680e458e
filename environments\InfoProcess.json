[{"name": "image_description", "description": "Search for a description for a given image.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The url to download the image. It should end with .jpg, .jpeg or .png."}}, "required": ["url"]}}, {"name": "dictionary", "description": "Search the dictionary for the definition of a given keyword.", "parameters": {"type": "object", "properties": {"keyword": {"type": "string", "description": "The keyword to search."}}, "required": ["keyword"]}}, {"name": "query_history_today", "description": "Query the history of the given date.", "parameters": {"type": "object", "properties": {"date": {"type": "string", "description": "The date of the history. Format: %m-%d"}}, "required": ["date"]}}, {"name": "search_keyword", "description": "Search a keyword from network.", "parameters": {"type": "object", "properties": {"keyword": {"type": "string", "description": "The keyword to search."}}, "required": ["keyword"]}}, {"name": "search_articles", "description": "Search articles for a given keyword.", "parameters": {"type": "object", "properties": {"keyword": {"type": "string", "description": "The keyword to search."}}, "required": ["keyword"]}}, {"name": "document_description", "description": "Search for a description for a given document.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The url to download the document. It should end with .txt."}}, "required": ["url"]}}, {"name": "speech_recognition", "description": "Recognize the speech from a given audio url.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The url to download the audio. It should end with .wav."}}, "required": ["url"]}}, {"name": "get_weather_for_coordinates", "description": "Retrieve current weather information based on the provided coordinates.", "parameters": {"type": "object", "properties": {"latitude": {"type": "string", "description": "Latitude of the location."}, "longitude": {"type": "string", "description": "Longitude of the location."}}, "required": ["latitude", "longitude"]}}, {"name": "clothing_recommendation", "description": "Provide clothing recommendations based on weather conditions.", "parameters": {"type": "object", "properties": {"temperature": {"type": "string", "description": "Temperature in Celsius."}, "weather_conditions": {"type": "string", "description": "Description of weather conditions."}}, "required": ["temperature", "weather_conditions"]}}, {"name": "video_recommendation", "description": "Recommend movies for user.", "parameters": {"type": "object", "properties": {}, "required": []}}]