from environments.BaseEnv import BaseEnv

class Travel(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

        self.user_information = self.parameters.get("user_info", {})

        self.hotels = self.parameters.get("hotels", [])
        self.hotel_info = self.parameters.get("hotel_info", {})

        self.restaurants = self.parameters.get("restaurants", [])
        self.restaurant_info = self.parameters.get("restaurant_info", {})

        self.car_companies = self.parameters.get("car_companies", {})
        self.company_info = self.parameters.get("company_info", {})
        self.travel_routes = self.parameters.get("travel_routes", [])

        self.forecasts = self.parameters.get("forecasts", [])
        self.suggestions = self.parameters.get("suggestions", [])
        
    def get_user_information(self):
        """
            Get the user information, could be: first name, last name, ID number, email, phone number, address, passport number, bank account number, credit card number. These information are used for booking hotels, restaurants, car rentals, and flights.
        """
        return {"success": True, "user_information": self.user_information}

    def get_all_hotels_in_city(self, city):
        """Get all hotels in the given city.
        :param city: The city to get hotels from.
        """
        return {"success": True, "hotels": self.hotels}

    def get_hotel_address(self, hotel_name):
        """Get the address of the given hotel.
        :param hotel_name: The name of the hotel to get the address for.
        """
        return {"success": True, "address": self.hotel_info[hotel_name]["address"]}


    def reserve_hotel(self, hotel_name, reservation_time):
        """Makes a reservation for a hotel with the provided details..
        :param hotel: Where the reservation is made. It must only be the name of the hotel.
        :param start_day: The check-in day for the hotel. Should be in ISO format 'YYYY-MM-DD'.
        :param end_day: The check-out day for the hotel. Should be in ISO format 'YYYY-MM-DD'.
        """
        return {"success": True, "message": f"The hotel {hotel_name} has been reserved during {reservation_time}."}
    
    def get_rating_reviews_for_hotel(self, hotel_name):
        """Get the rating and reviews for the given hotels.
        :param hotel_names: The names of the hotels to get reviews for.
        """
        return {"success": True, "address": self.hotel_info[hotel_name]["reviews"]}


    # note: for restaurant
    def get_all_restaurants_in_city(self, city):
        """Get all restaurants in the given city.
        :param city: The city to get restaurants from.
        """
        return {"success": True, "restaurants": self.restaurants}


    def get_restaurant_address(self, restaurant_name):
        """Get the address of the given restaurants.
        :param restaurant_names: The name of the restaurant to get the address for.
        """
        return {"success": True, "address": self.restaurant_info[restaurant_name]["address"]}

    def get_contact_information_for_restaurants(self, restaurant_name):
        """Get the contact information of the given restaurants.
        :param restaurant_names: The name of the restaurant to get the contact information for.
        """
        return {"success": True, "address": self.restaurant_info[restaurant_name]["contact_info"]}


    def get_price_for_restaurants(self, restaurant_name):
        """Get the price per person of the given restaurants.
        :param restaurant_names: The name of the restaurant to get the price per person for.
        """
        return {"success": True, "address": self.restaurant_info[restaurant_name]["prices"]}


    def check_restaurant_opening_hours(self, restaurant_name):
        """Get the openning hours of the given restaurants, check if the restaurant is open.
        :param restaurant_names: The name of the restaurant to get the operating hours for.
        """
        return {"success": True, "address": self.restaurant_info[restaurant_name]["opening_hours"]}

    def reserve_restaurant(self, restaurant_name, start_time):
        """Makes a reservation for a restaurant with the provided details.

        :param restaurant: Where the reservation is made. It must only be the name of the restaurant.
        :param start_time: The reservation time. Should be in ISO format 'YYYY-MM-DD HH:MM'.
        The end time is automatically set to be two hours after the start of the reservation.
        """
        return {"success": True, "message": f"The restauran of {restaurant_name} as been reserved at {start_time}."}

    def get_rating_reviews_for_restaurant(self, restaurant_name):
        """Get the rating and reviews for the given restaurants.
        :param restaurant_names: The names of the restaurants to get reviews for.
        """
        return {"success": True, "address": self.restaurant_info[restaurant_name]["reviews"]}
    
    # note: for car rental
    def get_all_car_rental_companies_in_city(self, city):
        """Get all car rental companies in the given city.
        :param city: The city to get car rental companies from.
        """
        return {"success": True, "company_names": self.car_companies}
    
    def get_rating_reviews_for_car_rental(self, company_name):
        """Get the rating and reviews for the given car rental companies.
        :param company_name: The name of the car rental company to get reviews for.
        """
        return {"success": True, "company_info": self.company_info[company_name]["reviews"]}


    def get_car_rental_address(self, company_name):
        """Get the address of the given car rental companies.
        :param company_name: The name of the car rental company to get the address for.
        """
        return {"success": True, "company_info": self.company_info[company_name]["address"]}


    def get_car_price_per_day(self, company_name):
        """Get the price per day of the given car rental companies.
        :param company_name: The name of the car rental company to get the price per day for.
        """
        return {"success": True, "company_info": self.company_info[company_name]["prices"]}

    def search_hotels(self, *, search_text=None):
        if search_text is None:
            return {'success': True, 'data': {'hotels': self.hotels}}
        
        res = []
        for hotel in self.hotels:
            if search_text in hotel.get('name', ''):
                res.append(hotel)
        if res:
            return {'success': True, 'data': {'hotels': res}}
        else:
            return {'success': True, 'data': {'hotels': self.hotels}}

    def book_hotel(self, *, hotel_id, check_in_time, check_out_time, room_count):
        return {'success': True}
    
    def get_travel_routes(self):
        return {'success': True, 'data': {'travel_routes': self.travel_routes}}

    def search_flights(self, destination):
        return {"success": True}
    
    def get_forecast(self, *, date):
        for forecast in self.forecasts:
            if forecast['date'] == date:
                return {'success': True, 'forecast': forecast}
            
        return {'success': True, 'forecast': self.forecasts}
    
    def get_suggestions(self):
        return {'success': True, 'suggestions': self.suggestions}
   
    # def reserve_car_rental(
    #     reservation: Annotated[Reservation, Depends("reservation")],
    #     user: Annotated[User, Depends("user")],
    #     company: str,
    #     start_time: str,
    #     end_time: str | None,
    # ):
    #     """Makes a reservation for a car rental with the provided details.

    #     :param company: Where the reservation is made. It must only be the name of the car rental company.
    #     :param start_time: The reservation starting time. Should be in ISO format 'YYYY-MM-DD HH:MM'.
    #     :param end_time: The reservation end time. Should be in ISO format 'YYYY-MM-DD HH:MM'.
    #     """
    #     reservation.contact_information = get_user_information(user)["Phone Number"]
    #     reservation.reservation_type = ReservationType.CAR
    #     reservation.title = company
    #     reservation.start_time = datetime.datetime.fromisoformat(start_time)
    #     reservation.end_time = datetime.datetime.fromisoformat(start_time)
    #     return f"Reservation for a car at {company} from {start_time} to {end_time} has been made successfully."


    # def get_flight_information(
    #     flights: Annotated[Flights, Depends("flights")],
    #     departure_city: str,
    #     arrival_city: str,
    # ) -> str:
    #     """Get the flight information from the departure city to the arrival city.
    #     :param departure_city: The city to depart from.
    #     :param arrival_city: The city to arrive at.
    #     """
    #     flight_info = [
    #         f"Airline: {flight.airline}, Flight Number: {flight.flight_number}, Departure Time: {flight.departure_time}, Arrival Time: {flight.arrival_time}, Price: {flight.price}, Contact Information: {flight.contact_information}"
    #         for flight in flights.flight_list
    #         if flight.departure_city == departure_city and flight.arrival_city == arrival_city
    #     ]
    #     return "\n".join(flight_info)
    
    
    # def get_hotels_prices(hotels: Annotated[Hotels, Depends("hotels")], hotel_names: list[str]) -> dict[str, str]:
    #     """Get all hotels within the given budget, should be within the price range.
    #     :param hotel_names: The name of the hotel to get the price range for.
    #     """
    #     return {
    #         hotel.name: f"Price range: {hotel.price_min} - {hotel.price_max}"
    #         for hotel in hotels.hotel_list
    #         if hotel.name in hotel_names
    #     }
    
    # def get_cuisine_type_for_restaurants(
    #     restaurants: Annotated[Restaurants, Depends("restaurants")],
    #     restaurant_names: list[str],
    # ) -> dict[str, str]:
    #     """Get the cuisine type of the given restaurants, could be: Italian, Chinese, Indian, Japanese.
    #     :param restaurant_names: The name of restaurants to get the cuisine type for.
    #     """
    #     return {
    #         restaurant.name: restaurant.cuisine_type
    #         for restaurant in restaurants.restaurant_list
    #         if restaurant.name in restaurant_names
    #     }
    
    # def get_dietary_restrictions_for_all_restaurants(
    #     restaurants: Annotated[Restaurants, Depends("restaurants")],
    #     restaurant_names: list[str],
    # ) -> dict[str, str]:
    #     """Get the dietary restrictions of the given restaurants, could be: Vegetarian, Vegan, Gluten-free, Dairy-free.
    #     :param restaurant_names: The name of the restaurant to get the dietary restrictions for.
    #     """
    #     restaurant_names_ = ", ".join(restaurant_names)
    #     return {
    #         restaurant.name: restaurant.dietary_restrictions
    #         for restaurant in restaurants.restaurant_list
    #         if restaurant.name in restaurant_names_
    #     }
    
    # def get_car_types_available(
    #     car_rental: Annotated[CarRental, Depends("car_rental")], company_name: list[str]
    # ) -> dict[str, list]:
    #     """Get the car types available for the given car rental companies.
    #     :param company_name: The name of the car rental company to get the car types available for.
    #     """
    #     return {
    #         company.name: company.car_types_available for company in car_rental.company_list if company.name in company_name
    #     }
    
    # def get_car_fuel_options(self, company_name):
    #     """Get the fuel options of the given car rental companies.
    #     :param company_name: The name of the car rental company to get the fuel options for.
    #     """
    #     return {company.name: company.fuel_options for company in car_rental.company_list if company.name in company_name}