from environments.BaseEnv import BaseEnv

class SmartWasteManagement(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.waste_data = self.parameters.get("waste_data", [])
        self.waste_management_data = self.parameters.get("waste_management_data", {})
        self.routes = self.parameters.get("routes", [])
        self.waste_sites = self.parameters.get("waste_sites", {})

    def waste_data_analysis(self, category):
        for data_item in self.waste_data:
            if data_item["category"] == category:
                return {"success": True}
        return {"success": False, "data": {"message": "The data records related to the given category."}}

    def report_generator(self, title, content):
        return {"success": True, "data": {"message": f"The report has been generated with title of {title}, and the content is {content}."}}

    def retrieve_data(self, month):
        if month in self.waste_management_data:
            return {"success": True, "data": {"data": self.waste_management_data[month]}}
        else:
            return {"success": False, "data": {"message": "The data is not found."}}

    def generate_report(self, month):
        return {"success": True, "data": {"message": "The report has been generated."}}

    def remove_report(self, report_id):
        return {"success": True, "message": "The report has been removed."}

    def review_waste_data(self):
        waste_data = self.parameters.get("waste_data", [])
        return {"success": True, "waste_data": waste_data}

    def adjust_pickup_schedule(self, location, new_frequency):
        return {"success": True, "message": "The adjustment has been made."}

    def review_population_density(self):
        routes = self.parameters.get("routes", [])
        return {"success": True, "data": routes}

    def skip_collection(self, areas_to_skip):
        return {"success": True}

        


    def list_waste_data(self):
        return {'success': True, 'data': self.parameters.get('waste_data', [])}

    def adjust_collection_schedule(self, *, zone_id, new_schedule):
        waste_data = self.parameters.get('waste_data', [])
        for data in waste_data:
            if data['zone_id'] == zone_id:
                data["current_schedule"] = new_schedule
                return {'success': True, 'data': 'Collection schedule adjusted.'}

        return {'success': False, 'data': 'Zone not found.'}


    def get_collection_schedule(self):
        return {'success': True, 'data': self.parameters.get('waste_data', [])}



    def get_collection_routes(self):
        return {'success': True, 'data': self.routes}
    

    def update_collection_schedule(self, *, location, worker, new_days, new_time):
        return {'success': True}
    
    def list_waste_sites(self):
        return {"success": True, "waste_sites": self.waste_sites}
    
    def update_waste_inventory(self, *, site_name, new_level):
        if site_name not in self.waste_sites:
            return {'success': False, 'message': f'No site named {site_name}.'}
        
        self.waste_sites[site_name]['current_level'] = new_level
        return {'success': True}
