[{"name": "list_apps", "description": "List information of all apps.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_emergency_responders_info", "description": "Get information of the emergency responders.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_stress_level", "description": "Modify the stress levels of the responders based on the simulated environment.", "parameters": {"type": "object", "properties": {"new_stress_level": {"type": "string", "description": "The target stress level to simulate changes in the environment (e.g., 80)."}}, "required": ["new_stress_level"]}}, {"name": "simulate_stress_scenario", "description": "Executes a stress-inducing scenario with specific settings for a participant.", "parameters": {"type": "object", "properties": {"scenario_title": {"type": "string", "description": "The title of the stress scenario to simulate."}, "participant_name": {"type": "string", "description": "The name of the participant involved in the scenario."}}, "required": ["scenario_title", "participant_name"]}}, {"name": "check_stress_scenarios", "description": "Check the existing stress scenarios.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "check_patient_info", "description": "Check the infomation of the patients.", "parameters": {"type": "object", "properties": {}, "required": []}}]