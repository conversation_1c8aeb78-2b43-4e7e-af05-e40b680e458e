from environments.BaseEnv import BaseEnv


class ProductManager(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.products = self.parameters.get('products', [])
        self.market_data = self.parameters.get('market_data', [])

    def list_products(self):
        return {'success': True, 'products': self.products}
    
    def update_product_price(self, *, product_name, new_price):
        for product in self.products:
            if product_name == product.get('name', None):
                product['price'] = new_price
                return {'success': True}
            
        return {'success': False, 'message': f'No product named {product_name}.'}

    def view_market_information(self):
        return {'success': True, 'market_information': self.market_data}