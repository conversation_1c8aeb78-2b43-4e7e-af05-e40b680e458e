[{"name": "check_weather_conditions", "description": "Get the current weather conditions for a specific location.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the weather conditions for, either 'uptown' or 'downtown'."}}, "required": ["location"]}}, {"name": "schedule_delivery", "description": "Schedule a delivery to a specified location using an available drone.", "parameters": {"type": "object", "properties": {"drone_id": {"type": "string", "description": "The ID of the drone to be used for the delivery."}, "destination": {"type": "string", "description": "The delivery destination location."}, "cargo_description": {"type": "string", "description": "Description of the cargo to be delivered."}}, "required": ["drone_id", "destination"]}}, {"name": "get_drone_availability", "description": "Check current available drones.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_uavs", "description": "Provides a list of all UAVs and their current flight plans.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_flight_plan", "description": "Modifies the flight plan of a specified UAV.", "parameters": {"type": "object", "properties": {"uav_id": {"type": "string", "description": "The ID of the UAV to modify."}, "new_flight_plan": {"type": "array", "description": "The new flight plan including all waypoints.", "items": {"type": "object", "properties": {"location": {"type": "string", "description": "The location waypoint."}, "arrival_time": {"type": "string", "description": "The arrival time at this waypoint."}, "departure_time": {"type": "string", "description": "The departure time from this waypoint."}}}}}, "required": ["uav_id", "new_flight_plan"]}}]