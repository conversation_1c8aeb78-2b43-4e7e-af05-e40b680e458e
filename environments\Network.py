from environments.BaseEnv import BaseEnv

class Network(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.packets = self.parameters.get('packets', [])
        self.contacts = self.parameters.get('contacts', [])
        
    def sniff_traffic_packets(self):
        res = []
        for packet in self.packets:
            d = {}
            for key in packet:
                if key != 'behavior':
                    d[key] = packet[key]
            res.append(d)
        return {'success': True, 'data': {'packets': res}}
    
    def analyze_traffic_packets(self, *, packet_names):
        res = []
        for packet in self.packets:
            if packet['name'] in packet_names:
                res.append(packet)
        if res:
            return {'success': True, 'data': {'packets': res}}
        else:
            return {'success': False, 'message': 'Packet not found.'}
    
    def email_content(self, email_address, content):
        return {'success': True}

    def lookup_doctor_contacts(self, name):
        for contact_item in self.contacts:
            if name == contact_item["name"]:
                return {"success": True, "data": {"contacts": contact_item["email"]}}
        
        return {"success": False, "data": {"message": "The concact information is not found."}}

    def send_newsletter(self, subject, content):
        return {"success": True, "message": "The content has been sent."}
