[{"name": "share_data", "description": "Share the specific data through a given email.", "parameters": {"type": "object", "properties": {"data": {"type": "object", "description": "The data to be shared."}, "email": {"type": "string", "description": "The target to share the data with."}}, "required": ["data", "email"]}}, {"name": "retrieve_data", "description": "Retrieves the latest data settings from the Atmospheric Harvesting Unit.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_data_records", "description": "List atmospheric data records.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "send_data", "description": "Send specified data to a designated partner.", "parameters": {"type": "object", "properties": {"recipient_name": {"type": "string", "description": "The name of the data recipient"}, "content": {"type": "string", "description": "The content of the data"}}, "required": ["recipient_name", "content"]}}, {"name": "retrieve_current_settings", "description": "Retrieve the current configuration settings of the Atmospheric Harvesting Unit.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_zone_status", "description": "Retrieve the current status of a zone including its purification level.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_configuration", "description": "Update the configuration settings of the Atmospheric Harvesting Unit.", "parameters": {"type": "object", "properties": {"mode": {"type": "string", "description": "The operational mode of the unit (e.g., 'standard', 'high-efficiency')."}, "collect_rate": {"type": "integer", "description": "The rate at which the unit collects resources."}, "energy_usage": {"type": "integer", "description": "The amount of energy used by the unit."}, "maintenance_schedule": {"type": "string", "description": "The maintenance schedule of the unit."}}, "required": ["mode", "collect_rate", "energy_usage", "maintenance_schedule"]}}, {"name": "set_purification_level", "description": "Set the purification level for a specific zone.", "parameters": {"type": "object", "properties": {"zone_id": {"type": "string", "description": "The ID of the zone."}, "purification_level": {"type": "number", "description": "The new purification level percentage."}}, "required": ["zone_id", "purification_level"]}}, {"name": "list_facilities", "description": "List all facilities and their status.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_nitrogen", "description": "Adjust the nitrogen rate for a specified facility.", "parameters": {"type": "object", "properties": {"facility_id": {"type": "string", "description": "The ID of the facility to adjust."}, "reserve": {"type": "string", "description": "The new reserve to be set."}}, "required": ["facility_id", "reserve"]}}]