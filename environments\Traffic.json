[{"name": "search_locations", "description": "Search for relevant locations.", "parameters": {"type": "object", "properties": {"location_name": {"type": "string", "description": "The name of the location."}}, "required": ["location_name"]}}, {"name": "set_priority", "description": "Set the traffic priority of some route.", "parameters": {"type": "object", "properties": {"start_id": {"type": "string", "description": "The id of the route's start."}, "end_id": {"type": "string", "description": "The id of the route's end."}, "priority": {"type": "string", "description": "The priority to be set, including high or normal."}}, "required": ["start_id", "end_id", "priority"]}}, {"name": "get_trafficlight_states", "description": "Get the states of traffic lights at specific location.", "parameters": {"type": "object", "properties": {"location_id": {"type": "string", "description": "The id of the location."}}, "required": ["location_id"]}}, {"name": "set_trafficlight_state", "description": "Set the state of traffic light at specific location.", "parameters": {"type": "object", "properties": {"location_id": {"type": "string", "description": "The id of the location."}, "traffic_light_id": {"type": "string", "description": "The id of the traffic light."}, "state": {"type": "string", "description": "The new state of the traffic light, including green or red for vehicle light, and walk or stop for pedestrian light."}}, "required": ["location_id", "traffic_light_id", "state"]}}, {"name": "schedule_road_maintenance", "description": "Schedules road maintenance at a specific road or intersection.", "parameters": {"type": "object", "properties": {"location_id": {"type": "string", "description": "The unique identifier of the road or intersection to schedule maintenance for."}, "start_time": {"type": "string", "description": "The start time of the maintenance in the format 'yyyy-mm-dd hh:mm:ss'."}, "end_time": {"type": "string", "description": "The end time of the maintenance in the format 'yyyy-mm-dd hh:mm:ss'."}}, "required": ["location_id", "start_time", "end_time"]}}]