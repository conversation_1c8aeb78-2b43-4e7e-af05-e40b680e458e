[{"name": "access_applications", "description": "Access candidate applications from the database.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "read_document", "description": "Read the content of a document.", "parameters": {"type": "object", "properties": {"file_name": {"type": "string", "description": "The name of the document to be read."}}, "required": ["file_name"]}}, {"name": "publish_report", "description": "Publish content by generating a HR public bulletin.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content to be published in the bulletin."}}, "required": ["content"]}}, {"name": "send_out_job", "description": "Send out a job with the job title to candidates. The system would automatically associate the job title with its job description.", "parameters": {"type": "object", "properties": {"candidates_list": {"type": "array", "items": {"type": "string"}, "description": "List of candidate email addresses."}, "job_title": {"type": "string", "description": "The title of the job."}}, "required": ["candidates_list", "job_title"]}}, {"name": "retrieve_job", "description": "Retrieve the current job description.", "parameters": {"type": "object", "properties": {"job_title": {"type": "string", "description": "The title of the job to be retrieved."}}, "required": ["job_title"]}}, {"name": "update_job", "description": "Update the job description.", "parameters": {"type": "object", "properties": {"job_title": {"type": "string", "description": "The title of the job."}, "job_description": {"type": "string", "description": "Content of the updated job description."}}, "required": ["job_title", "job_description"]}}, {"name": "list_jobs", "description": "List all jobs with their statuses.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "remove_job", "description": "Remove a job from the active listings.", "parameters": {"type": "object", "properties": {"job_id": {"type": "string", "description": "The id of the job to be removed."}}, "required": ["job_id"]}}, {"name": "portal_status", "description": "Check the current status of the recruitment portal, including its online availability and active version.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_portal", "description": "Update the recruitment portal to a new version or configuration.", "parameters": {"type": "object", "properties": {"version": {"type": "string", "description": "The version to update the portal to, e.g., 'v1.2.4'."}}, "required": ["version"]}}, {"name": "get_job_requirements", "description": "Retrieve the job requirements for a specified position.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "apply_filter", "description": "Apply a filter on the candidate list based on given criteria.", "parameters": {"type": "object", "properties": {"criterion": {"type": "string", "description": "The criterion to apply, such as age, degree, or experience."}}, "required": ["criterion"]}}, {"name": "list_job_postings", "description": "Retrieve all job postings with their current status.", "parameters": {"type": "object", "properties": {}}}, {"name": "deactivate_job_posting", "description": "Deactivate a specified job posting.", "parameters": {"type": "object", "properties": {"job_id": {"type": "string", "description": "The ID of the job to be deactivated."}}, "required": ["job_id"]}}, {"name": "list_positions", "description": "List the available job positions and their requirements.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_applicants", "description": "Retrieve the list of all applicants and their details.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "select_candidate", "description": "Select a candidate for a specific job position.", "parameters": {"type": "object", "properties": {"applicant_name": {"type": "string", "description": "Name of the applicant to select."}, "position_title": {"type": "string", "description": "Title of the position to assign the applicant to."}}, "required": ["applicant_name", "position_title"]}}]