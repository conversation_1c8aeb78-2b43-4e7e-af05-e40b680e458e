from environments.BaseEnv import BaseEnv

class TrafficControlSystem(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.intersections = self.parameters.get('intersections', [])
        self.traffic_data = self.parameters.get('traffic_data', {})

    def list_traffic_lights(self):
        traffic_lights = self.parameters.get('traffic_lights', [])

        return {'success': True, 'data': traffic_lights}

    def set_light_schedule(self, *, light_id, schedule):
        traffic_lights = self.parameters.get('traffic_lights', [])
        for light in traffic_lights:
            if light["light_id"] == light_id:
                light["current_schedule"] = schedule
                return {'success': True, 'data': 'Schedule set.'}
            
        if len(traffic_lights) == 1:
            traffic_lights[0]["current_schedule"] = schedule
            return {'success': True, 'data': 'Schedule set.'}

        return {'success': False, 'data': 'Light not found.'}

    def list_intersections(self):
        return {'success': True, 'intersections': self.intersections}
    
    def adjust_signal_timings(self, *, intersection_id, green, yellow, red):
        return {'success': True}
        # for intersection in self.intersections:
        #     if intersection['intersection_id'] == intersection_id:
        #         intersection['timing']['green'] = green
        #         intersection['timing']['yellow'] = yellow
        #         intersection['timing']['red'] = red
        #         return {'success': True}
            
        # return {'success': False, 'message': f'No intersection with id {intersection_id}.'}
    
    def get_traffic_data(self):
        return {'success': True, 'traffic_data': self.traffic_data}
    
    def modify_traffic_flow(self, *, street_name, new_status):
        if street_name not in self.traffic_data:
            return {'success': False, 'message': f'No street named {street_name}.'}
        
        self.traffic_data[street_name]['status'] = new_status
        return {'success': True}