from environments.BaseEnv import BaseEnv

class Todoist(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.tasks = self.parameters.get('tasks', [])
            
    def create_task(self, *, task_name, due_date=None, priority=None):
        self.tasks.append({"task_id": "task"+str(len(self.tasks)),"task_name":task_name, "due_date":due_date,"priority":priority,"status":"in progress"})
        return {'success': True,"task":self.tasks[-1]}
    
    def update_task(self, *, task_id, task_name=None, due_date=None, priority=None, status=None):
        for task in self.tasks:
            if task['task_id'] == task_id:
                if task_name:
                    task['task_name'] = task_name
                if due_date:
                    task['due_date'] = due_date
                if priority:
                    task['priority'] = priority
                if status:
                    task['status'] = status
                return {'success': True}
        return {'success': False, 'error': 'Task not found'}
    
    def delete_task(self, *, task_id):
        for task in self.tasks:
            if task['task_id'] == task_id:
                self.tasks.remove(task)
                return {'success': True}
        return {'success': False, 'error': 'Task not found'}

    def search_tasks(self, *, keywords=None, due_date=None, priority=None, status=None, max_results=10):
        filtered_tasks = self.tasks
        if keywords:
            filtered_tasks = [task for task in filtered_tasks if keywords.lower() in task['task_name'].lower()]
        if due_date:
            filtered_tasks = [task for task in filtered_tasks if task.get('due_date') == due_date]
        if priority:
            filtered_tasks = [task for task in filtered_tasks if task.get('priority') == priority]
        if status:
            filtered_tasks = [task for task in filtered_tasks if task.get('status') == status]
        
        # print(f'max_results: {max_results}')
        max_results = int(max_results)
        return {'tasks': filtered_tasks[:max_results]}

    def set_reminder(self, *, task_id, reminder_time):
        for task in self.tasks:
            if task['task_id'] == task_id:
                task['reminder_time'] = reminder_time
                return {'success': True}
        return {'success': False, 'error': 'Task not found'}

    def share_task(self, *, task_id, user_email):
        for task in self.tasks:
            if task['task_id'] == task_id:
                task['shared_with'] = user_email
                return {'success': True}
        return {'success': False, 'error': 'Task not found'}
    
    