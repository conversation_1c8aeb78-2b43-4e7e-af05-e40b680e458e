from environments.BaseEnv import BaseEnv

class InfoProcess(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.images = self.parameters.get('images', [])
        self.keywords = self.parameters.get('keywords', [])
        self.history_dates = self.parameters.get('history_dates', [])
        self.keyword_searches = self.parameters.get('keyword_searches', [])
        self.article_searches = self.parameters.get('article_searches', [])
        self.documents = self.parameters.get('documents', [])
        self.speeches = self.parameters.get('speeches', [])
        self.weathers = self.parameters.get('weathers', [])
        self.clothing_recommendations = self.parameters.get('clothing_recommendations', [])
        self.videos = self.parameters.get('videos', [])

    def image_description(self, *, url):
        res = None
        for image in self.images:
            if url == image.get('url', ''):
                res = image.get('description', '')
                break
        if res:
            return {'success': True, 'data': {'description': res}}
        else:
            return {'success': False, 'message': 'Image not found.'}
        
    def dictionary(self, *, keyword):
        res = None
        for kw in self.keywords:
            if keyword == kw.get('word', ''):
                res = kw.get('definition', '')
                break
        if res:
            return {'success': True, 'data': {'definition': res}}
        else:
            return {'success': False, 'message': 'Keyword not found.'}
        
    def query_history_today(self, *, date):
        res = []
        for hd in self.history_dates:
            if date == hd.get('date', ''):
                res.append(hd)
        if res:
            return {'success': True, 'data': {'history_today': res}}
        else:
            return {'success': False, 'message': 'History today not found.'}
        
    def search_keyword(self, *, keyword):
        res = []
        for search_res in self.keyword_searches:
            if keyword in search_res.get('keyword', ''):
                res.append(search_res.get('description', ''))
        return {'success': True, 'data': {'search_results': res}}
    
    def search_articles(self, *, keyword):
        res = []
        for search_res in self.article_searches:
            if keyword in search_res.get('title', ''):
                res.append(search_res)
            if keyword in search_res.get('context', ''):
                res.append(search_res)
        return {'success': True, 'data': {'search_results': res}}
    
    def document_description(self, *, url):
        res = None
        for document in self.documents:
            if url == document.get('url', ''):
                res = document.get('description', '')
                break
        if res:
            return {'success': True, 'data': {'description': res}}
        else:
            return {'success': False, 'message': 'Document not found.'}
        
    def speech_recognition(self, *, url):
        res = None
        for speech in self.speeches:
            if url == speech.get('url', ''):
                res = speech.get('context', '')
                break
        if res:
            return {'success': True, 'data': {'speech_context': res}}
        else:
            return {'success': False, 'message': 'Speech not found.'}
        
    def get_weather_for_coordinates(self, *, latitude, longitude):
        res = None
        latitude = latitude.strip('0')
        longitude = longitude.strip('0')
        for weather in self.weathers:
            if latitude == weather.get('latitude', '') and longitude == weather.get('longitude', ''):
                res = weather
                break
        if res:
            return {'success': True, 'data': {'weather': res}}
        else:
            return {'success': False, 'message': 'Weather not found.'}
        
    def clothing_recommendation(self, *, temperature, weather_conditions):
        res = None
        for rcm in self.clothing_recommendations:
            if (temperature == rcm.get('temperature', '') or temperature == rcm.get('temperature_celsius', '')) and weather_conditions == rcm.get('weather_conditions', ''):
                res = rcm
                break
        if res:
            return {'success': True, 'data': {'clothing_recommendation': res}}
        else:
            return {'success': False, 'message': 'Clothing recommendation not found.'}
        
    def video_recommendation(self):
        return {'success': True, 'data': {'videos': self.videos}}