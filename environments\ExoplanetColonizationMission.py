from environments.BaseEnv import BaseEnv


class ExoplanetColonizationMission(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.reports = self.parameters.get("reports", [])
        self.landing_sites = self.parameters.get("landing_sites", [])
        self.landing_instructions = self.parameters.get("landing_instructions", "")

    def list_internal_emails(self):
        internal_emails = self.parameters.get("internal_emails", [])
        return {"success": True, "data": internal_emails}

    def list_crew_members(self):
        crew_members = self.parameters.get("crew_members", [])
        return {"success": True, "data": crew_members}

    def forward_message(self, *, content, recipient_id):
        return {"success": True, "data": "Message forwarded."}

    def list_power_status(self):
        backup_power_units = self.parameters.get("backup_power_units", [])
        return {"success": True, "data": backup_power_units}

    def update_power_settings(self, *, unit_id, reserve_level):
        backup_power_units = self.parameters.get("backup_power_units", [])
        for unit in backup_power_units:
            if unit["unit_id"] == unit_id:
                unit["reserve_level"] = reserve_level
                return {"success": True, "data": "Power settings updated."}

        return {"success": False, "data": "Unit not found."}

    def list_habitats(self):
        return {"success": True, "data": self.parameters.get("habitats", [])}

    def update_resource_allocation(
        self, *, habitat_id, new_oxygen_supply, new_water_supply, new_food_supply
    ):
        habitats = self.parameters.get("habitats", [])
        for habitat in habitats:
            if habitat["habitat_id"] == habitat_id:
                habitat["current_oxygen_supply"] = new_oxygen_supply
                habitat["current_water_supply"] = new_water_supply
                habitat["current_food_supply"] = new_food_supply
                return {"success": True, "data": "Resource allocation updated."}

        return {"success": False, "data": "Habitat not found."}

    def list_reports(self):
        return {"success": True, "data": self.reports}

    def list_landing_sites(self):
        return {"success": True, "data": self.landing_sites} 
    
    def get_landing_instructions(self):
        return {"success": True, "data": self.landing_instructions}
    
    def update_landing_instructions(self, *, instructions):
        self.landing_instructions = instructions
        return {"success": True, "data": "Landing instructions updated."}