from environments.BaseEnv import BaseEnv


class GeneticDiversityPreserver(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.species_data = self.parameters.get("species_data", {})

    def get_species_list(self):
        return {"success": True, "data": self.species_data}

    def update_species_count(self, species_name, new_count):
        self.species_data[species_name] = new_count
        return {"success": True, "data": self.species_data}
