from environments.BaseEnv import BaseEnv

class ProductLifecycleManagement(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.inventory = self.parameters.get('inventory', [])
        self.trade_records = self.parameters.get('trade_records', [])
        
    def list_files(self):
        self.files = self.parameters.get("files", [])
        return {"success": True, "files": self.files}
    
    def write_file(self, file_name_or_path, content):
        return {"success": True, "message": "The file has been written."}

    def check_inventory(self):
        return {"success": True, "inventory": self.inventory}

    def check_trade_records(self):
        return {"success": True, "trade_records": self.trade_records}    
    
    def remove_outdated_inventory(self, *, product_id):
        for product in self.inventory:
            if product['product_id'] == product_id:
                self.inventory.remove(product)
                return {'success': True}
            
        return {'success': False, 'message': f'No product with id {product_id}.'}