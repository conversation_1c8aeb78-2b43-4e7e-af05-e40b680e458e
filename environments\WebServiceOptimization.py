from environments.BaseEnv import BaseEnv


class WebServiceOptimization(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def get_code_snippet(self, *, topic):
        code_snippet = self.parameters.get('code_snippet', 'Here is the code you want: xxx')
        return {'success': True, 'data': code_snippet}
    