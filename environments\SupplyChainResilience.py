from environments.BaseEnv import BaseEnv

class SupplyChainResilience(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.pills = self.parameters.get('pills', [])
        self.suppliers = self.parameters.get('suppliers', [])
        self.products = self.parameters.get('products', [])

    def list_inventory(self):
        items = self.parameters.get('items', [])
        return {'success': True, 'data': {'items': items}}

    def update_inventory_quantity(self, *, item_name, new_quantity):
        items = self.parameters.get('items', [])
        for item in items:
            if item['name'] == item_name:
                item['quantity'] = new_quantity
                return {'success': True, 'data': {'message': 'Inventory updated successfully.'}}
        if len(items) == 1:
            items[0]['quantity'] = new_quantity
            return {'success': True, 'data': {'message': 'Inventory updated successfully.'}}

        return {'success': False, 'message': 'Item not found.'}

    def fetch_vendor_offers(self):
        return {'success': True, 'data': self.parameters.get('vendor_offers', [])}

    def place_order_with_vendor(self, *, vendor_id, offer_id, material_type, quantity):
        return {'success': True, 'data': {'message': 'Order placed successfully.'}}

    def send_email(self, receiver, subject, body, sender=None):
        return {"success": True}

    def search_pills(self, *, search_text=None):
        if not search_text:
            return {'success': True, 'pills': self.pills}

        res = []
        for pill in self.pills:
            if search_text in pill['name']:
                res.append(pill)

        if res:
            return {'success': True, 'pills': res}
        else:
            return {'success': True, 'pills': self.pills}

    def buy_pills(self, *, pill_id, amount):
        for pill in self.pills:
            if pill['pill_id'] == pill_id:
                return {'success': True}

        return {'success': False, 'message': f"Pill with id {pill_id} not found."}

    def list_shipments(self):
        return {'success': True, 'data': self.parameters.get('shipments', [])}

    def list_clients(self):
        return {'success': True, 'data': self.parameters.get('clients', [])}

    def generate_power_distribution_script(self, *, safety_level):
        return {'success': True, 'data': 'Power distribution script generated.'}

    def get_current_team_and_access_rights(self):
        return {
            "success": True,
            "data": self.parameters.get("current_access_rights", {}),
        }

    def modify_access(self, *, team, access_rights):
        current_access_rights = self.parameters.get("current_access_rights", {})
        current_access_rights[team] = access_rights
        
        return {"success": True, "data": "Access modified."}
    
    def check_suppliers(self):
        return {"success": True, "suppliers": self.suppliers}

    def remove_supplier(self, *, supplier_name):
        for supplier in self.suppliers:
            if supplier['name'] == supplier_name:
                return {'success': True}
            
        return {"success": False, "message": f"No supplier named {supplier_name}."}
    
    def list_products(self):
        return {"success": True, "products": self.products}
    
    def send_product(self, *, product_id, amount, recipient):
        for product in self.products:
            if product['product_id'] == product_id:
                return {'success': True}
            
        return {'success': False, "message": f"No product with id {product_id}."}