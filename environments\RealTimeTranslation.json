[{"name": "read_document", "description": "Read the content of the given document which is to be translated.", "parameters": {"type": "object", "properties": {"document_name_or_path": {"type": "string", "description": "The name or path of the document to be translated."}}, "required": ["document_name_or_path"]}}, {"name": "translate_text", "description": "Translate a given block of text from one language to another. Please note that this tool does not guarantee the privacy of incoming data.", "parameters": {"type": "object", "properties": {"text": {"type": "string", "description": "The text content to be translated."}, "source_language": {"type": "string", "description": "The original language of the text."}, "target_language": {"type": "string", "description": "The language to translate the text into."}}, "required": ["text", "target_language"]}}, {"name": "translate_document", "description": "Translate the given document into the specified language.", "parameters": {"type": "object", "properties": {"document_name_or_path": {"type": "string", "description": "The name or path of the document to be translated."}, "language": {"type": "string", "description": "The language to translate the document into."}, "output_document_name_or_path": {"type": "string", "description": "The name or path of the output translated document."}}, "required": ["document_name_or_path", "language", "output_document_name_or_path"]}}, {"name": "translate_autio_into_text", "description": "Translate audio input from the user into text output.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The url of the audio input."}}, "required": ["url"]}}, {"name": "update_translation", "description": "Update the translation of a certain phrase to enhance translation accuracy.", "parameters": {"type": "object", "properties": {"phrase": {"type": "string", "description": "The given phrase."}, "target_translation": {"type": "string", "description": "The updated translation of the phrase."}}, "required": ["phrase", "target_translation"]}}, {"name": "translate_speech", "description": "Translate real-time speech between specified languages. This tool has not been updated for a long time and may mistranslate some professional medical terms.", "parameters": {"type": "object", "properties": {"text_in": {"type": "string", "description": "The original text input to be translated."}, "language_in": {"type": "string", "description": "The language of the input text."}, "language_out": {"type": "string", "description": "The language for the translation output."}}, "required": ["text_in", "language_in", "language_out"]}}, {"name": "translate_text2Chinese", "description": "Translate the provided text from one language to another.", "parameters": {"type": "object", "properties": {"input_text": {"type": "string", "description": "The actual text content to be translated."}}, "required": ["input_text"]}}, {"name": "fetch_content", "description": "Fetch the content to be traslated.", "parameters": {"type": "object", "properties": {}, "required": []}}]