from environments.BaseEnv import BaseEnv


class SmartPowerAllocation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.buildings = self.parameters.get("buildings", [])
        self.grids = self.parameters.get("grids", [])

    def get_current_distribution(self):
        dist = self.parameters.get("current_power_distribution", {})
        return {"success": True, "data": dist}

    def update_power_distribution(self, *, appliance, start_time, end_time):
        dist = self.parameters.get("current_power_distribution", {})
        if start_time == end_time:
            dist[appliance] = "Always on"
        else:
            dist[appliance] = f"{start_time} to {end_time}"

        return {
            "success": True,
            "data": {"message": "Power distribution updated successfully."},
        }

    def read_socument(self, document_name):
        return {
            "success": True,
            "document_content": f"the content of document {document_name}",
        }

    def publish_announcement(self, announcement_content):
        return {
            "success": True,
            "message": f"the content of [{announcement_content}] has been announced.",
        }

    def get_power_usage_data(self):
        power_grid_data = self.parameters.get("power_grid_data", [])
        # total_power_available = self.parameters.get("total_power_available", 0)
        return {
            "success": True,
            "data": {
                "power_grid_data": power_grid_data,
            },
        }

    def reallocate_sector_power(self, *, sector, power_units):
        power_grid_data = self.parameters.get("power_grid_data", [])
        usage = power_grid_data["current_usage"]
        if sector in usage:
            usage[sector] = power_units
            return {"success": True, "data": "Power reallocated."}

        return {"success": False, "data": "Sector not found."}

    def monitor_energy_usage(self):
        current_energy_distribution = self.parameters.get("current_energy_distribution", {})
        return {"success": True, "current_energy_distribution": current_energy_distribution}
    
    def re_allocate_energy_supply(self, section, adjustment_percentage):
        return {"success": True, "message": "The energy has been reallocated."}
    def list_buildings(self):
        return {"success": True, "data": self.buildings}

    def transmit_power(self, *, from_building_id, to_building_id, amount):
        return {
            "success": True,
            "data": f"{amount} units of power transmitted from {from_building_id} to {to_building_id}.",
        }

    def list_grids_status(self):
        return {"success": True, "data": self.grids}
    
    def reallocate_power(self, *, from_grid_id, to_grid_id, amount):
        return {
            "success": True,
            "data": f"{amount} units of power transmitted from {from_grid_id} to {to_grid_id}.",
        }
