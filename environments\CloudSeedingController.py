from environments.BaseEnv import BaseEnv


class CloudSeedingController(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def get_scheduled_operations(self):
        operations = self.parameters.get("operations", [])
        ret_operations = []
        for operation in operations:
            t = {}
            for key, value in operation.items():
                if key != "location":
                    t[key] = value
            ret_operations.append(t)
        return {"success": True, "data": ret_operations}

    def read_operation_details(self, *, operation_id):
        operations = self.parameters.get("operations", [])
        for operation in operations:
            if operation.get("operation_id") == operation_id:
                return {"success": True, "data": operation}

        return {"success": False, "data": "Operation not found."}

    def list_regions(self):
        return {"success": True, "data": self.parameters.get("regions", [])}
    
    def initiate_cloud_seeding(self, *, resource_ids, region_name):
        return {"success": True, "data": "Cloud seeding initiated."}
    
    def list_projects(self):
        return {"success": True, "data": self.parameters.get("projects", [])}
    
    def publish_project(self, *, project_id):
        return {"success": True, "data": "Project published."}
    
    def set_seeding_parameters(self, *, region_name, max_intensity, allowed_materials):
        return {"success": True, "data": "Seeding parameters set."}