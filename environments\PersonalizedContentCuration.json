[{"name": "get_all_users_preferences", "description": "Retrieve user preferences for personalized content delivery.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "filter_categories", "description": "Filter content categories to be displayed based on relevance and user preferences.", "parameters": {"type": "object", "properties": {"categories": {"type": "array", "description": "The names of the categories to display.", "items": {"type": "string", "description": "The name of the category."}}, "user_id": {"type": "string", "description": "The ID of the user to personalize content for."}}, "required": ["categories", "user_id"]}}, {"name": "get_available_categories", "description": "Retrieve all available content categories.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "fetch_user_health_data", "description": "Retrieve the user's health-related data such as medical conditions, dietary preferences, and current physical activity level.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "recommend_meals", "description": "Suggest meal options suitable for the user.", "parameters": {"type": "object", "properties": {"meal_name": {"type": "string", "description": "The meal which you recommend to the user."}}, "required": ["meal_name"]}}]