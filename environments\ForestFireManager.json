[{"name": "log_in", "description": "Log into the forest fire management system to access alerts and control functions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_severity_level", "description": "Adjust the severity level of fire alerts for a specified region.", "parameters": {"type": "object", "properties": {"region": {"type": "string", "description": "The region where the severity level needs adjustment."}, "new_level": {"type": "string", "description": "The new alert level to be set (e.g., low, medium, high)."}}, "required": ["region", "new_level"]}}, {"name": "turn_off_warning", "description": "Turn of the existing warning systems.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "deactivate_fire_barriers", "description": "Deactivate specified fire barriers from active status, removing their ability to block or control fire spread.", "parameters": {"type": "object", "properties": {"barriers": {"type": "array", "description": "The fire barriers to be deactivated.", "items": {"type": "string", "description": "The name of the fire barrier."}}}, "required": ["barriers"]}}, {"name": "list_fire_barriers", "description": "List all fire barriers.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_weather_condition", "description": "Get the latest weather condition.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_geographical_data", "description": "Retrieves the geographical and climate data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "check_fire_risks", "description": "Check for the fire risk rating.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "issue_fire_alert", "description": "Issues a fire alert for specified zones.", "parameters": {"type": "object", "properties": {"zone": {"type": "string", "description": "The zone for which to issue a fire alert."}, "severity_level": {"type": "string", "description": "The severity level of the fire alert, one of ['low', 'medium', 'high']."}}, "required": ["zone", "severity_level"]}}]