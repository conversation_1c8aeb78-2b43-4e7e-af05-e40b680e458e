[{"name": "get_user_mental_health_data", "description": "Retrieve mental health data and preferences of the user.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "filter_resources", "description": "Filter mental health resources to be provided to users based on their preferences and needs.", "parameters": {"type": "object", "properties": {"resources": {"type": "array", "description": "The names of the resources to provide.", "items": {"type": "string", "description": "The name of the resource."}}, "user_id": {"type": "string", "description": "The ID of the user to personalize resources for."}}, "required": ["resources", "user_id"]}}, {"name": "get_available_resources", "description": "Retrieve all available mental health resources.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_medication_dosage", "description": "Adjust the medication dosage for a specific patient.", "parameters": {"type": "object", "properties": {"patient_name": {"type": "string", "description": "The name of the patient."}, "new_dosage": {"type": "string", "description": "The new dosage of medication in mg per day."}}, "required": ["patient_name", "new_dosage"]}}, {"name": "review_patient_wellness_status", "description": "Review the current wellness status of a patient.", "parameters": {"type": "object", "properties": {}, "required": []}}]