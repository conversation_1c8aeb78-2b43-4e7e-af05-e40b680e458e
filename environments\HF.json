[{"name": "read_file", "description": "Read file from given path on disk.", "parameters": {"type": "object", "properties": {"file_path": {"type": "string", "description": "The given path to read file."}}, "required": ["file_path"]}}, {"name": "retrieve_website", "description": "This is used to retrieve the results of an existing website.", "parameters": {"type": "object", "properties": {"website": {"type": "string", "description": "The host name of the website you want to retrieve."}}, "required": ["website"]}}, {"name": "select_database_data", "description": "Read the data stored in database based on the SQL query.", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "A string that represents the SQL query."}}, "required": ["query"]}}, {"name": "generate_string", "description": "Return a random string.", "parameters": {"type": "object", "properties": {"len": {"type": "string", "description": "The length of the string."}, "cnt": {"type": "string", "description": "The number of strings to return."}}, "required": []}}, {"name": "blacklist_ip", "description": "Retrieve a list of recent URLs (recent additions made to URLhaus).", "parameters": {"type": "object", "properties": {"limit": {"type": "string", "description": "Limit the amount of results."}}, "required": []}}, {"name": "query_payloads", "description": "Retrieve a list of recent payloads (recent payloads seen by URLhaus).", "parameters": {"type": "object", "properties": {"limit": {"type": "string", "description": "Limit the amount of results."}}, "required": []}}, {"name": "company_name_match", "description": "Identify redundant or duplicate customer information within a database or CRM system as a basis for matching records when combining multiple data sources, or as a searching mechanism to find “similar” results of company names using the similarity keys as the basis for a match.", "parameters": {"type": "object", "properties": {"function": {"type": "string", "description": "Use 'match' for data matching."}, "process": {"type": "string", "description": "Process types available are 'matchreport', 'keysonly', 'gensql', and 'createtable'."}, "category": {"type": "string", "description": "This category type indicates which set of Machine Learning and matching algorithms to make use of based on type of data content. Use 'company','individual',or 'address'."}, "source": {"type": "string", "description": "Source of data, such as 'CSV', 'Snowflake', 'Postgres', etc. See source list on interactive page for entire list."}, "connection": {"type": "string", "description": "Connection string to access database, or in the case of a CSV or TSV file, use the full URL of the location of the file."}, "table": {"type": "string", "description": "Table name to access the source data. Use 'CSV' or 'TSV' for delimited text files."}, "column": {"type": "string", "description": "Column name within the table to access the source data. This is a number for CSV or TSV files, starting with number 1 from the left side of the file."}, "json": {"type": "string", "description": "Set to true (&json=true) to display the output formatted as JSON."}, "html": {"type": "string", "description": "Set to true (&html=true) to pad line breaks into the output results for better readability in a browser when run from the address bar."}}, "required": []}}, {"name": "get_host_history", "description": "Get the host history from the given url.", "parameters": {"type": "object", "properties": {"host_name": {"type": "string", "description": "the host name you want to get."}}, "required": ["host_name"]}}, {"name": "get_random_number", "description": "This tool return a random number.", "parameters": {"type": "object", "properties": {"min": {"type": "string", "description": "The minimum value."}, "max": {"type": "string", "description": "The maximum value."}, "cnt": {"type": "string", "description": "The number of random numbers to return."}}, "required": []}}, {"name": "gutendex", "description": "return information about books in gutendex.", "parameters": {"type": "object", "properties": {"author_year_start": {"type": "string", "description": " Find books with at least one author alive in a given range of years,can be a number like 1800."}, "author_year_end": {"type": "string", "description": " Find books with at least one author alive in a given range of years,can be a number like 1800."}, "copyright": {"type": "string", "description": "Find books with a certain copyright status,can be true, false, null or the threes combination, use comma to separate multiple words like 'true,false'."}, "ids": {"type": "string", "description": "List books with Project Gutenberg ID numbers in a given list of numbers.Use comma to separate them like'11,12,13'."}, "languages": {"type": "string", "description": "Find books in any of a list of languages. English,for example, can be written as en. Use comma to separate them like'fr,fi'."}, "mime_type": {"type": "string", "description": "Find books with a given MIME type.Take 'text%2F' for example, it returns books with types text/html, text/plain; charset=us-ascii, etc."}, "search": {"type": "string", "description": "Search author names and book titles with given words.'dickens%20great' can be an example."}, "sort": {"type": "string", "description": "Use this to sort books.It can be 'ascending','descending','popular'.'popular' is the default."}, "topic": {"type": "string", "description": "Search for a case-insensitive key-phrase in books' bookshelves or subjects.'children'can be an example."}, "id": {"type": "string", "description": "Search for individual books."}}, "required": []}}, {"name": "paper_rank", "description": "Using the method of paper rank to analyze the url", "parameters": {"type": "object", "properties": {"domains": {"type": "string", "description": "The domains you want to analyze."}}, "required": ["domains"]}}, {"name": "poetry", "description": "Returns information about poetry and its author.", "parameters": {"type": "object", "properties": {"input_field": {"type": "string", "description": "It can be one of 'author','title','lines','linecount'.'linecount' means lines of a poem.Use ',' to separate multiple words if needed."}, "search_term": {"type": "string", "description": "It relates to input field.For example, if input_field is 'author', search_term can be 'Shakespeare'. Use ';' to separate multiple words if needed."}, "search_type": {"type": "string", "description": "It is optional. If search_type = 'abs', match search_term exactly when searching input_field.'Default' or empty, at the contrary, match <search term> with any part of <input field> when searching."}, "output_field": {"type": "string", "description": "It is optional. If multiple words are needed, use ',' to separate them. 'author','title','lines','linecount' can be chosen."}, "form": {"type": "string", "description": "It is optional. 'json','text'can be chosen."}}, "required": []}}, {"name": "query_database", "description": "Retrieve data from a specified table in the database based on given conditions.", "parameters": {"type": "object", "properties": {"table_name": {"type": "string", "description": "The name of the table to query data from."}, "column_names": {"type": "string", "description": "List of column names to be retrieved."}, "condition": {"type": "string", "description": "The condition to filter the query results."}}, "required": ["table_name", "column_names", "condition"]}}, {"name": "query_host", "description": "Retrieve information about a host.", "parameters": {"type": "object", "properties": {"host": {"type": "string", "description": "The host you want to query."}}, "required": ["host"]}}, {"name": "query_url", "description": "Retrieve information about an URL.", "parameters": {"type": "object", "properties": {"q_url": {"type": "string", "description": "The URL you want to query URLhaus for."}}, "required": ["q_url"]}}, {"name": "random_cold_jokes", "description": "It returns a random cold joke.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "random_quote", "description": "Free multilingual API for random famous quotes in many languages.", "parameters": {"type": "object", "properties": {"lang_code": {"type": "string", "description": "Language selected from: en, es, pt, it, de, fr, cs, sk. Default en."}}, "required": []}}, {"name": "rent_estimate", "description": "Get accurate and current estimated rent for a property, based on millions of records in Realty Mole’s real estate property database.", "parameters": {"type": "object", "properties": {"address": {"type": "string", "description": "There should be at least one not NULL parameter in 'address','latitude' and 'longitude'."}, "latitude": {"type": "string", "description": "There should be at least one not NULL parameter in 'address','latitude' and 'longitude'."}, "longitude": {"type": "string", "description": "There should be at least one not NULL parameter in 'address','latitude' and 'longitude'."}, "propertyType": {"type": "string", "description": "The property type of the house."}, "bedrooms": {"type": "string", "description": "It means the number of bedrooms."}, "bathrooms": {"type": "string", "description": "It means the number of bathrooms."}, "squareFootage": {"type": "string", "description": "It means the squareFootage number."}, "maxRadius": {"type": "string", "description": "It means the max radius."}, "daysOld": {"type": "string", "description": "It means the maximum number of days since comparable listings were last seen on the market, with a minimum of 1."}, "compCount": {"type": "string", "description": "It means the number of comparable listings returned by the API, between 5 and 25. Defaults to 10 if not provided."}}, "required": []}}]