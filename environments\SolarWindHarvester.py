from environments.BaseEnv import BaseEnv


class SolarWindHarvester(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.sectors = self.parameters.get("sectors", [])

    def list_sectors(self):
        return {"success": True, "data": self.sectors}

    def adjust_output(self, *, sector_id, new_output):
        for sector in self.sectors:
            if sector["sector_id"] == sector_id:
                sector["current_output"] = new_output
                return {"success": True}
        return {"success": False, "message": f"Sector {sector_id} not found."}
