from environments.BaseEnv import BaseEnv

class PersonalizedLearningPlan(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def search_sources(self):
        return {'success': True, 'data': self.parameters.get('sources', [])}

    def incorporate_sources(self, *, source_titles):
        sources = self.parameters.get('sources', [])
        res = []
        for s in sources:
            if s['title'] in source_titles:
                res.append(s)

        if res:
            return {'success': True, 'data': res}
        else:
            return {'success': False, 'data': 'Sources not found.'}

    def list_plans(self):
        plans = self.parameters.get('plans', [])
        return {'success': True, 'data': plans}
    
    def remove_plan(self, *, plan_name):
        plans = self.parameters.get('plans', [])
        new_plans = []
        for p in plans:
            if p['name'] != plan_name:
                new_plans.append(p)
        
        if len(new_plans) < len(plans):
            self.parameters['plans'] = new_plans
            return {'success': True, 'data': 'Plan removed successfully.'}
        else:
            return {'success': False, 'data': 'Plan not found.'}
        