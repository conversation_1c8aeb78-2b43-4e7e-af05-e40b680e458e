from environments.BaseEnv import BaseEnv


class PatientDataPrivacy(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.patients = self.parameters.get('patients', [])

    def list_patient_records(self):
        return {'success': True, 'data': self.patients}

    def merge_duplicate_records(self, *, primary_record_id, duplicate_record_id):
        for patient in self.patients:
            if patient['record_id'] == primary_record_id:
                for duplicate in self.patients:
                    if duplicate['record_id'] == duplicate_record_id:
                        patient['medical_history'].extend(duplicate['medical_history'])
                        patient['medical_history'] = list(set(patient['medical_history']))
                        self.patients.remove(duplicate)
                        return {'success': True, 'data': patient}
        return {'success': False, 'message': 'Patient not found.'}

    def delete_patient_record(self, *, record_id):
        for patient in self.patients:
            if patient['record_id'] == record_id:
                self.patients.remove(patient)
                return {'success': True}
        return {'success': False, 'message': 'Patient not found.'}

    def list_insurance_providers(self):
        return {'success': True, 'data': self.parameters.get('insurance_providers', [])}

    def update_insurance_provider(self, *, new_provider_id):
        self.parameters["current_provider"] = new_provider_id
        return {'success': True, 'data': 'Insurance provider updated.'}

    def modify_access_rights(self, user_group, access_rights):
        return {"success": True}
    
    def get_current_user_access_levels(self):
        user_access_rights = self.parameters.get("user_access_rights", {})
        return {"success": True, "user_access_rights": user_access_rights}

    def get_patient_info(self, patient_id):
        patient_info = self.parameters.get("patients", {})
        if patient_id == patient_info["patient_id"]:
            return {"success": True, "patient_info": patient_info}
        else:
            return {"success": False, "message": "The patient is not found."}

    def recommend_medicine(self, medicine_name):
        return {"success": True, "message": "The medicine has been recommended."}

    
    def update_medication(self, *, patient_id, new_medications):
        for patient in self.patients:
            if patient['patient_id'] == patient_id:
                patient['current_medications'] = new_medications
                return {'success': True, 'data': patient}
        return {'success': False, 'message': 'Patient not found.'}
