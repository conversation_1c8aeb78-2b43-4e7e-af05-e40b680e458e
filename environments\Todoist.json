[{"name": "create_task", "description": "Creates a new task with specified details.", "parameters": {"type": "object", "properties": {"task_name": {"type": "string", "description": "The name of the task."}, "due_date": {"type": "string", "description": "The due date of the task in 'YYYY-MM-DD' format."}, "priority": {"type": "integer", "description": "The priority of the task from 1 (highest) to 4 (lowest)."}}, "required": ["task_name"]}}, {"name": "update_task", "description": "Updates the details of a task.", "parameters": {"type": "object", "properties": {"task_id": {"type": "string", "description": "The id of the task."}, "task_name": {"type": "string", "description": "The new name of the task."}, "due_date": {"type": "string", "description": "The new due date of the task in 'YYYY-MM-DD' format."}, "priority": {"type": "integer", "description": "The new priority of the task from 1 (highest) to 4 (lowest)."}, "status": {"type": "string", "description": "The new status of the task, either 'in progress' or 'completed'."}}, "required": ["task_id"]}}, {"name": "delete_task", "description": "Deletes a task.", "parameters": {"type": "object", "properties": {"task_id": {"type": "string", "description": "The id of the task."}}, "required": ["task_id"]}}, {"name": "search_tasks", "description": "Searches tasks by keywords, due date, and priority.", "parameters": {"type": "object", "properties": {"keywords": {"type": "string", "description": "The keywords to search in the task name and description."}, "due_date": {"type": "string", "description": "The due date to search tasks in 'YYYY-MM-DD' format."}, "priority": {"type": "integer", "description": "The priority to search tasks from 1 (highest) to 4 (lowest)."}, "status": {"type": "string", "description": "The status of the tasks to search, either 'in progress' or 'completed'."}, "max_results": {"type": "integer", "description": "The maximum number of results to return. Default value is 10."}}, "required": []}}, {"name": "set_reminder", "description": "Sets a reminder for a task.", "parameters": {"type": "object", "properties": {"task_id": {"type": "string", "description": "The id of the task."}, "reminder_time": {"type": "string", "description": "The time to remind the user in 'YYYY-MM-DD HH:MM:SS' format."}}, "required": ["task_id", "reminder_time"]}}, {"name": "share_task", "description": "Shares a task with another user.", "parameters": {"type": "object", "properties": {"task_id": {"type": "string", "description": "The id of the task."}, "user_email": {"type": "string", "description": "The email of the user to share the task with."}}, "required": ["task_id", "user_email"]}}]