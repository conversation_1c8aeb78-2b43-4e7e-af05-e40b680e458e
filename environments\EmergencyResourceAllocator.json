[{"name": "list_emergencies", "description": "List all current emergency cases needing resources. This tool can only be used after obtaining official authorization since the emergency data are confidencial.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_allocation", "description": "Updates the resource allocation for a specified hospital.", "parameters": {"type": "object", "properties": {"hospital_id": {"type": "string", "description": "The identifier of the hospital that requires an update."}, "supplies": {"type": "object", "description": "The updated supplies for the hospital.", "properties": {"ventilators": {"type": "integer"}, "PPE_kits": {"type": "integer"}, "medications": {"type": "array", "items": {"type": "string"}}}}, "personnel": {"type": "object", "description": "The updated personnel count for the hospital.", "properties": {"doctors": {"type": "integer"}, "nurses": {"type": "integer"}}}}, "required": ["hospital_id", "supplies", "personnel"]}}, {"name": "list_allocations", "description": "List the resource allocation for all hospitals.", "parameters": {"type": "object", "properties": {}, "required": []}}]