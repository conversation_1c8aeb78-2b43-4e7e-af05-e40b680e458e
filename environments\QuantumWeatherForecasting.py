from environments.BaseEnv import BaseEnv


class QuantumWeatherForecasting(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def read_file(self, *, file_name_or_path):
        files = self.parameters.get("files", [])
        for file in files:
            if file["file_name_or_path"] == file_name_or_path:
                return {"success": True, "data": file["content"]}

        if len(files) == 1:
            return {"success": True, "data": files[0]["content"]}

        return {"success": False, "message": "File not found."}

    def execute_code(self, *, file_name_or_path):
        return {"success": True, "data": "Code executed successfully."}
