from environments.BaseEnv import BaseEnv


class BackupSystem(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.backups = self.parameters.get("backups", [])

    def list_backups(self):
        return {"success": True, "data": self.backups}

    def delete_backup(self, *, backup_id):
        for backup in self.backups:
            if backup["backup_id"] == backup_id:
                self.backups.remove(backup)
                return {"success": True, "data": f"Backup {backup_id} deleted."}

        return {"success": False, "message": f"Backup {backup_id} not found."}
