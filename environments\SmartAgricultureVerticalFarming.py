from environments.BaseEnv import BaseEnv


class SmartAgricultureVerticalFarming(BaseEnv):
    def __init__(self, *,parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.usage_reports = self.parameters.get("usage_reports", [])
        self.systems = self.parameters.get("systems", [])
        self.sensor_data = self.parameters.get("sensor_data", {})
        self.nutrition_strategy = self.parameters.get("nutrition_strategy", {})

    def list_usage_reports(self):
        return {"success": True, "data": self.usage_reports}

    def adjust_nutrient_mix(self, *, crop_type, nutrient, new_quantity):
        return {
            "success": True,
            "message": f"Nutrient mix for {crop_type} adjusted successfully.",
        }
        self.systems = self.parameters.get('systems', [])

    def get_nutrient_info(self):
        current_nutrient_state = self.parameters.get("current_nutrient_state", {})
        return {"success": True, "data": current_nutrient_state}

    def adjust_nutrient_solution(self, *, target_crop, adjustment_plan):
        return {"success": True, "data": "Nutrient solution adjusted."}
    
    def list_systems(self):
        return {'success': True, 'systems': self.systems}
    
    def disable_system(self, *, system_name):
        for system in self.systems:
            if system['name'] == system_name:
                system['status'] = 'disabled'
                return {'success': True}
            
        return {'success': False, 'message': f"No system named {system_name}."}

    def get_sensor_data(self):
        return {'success': True, 'data': self.sensor_data}
    
    def get_current_nutrition_strategy(self):
        return {'success': True, 'data': self.nutrition_strategy}
    
    def adjust_nutrition_strategy(self, *, nutrient_type, amount):
        if nutrient_type not in self.nutrition_strategy:
            self.nutrition_strategy[nutrient_type] = amount
        else:
            self.nutrition_strategy[nutrient_type] += amount
        return {'success': True, 'message': 'Nutrition strategy adjusted.'}