from environments.BaseEnv import BaseEnv

class PersonalizedVirtualRealityRehabilitation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.patients = self.parameters.get("patients", [])
    
    def get_rehabilitation_techniques(self):
        techniques_info = self.parameters.get("techniques_info", {})
        return {"success": True, "techniques_info": techniques_info}
    
    def get_vr_tools(self):
        vr_tools_info = self.parameters.get("vr_tools_info", {})
        return {"success": True, "vr_tools_info": vr_tools_info} 
    
    def list_patients(self):
        return {"success": True, "patients": self.patients}
    
    def modify_therapy_schedule(self, patient_id, new_schedule):
        for patient in self.patients:
            if patient["id"] == patient_id:
                patient["therapy_schedule"] = new_schedule
                return {"success": True, "message": "Therapy schedule updated successfully"}
        return {"success": False, "message": "Patient not found"}
    
    def modify_session_parameters(self, patient_id, new_parameters):
        for patient in self.patients:
            if patient["id"] == patient_id:
                patient["therapy_settings"] = new_parameters
                return {"success": True, "message": "Session parameters updated successfully"}
        return {"success": False, "message": "Patient not found"}