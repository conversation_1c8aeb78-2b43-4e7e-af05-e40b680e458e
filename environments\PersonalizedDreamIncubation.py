from environments.BaseEnv import BaseEnv


class PersonalizedDreamIncubation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.user_info = self.parameters.get("user_info", {})
        self.users = self.parameters.get("users", {})
        self.company_info = self.parameters.get("company_info", {})
    
    def login(self):
        return {"success": True, "data": {"user_info": self.user_info}}
    
    def set_temperature(self, temperature_level):
        return {"success": True, "data": {"message": "The temperature has been set."}}

    def read_dream_log(self):
        logs = self.parameters.get("dream_logs", [])
        return {"success": True, "logs": logs}
    
    def update_dream_log(self, date, contents):
        return {"success": True}
        

    def read_study_results(self, *, study_id):
        studies = self.parameters.get("studies", [])
        for study in studies:
            if study["study_id"] == study_id:
                return {"success": True, "data": study}

        return {"success": False, "data": "Study not found."}

    def fetch_user_data(self, *, user_id):
        if user_id in self.users:
            return {"success": True, "data": self.users[user_id]}
        return {"success": False, "message": "User not found."}

    def modify_dream_settings(
        self, *, user_id, new_investment_strategy, new_retirement_vision
    ):
        return {"success": True, "message": "Dream settings modified."}

    def get_user_profile(self, *, user_name):
        for user in self.users:
            if user["user_name"] == user_name:
                return {"success": True, "data": user}

        return {"success": False, "message": "User not found."}

    def adjust_goal_timeline(self, *, user_id, goal_id, new_completion_time):
        return {"success": True, "message": "Goal timeline adjusted."}
    
    def get_company_procedures(self, *, company_name):
        if company_name in self.company_info:
            return {"success": True, "data": self.company_info[company_name]}
        return {"success": False, "data": "Company not found."}
