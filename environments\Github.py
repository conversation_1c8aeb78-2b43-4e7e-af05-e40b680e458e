from environments.BaseEnv import BaseEnv

class Github(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.repositories = self.parameters.get('repositories', [])
        self.users = self.parameters.get('users', [])
            
    def manage_collaborator(self, *, repo_id, action, collaborator_username):
        for repo in self.repositories:
            if repo['repo_id'] == repo_id:
                if action == 'add':
                    repo['collaborators'].append(collaborator_username)
                elif action == 'remove':
                    repo['collaborators'].remove(collaborator_username)
                return {'success': True}
        return {'success': False, 'message': 'Repository not found.'}
    
    def search_issues(self, *, repository_name, search_text):
        for repo in self.repositories:
            if repo['name'] == repository_name:
                issues = [issue for issue in repo.get('issues', []) if (search_text is None or search_text in issue.get('title', ''))]
                if issues:
                    return {'success': True, 'data': {'issues': issues}}
                else:
                    return {'success': True, 'data': {'issues': repo.get('issues', [])}}

        return {'success': False, 'message': 'Repository not found.'}
    
    def create_repository(self, *, name, visibility):
        self.repositories.append({'repo_id':str(len(self.repositories)+1),'repository_name': name, 'visibility': visibility, 'issues': [], 'collaborators': []})
        return {'success': True}
    
    def get_repository_details(self, *, name):
        for repo in self.repositories:
            if repo["name"]==name:
                return {'success': True, "repo":repo}
        return {'success': False}
    
    def delete_repository(self, *, name):
        for i, repo in enumerate(self.repositories):
            if repo["name"] == name:
                del self.repositories[i]
                return {'success': True, "repo":repo}
        return {'success': False}
    
    def get_user_details(self, *, username):
        for user in self.users:
            if user["username"]==username:
                return {'success': True, "user":user}
        return {'success': False}
    
    def search_repositories(self, *, keywords=None, search_scope='all', max_results=5):
        if max_results > 10:
            max_results = 10

        filtered_repos = self.repositories

        if search_scope == 'owned':
            filtered_repos = [repo for repo in filtered_repos if repo.get('owner') == 'current_user']
        elif search_scope == 'member':
            filtered_repos = [repo for repo in filtered_repos if 'current_user' in repo.get('collaborators', [])]

        if keywords:
            filtered_repos = [repo for repo in filtered_repos if keywords.lower() in repo.get('name', '').lower()]
        if not filtered_repos:
            return {'success': True, 'repositories': self.repositories[:max_results]}
        return {'success': True, 'repositories': filtered_repos[:max_results]}
