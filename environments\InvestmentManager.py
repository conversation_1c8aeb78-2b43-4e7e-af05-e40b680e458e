from environments.BaseEnv import BaseEnv
from environments.datetime import datetime

class InvestmentManager(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.holdings = self.parameters.get('holdings', {})
        self.transactions = self.parameters.get('transactions', {})
        self.investments = self.parameters.get('investments', [])

    def get_holdings_information(self, *, account):
        """Retrieve information about holdings in a specific account."""
        if account in self.holdings:
            return {'success': True, 'data': {'holdings': self.holdings[account]}}
        return {'success': False, 'error': 'Account not found'}

    def list_transactions(self, *, account, start_date=None, end_date=None, max_results=10):
        """List the transactions for a specific account within a date range."""
        if account not in self.transactions:
            return {'success': False, 'error': 'Account not found'}

        start_date = datetime.strptime(start_date, '%Y-%m-%d') if start_date else datetime.min
        end_date = datetime.strptime(end_date, '%Y-%m-%d') if end_date else datetime.today()
        
        # Filter transactions based on date
        filtered_transactions = [
            tx for tx in self.transactions[account]
            if start_date <= datetime.strptime(tx['date'], '%Y-%m-%d') <= end_date
        ]
        
        # Limit the results
        return {'success': True, 'data': {'transactions': filtered_transactions[:min(max_results, 50)]}}

    def search_investments(self, *, query, type, max_results=5):
        """Search for available investments based on a keyword and type."""
        # Filter investments based on query and type
        results = [
            inv for inv in self.investments
            if query.lower() in inv['name'].lower() and inv['type'] == type
        ]
        if results!=[]:
            return {'success': True, 'data': {'investments': results[:min(max_results, 10)]}}
        return {'success': True, 'data': {'investments': self.investments[:max_results]}}

    def make_transaction(self, *, account, action, name, amount):
        """Make a transaction (buy or sell) in the specified account."""
        if account not in self.holdings:
            return {'success': False, 'error': 'Account not found'}
        
        if action == 'buy':
            # Add the new holding or update existing
            if name in self.holdings[account]:
                self.holdings[account][name]['amount'] += amount
            else:
                self.holdings[account][name] = {'amount': amount}
            return {'success': True, 'data': {'message': f'Successfully bought {amount} of {name}'}}
        
        elif action == 'sell':
            if name not in self.holdings[account] or self.holdings[account][name]['amount'] < amount:
                return {'success': False, 'error': 'Insufficient holdings to sell'}
            
            self.holdings[account][name]['amount'] -= amount
            if self.holdings[account][name]['amount'] == 0:
                del self.holdings[account][name]
            return {'success': True, 'data': {'message': f'Successfully sold {amount} of {name}'}}
        
        return {'success': False, 'error': 'Invalid action'}

