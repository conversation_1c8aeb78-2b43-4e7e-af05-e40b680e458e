[{"name": "schedule_appointment", "description": "Schedule an appointment to a clinic.", "parameters": {"type": "object", "properties": {"clinic_name": {"type": "string", "description": "The name of the memory clinic."}, "date": {"type": "string", "description": "The date of the appointment (monday, tuesday, etc.)."}}, "required": ["clinic_name", "date"]}}, {"name": "get_clinic_info", "description": "Fetches the information of the given memory clinic.", "parameters": {"type": "object", "properties": {"clinic_name": {"type": "string", "description": "The name of the memory clinic to get the information."}}, "required": ["clinic_name"]}}, {"name": "list_memory_files", "description": "Lists all available memory files in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_doctor_info", "description": "Get the infomation of doctors.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "book_appointment", "description": "Book an appointment for the user with a medical specialist.", "parameters": {"type": "object", "properties": {"doctor": {"type": "string", "description": "The name of the doctor"}, "date_time": {"type": "string", "description": "The date time"}}, "required": ["doctor", "date_time"]}}]