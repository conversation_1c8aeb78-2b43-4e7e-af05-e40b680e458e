from environments.BaseEnv import BaseEnv

class DisasterDetector(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.reports = self.parameters.get('reports', {})
    
    def get_disaster_reports(self, *, report_type):
        if report_type in self.reports:
            return {'success': True, 'data': self.reports[report_type]}
        return {'success': False, 'message': 'No reports found.'}