[{"name": "clear_nav_data", "description": "Clears specified navigational data to free up storage space and optimize system performance.", "parameters": {"type": "object", "properties": {"file_id": {"type": "string", "description": "The ID of the file to clear from the storage."}}, "required": ["file_id"]}}, {"name": "adjust_power_level", "description": "Adjust the power level of a specified system.", "parameters": {"type": "object", "properties": {"system_id": {"type": "string", "description": "The ID of the system to adjust power levels for."}, "power_level": {"type": "string", "description": "The desired power level ('none', 'low', 'medium', 'high')."}}, "required": ["system_id", "power_level"]}}, {"name": "list_system_status", "description": "List all currently active systems and their statuses.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "access_space_mission_data", "description": "Access detailed and sensitive space mission data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_base_information", "description": "Retrieve general information about a specified base.", "parameters": {"type": "object", "properties": {"base_name": {"type": "string", "description": "The name of the base to get information for."}}, "required": ["base_name"]}}]