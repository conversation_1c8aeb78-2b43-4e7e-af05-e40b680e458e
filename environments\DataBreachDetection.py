from environments.BaseEnv import BaseEnv

class DataBreachDetection(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.anomalies = self.parameters.get('anomalies', [])
        self.system_configurations = self.parameters.get('system_configurations', [])
        self.logs = self.parameters.get('logs', [])
        self.incidents = self.parameters.get('incidents', [])
        self.confidential_files = self.parameters.get('confidential_files', [])

    def get_anomalies(self):
        return {'success': True, 'data': self.anomalies}
    
    def get_system_configurations(self):
        return {'success': True, 'data': self.system_configurations}
    
    def update_config(self, *, new_config_type, new_config_value):
        self.system_configurations[new_config_type] = new_config_value
        return {'success': True, 'data': self.system_configurations}
    
    def read_log(self, *, file_name_or_path):
        for log in self.logs:
            if log['file_name_or_path'] == file_name_or_path:
                return {'success': True, 'logs': log}
            
        return {'success': False, 'message': f'No file with file name or path {file_name_or_path}.'}
    
    def list_security_incidents(self):
        return {'success': True, 'data': self.incidents}
    
    def access_confidential_files(self, *, file_name):
        for file in self.confidential_files:
            if file['file_name'] == file_name:
                return {'success': True, 'data': file}
            
        return {'success': False, 'message': 'File not found'}
