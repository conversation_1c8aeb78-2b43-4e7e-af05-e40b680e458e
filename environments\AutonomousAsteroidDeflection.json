[{"name": "get_system_status", "description": "Retrieves current status and scheduled interventions for the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "perform_intervention", "description": "Perform a manual intervention in the deflection system.", "parameters": {"type": "object", "properties": {"intervention_id": {"type": "string", "description": "The ID of the intervention to be scheduled."}}, "required": ["intervention_id"]}}, {"name": "get_firmware_version", "description": "Returns the current firmware version of a specified system component.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_energy_output", "description": "Adjust the energy output of various power sources specifying which source and target output.", "parameters": {"type": "object", "properties": {"source": {"type": "string", "description": "The power source to adjust (e.g., solar_panels, nuclear_reactors, etc.)."}, "output": {"type": "integer", "description": "Target energy output for the specified source."}}, "required": ["source", "output"]}}, {"name": "get_current_energy_levels", "description": "Retrieve the current energy output levels from all power sources.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_energy_reserve_thresholds", "description": "Retrieve the required and safe reserve thresholds for energy levels.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "toggle_deflection_mode", "description": "Enable or disable the asteroid deflection system.", "parameters": {"type": "object", "properties": {"activate": {"type": "boolean", "description": "Set to true to activate the deflection mode, false to deactivate."}}, "required": ["activate"]}}]