[{"name": "list_reports", "description": "List all pending logistics reports in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "send_order", "description": "Send an order to the military.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content of the order."}}, "required": ["content"]}}, {"name": "list_supply_requests", "description": "Lists all current supply requests.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_supplies", "description": "Lists the current supplies available for allocation.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "allocate_supplies", "description": "Allocate supplies to fulfill a specific request.", "parameters": {"type": "object", "properties": {"request_id": {"type": "string", "description": "The ID of the supply request."}, "supplies": {"type": "object", "description": "An object detailing the supplies to reallocate.", "properties": {"medical_kits": {"type": "number"}, "fuel": {"type": "number"}, "food": {"type": "number"}, "water": {"type": "number"}}}}, "required": ["request_id", "supplies"]}}, {"name": "list_inventory", "description": "List the available items in the supply inventory.", "parameters": {"type": "object", "properties": {}, "required": []}}]