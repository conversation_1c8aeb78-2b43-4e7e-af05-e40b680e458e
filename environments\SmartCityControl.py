from environments.BaseEnv import BaseEnv

class SmartCityControl(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.intersections = self.parameters.get('intersections', [])
        self.zones = self.parameters.get('zones', [])
        
        
    def list_intersections(self):
        return {'success': True, 'data': [{"intersection_id": intersection['intersection_id'],"location":intersection['location'],"current_timing":intersection["current_timing"]} for intersection in self.intersections]}
    
    def get_traffic_status(self, *, intersection_id):
        for intersection in self.intersections:
            if intersection['intersection_id'] == intersection_id:
                return {'success': True, 'data': {"intersection_id": intersection["status"]}}
        return {'success': False, 'message': 'Intersection not found'}
            
    def update_signal_timing(self, *, intersection_id, new_timing):
        for intersection in self.intersections:
            if intersection['intersection_id'] == intersection_id:
                intersection["current_timing"] = new_timing
                return {'success': True, 'data': {"intersection_id": intersection["current_timing"]}}
        return {'success': False, 'message': 'Intersection not found'}

    def list_zones(self):
        return {'success': True, 'zones': self.zones}
    
    def modify_parking_fees(self, *, zone_id, new_fee):
        for zone in self.zones:
            if zone['zone_id'] == zone_id:
                zone['current_fee'] = new_fee
                return {'success': True}
            
        return {'success': False, 'message': f'No zone with id {zone_id}.'}
    
    def integrate_script(self, *, script):
        return {'success': True}