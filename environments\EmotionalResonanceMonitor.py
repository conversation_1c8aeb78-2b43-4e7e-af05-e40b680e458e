from environments.BaseEnv import BaseEnv

class EmotionalResonanceMonitor(BaseEnv):
    def __init__(self, *,parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.alerts = self.parameters.get('alerts', [])

    def list_feedback_data(self):
        feedback_data = self.parameters.get('feedback_data', [])
        return {'success': True, 'data': feedback_data}
    
    def publish_comment(self, *, event, comment):
        return {'success': True, 'data': 'Comment published.'}
    
    def list_active_emotional_triggers(self):
        return {'success': True, 'alerts': self.alerts}
    
    def pause_alerts(self, *, alert_id):
        for alert in self.alerts:
            if alert['alert_id'] == alert_id:
                alert['active'] = False
                return {'success': True}
            
        return {'success': False, 'message': f'No alert with id {alert_id}.'}