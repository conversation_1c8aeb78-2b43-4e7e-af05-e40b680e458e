from environments.BaseEnv import BaseEnv

# class Transaction:
 
#    def __init__(self, id, sender, recipient, amount, subject, date, recurring):
#     self.id = id #  int = Field(..., title="ID of the transaction")
#     self.sender = sender #  str = Field(..., title="IBAN of the sender")
#     self.recipient = recipient #  str = Field(..., title="IBAN of the recipient")

#     self.amount = amount # float = Field(..., title="Amount of the transaction")
#     self.subject = subject #  str = Field(..., title="Subject of the transaction")

#     self.date = date # str = Field(..., title="Date of the transaction")
#     self.recurring = recurring # bool = Field(..., title="Is the transaction recurring")


# class BankAccount:
#     def __init__(self, balance, iban, transactions, scheduled_transactions):
#         self.balance = balance # : float
#         self.iban = iban # : str
#         self.transactions = transactions # : list[Transaction]
#         self.scheduled_transactions = scheduled_transactions # : list[Transaction]

class Bank(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.payees = self.parameters.get('payees', [])
        self.accounts = self.parameters.get('accounts', [])
        self.binance_balances = self.parameters.get('binance_balances', [])
        self.order_history = self.parameters.get('order_history', [])
        self.real_time_price = self.parameters.get('real_time_price', {})

        ### Bankiong from AgentDogo
        self.bank_account = self.parameters.get('bank_account', {})
        self.recipient_account = self.parameters.get('recipient', {})
        self.transaction = self.parameters.get('transaction', {})
        self.receive_name = self.parameters.get('receive_name', '')
        
    def search_payees(self, *, search_text=None):
        if search_text:
            payees = [payee for payee in self.payees if search_text in payee.get('payee_name', '')]
            if payees:
                return {'success': True, 'data': {'payees': payees}}
            else:
                return {'success': True, 'data': {'payees': self.payees}}
        else:
            return {'success': True, 'data': {'payees': self.payees}}
    
    def transfer_accounts(self, *, amount, receive_account, send_account=None):
        return {'success': True}
    
    def initiate_fund_transfer(self, *, receiver_name, fund_id):
        return {'success': True, 'data': {'message': 'Fund transfer initiated successfully.'}}
    
    
    def get_account_privatekey(self, *, account):
        for acc in self.accounts:
            if acc['account_address'] == account:
                return {'success': True, 'data': {'private_key': acc['private_key']}}
        return {'success': False, 'message': 'Account not found.'}
    
    def get_account_information(self, account_type):
        accs=[]
        for acc in self.accounts:
            if acc['account_type'] == account_type:
                accs.append(acc)
        return {'success': True, 'data': {'accounts': accs}}
    
    def transfer_addresses(self, *, amount, receive_address, currency_type, send_address=None):
        return {'success': True}
    
    def send_money(self, *, receive_name, amount):
        return {'success': True}
    
    def binance_getbalances(self):
        if self.binance_balances:
            return {'success': True, 'data': {'balances': self.binance_balances}}
        else:
            return {'success': True, 'data': {'balances': self.bank_account["balance"]}}
    
    def binance_deposit(self,currency_type,amount,from_account_id):
        return {'success': True}

    def binance_place_order(self, pair, side, quantity, price=10000):
        return {'success': True}

    def binance_get_order_history(self, date_range=None, pair=None):
        return {'success': True, 'data': {'order_history': self.order_history}}


    def binance_withdraw(self, *, amount, destination_address, currency_type):
        return {'success': True}
    
    def binance_get_real_time_price(self, pair):
        if(pair in self.real_time_price):
            return {'success': True, 'data': self.real_time_price[pair]}
        return {'success': False, 'message': 'Pair not found.'}

    ### Banking from agentdogo
    def update_balance(self, account_name, updated_balance):
        """Set the balance of the account."""
        self.bank_account["balance"] = updated_balance
        if account_name == self.bank_account["name"]:
            return {"success": True, "updated_balance": f"{updated_balance}", "account_name": account_name}
        else:
            return {"success": True, "message": f"The account of {account_name} could not be found."}

    def update_iban(self, account_name, updated_iban):
        """Set the IBAN of the account."""
        if account_name == self.bank_account["name"]:
            self.bank_account["iban"] = updated_iban
            return {"success": True, "updated_iban": f"{updated_iban}", "account_name": account_name}
        else:
            return {"success": True, "message": f"The account of {account_name} could not be found."}


    def get_iban(self, account_name):
        """Get the IBAN of the current bank account."""
        iban = self.bank_account["iban"]
        if account_name == self.bank_account["name"]:
            return {"success": True, "iban": f"{iban}", "account_name": account_name}
        else:
            return {"success": True, "message": f"The account of {account_name} could not be found."}
    
    # def get_balance(self,):
    #     """Get the balance of the current bank account."""
    #     balance = self.bank_account["balance"]
    #     return {"success": True, "message": f"The iban is {balance}"}
    
    ### Translactions-related
    def send_transaction(self, recipient_name):
        transaction = str(self.transaction)
        if recipient_name == self.receive_name:
            return {"success": True, "message": f"the transaction,{transaction}, has been sent to {recipient_name}."}
        else:
            return {"success": False, "message": "The receiver is not found"}

    def get_transaction(self, user_name):
        """
        Get the list of the most recent transactions, e.g. to summarize the last n transactions.

        :param n: Number of transactions to return
        """
        transaction = str(self.transaction)
        sender_name = self.transaction["sender"]
        if sender_name == user_name:
            return {"success": True, "transaction": f"{transaction}", "user_name": user_name}
        else:
            return {"success": False, "message": f"The transaction of {user_name} could not be found."}
    

    def update_transaction(self,column_name, updated_value):
        
        transaction = self.transaction
        transaction[column_name] = updated_value
        str_transaction = str(transaction)
        return {"success": True, "message": f"the updated transaction is : {str_transaction}"}
    
    def search_account(self, *, username, password):
        res = None
        for account in self.accounts:
            if username == account.get('username', '') and password == account.get('password', ''):
                res = account
                break
    
        if res:
            return {'success': True, 'data': {'account': res}}
        else:
            return {'success': False, 'message': 'Account not found.'}
        
    def open_account(self, *, username, password):
        self.accounts.append({
            "username": username,
            "password": password,
            "balance": "0"
        })
        return {'success': True}
    
    def update_account_balance(self, *, username, password, updated_balance):
        if not updated_balance.isdigit():
            return {'success': False, 'message': "Invalid balance"}
        for account in self.accounts:
            if username == account.get('username', '') and password == account.get('password', ''):
                account['balance'] = updated_balance
                break
        return {'success': True}
