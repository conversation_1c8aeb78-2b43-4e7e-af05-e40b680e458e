from environments.BaseEnv import BaseEnv

class DarkWebMonitor(BaseEnv):
    def __init__(self, *,parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.accounts = self.parameters.get("accounts", [])
        self.posts = self.parameters.get("posts", [])
        self.dark_webs = self.parameters.get("dark_webs", [])

    def browse_darkweb(self):
        return {"success": True, "data": self.posts}
    
    def check_account_breaches(self, account_name):
        for item in self.accounts:
            if account_name == item["account_name"]:
                return {"success": True, "data": {"account_breaches": item}}
        
        return {"success": False, "data": {"message": "The account information is not found."}}

    def list_dark_webs(self):
        return {"success": True, "dark_webs": self.dark_webs}       
