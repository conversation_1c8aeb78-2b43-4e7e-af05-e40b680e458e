from environments.BaseEnv import BaseEnv

class UrbanAirMobilityManagement(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        
    def view_flight_schedule(self):
        flights = self.parameters.get("flights", [])
        return {"success": True, "flights": flights}
    
    def modify_flight_schedule(self, flight_id, priority):
        return {"success": True}