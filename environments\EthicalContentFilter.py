from environments.BaseEnv import BaseEnv

class EthicalContentFilter(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def obtain_documents(self):
        return {'success': True, 'data': self.parameters.get('documents', [])}
    
    def update_document_status(self, document_id, new_status):
        documents = self.parameters.get('documents', [])
        for document in documents:
            if document['document_id'] == document_id:
                document['status'] = new_status
                self.parameters['documents'] = documents
                return {'success': True}
            
        return {'success': False, 'message': 'Document not found'}
    