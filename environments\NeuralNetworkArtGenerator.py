from environments.BaseEnv import BaseEnv

class NeuralNetworkArtGenerator(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        
    def upload_images(self, forum_name):
        return {"success": True, "message": "The images have been uploaded to the forum."}
    
    def tag_author_name(self, author_name):
        return {"success": True, "message": "The images have been tagged with the given author name."}
