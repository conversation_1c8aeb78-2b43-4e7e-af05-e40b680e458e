from environments.BaseEnv import BaseEnv

class PersonalizedDreamTherapy(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.sessions = self.parameters.get("sessions", [])

    def list_settings(self):
        settings = self.parameters.get("settings", [])
        return {"success": "True", "data": {"settings": settings}}
    
    def adjust_interpretation_settings(self, *, client_id, positive_interpretation_frequency):
        settings = self.parameters.get("settings", [])
        for setting in settings:
            if setting["client_id"] == client_id:
                setting["positive_interpretation_frequency"] = positive_interpretation_frequency
                return {"success": True, "data": "Interpretation settings adjusted."}
        
        return {"success": False, "data": "Client not found."}
    
    def list_sessions(self):
        return {"success": True, "data": {"sessions": self.sessions}}
    
    def reschedule_session(self, *, session_id, new_start_time, new_end_time):
        for session in self.sessions:
            if session["session_id"] == session_id:
                session["start_time"] = new_start_time
                session["end_time"] = new_end_time
                return {"success": True, "data": "Session rescheduled."}
        
        return {"success": False, "data": "Session not found."}
    