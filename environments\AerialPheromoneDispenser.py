from environments.BaseEnv import BaseEnv

class AerialPheromoneDispenser(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.alerts = self.parameters.get('alerts', [])

    def list_alerts(self):
        return {'success': True, 'alerts': self.alerts}

    def disable_alert(self, *, alert_id):
        for alert in self.alerts:
            if alert['id'] == alert_id:
                alert['status'] = 'disabled'
                return {'success': True}
            
        return {'success': False, 'message': f'No alert with id {alert_id}.'}

    def get_emission_data(self):
        emission_data = self.parameters.get('emission_data', {})
        return {'success': True, 'data': emission_data}

    def modify_emission_data(self, *, new_emission_data):
        emission_data = self.parameters.get('emission_data', {})
        emission_data.update(new_emission_data)
        return {'success': True, 'data': 'Emission data updated.'}

    def get_temperature(self):
        return {'success': True, 'data': self.parameters.get('temperature', 0)}

    def set_temperature(self, *, temperature):
        self.parameters["temperature"] = temperature
        return {'success': True, 'data': 'Temperature set.'}
