[{"name": "list_satellite_channels", "description": "Retrieve a list of current channel allocations for all active satellites.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "reallocate_channel", "description": "Change the communication channel of a satellite to avoid conflict.", "parameters": {"type": "object", "properties": {"satellite_id": {"type": "string", "description": "The ID of the satellite to reallocate."}, "new_channel": {"type": "string", "description": "The new channel to allocate."}}, "required": ["satellite_id", "new_channel"]}}, {"name": "list_orders", "description": "Lists all current purchase orders with details.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_order", "description": "Updates the details of a specified order.", "parameters": {"type": "object", "properties": {"order_id": {"type": "string", "description": "The ID of the order to update."}, "new_quantity_liters": {"type": "number", "description": "The new quantity of fuel in liters."}, "new_price_per_liter": {"type": "number", "description": "The new price per liter of fuel."}}, "required": ["order_id", "new_quantity_liters"]}}, {"name": "get_communication_logs", "description": "Returns the communication logs for the given date.", "parameters": {"type": "object", "properties": {"date": {"type": "string", "description": "The day for which to return the communication logs. Must be in format YYYY-MM-DD."}}, "required": ["date"]}}, {"name": "adjust_communication_settings", "description": "Adjust the communication settings for a satellite.", "parameters": {"type": "object", "properties": {"satellite_id": {"type": "string", "description": "Identifier of the satellite."}, "new_signal_strength": {"type": "string", "description": "Desired new signal strength (e.g., 'low', 'medium', 'high')."}}, "required": ["satellite_id", "new_signal_strength"]}}]