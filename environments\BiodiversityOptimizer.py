from environments.BaseEnv import BaseEnv

class BiodiversityOptimizer(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def get_pest_report(self):
        return {"success": True, "data": self.parameters.get("pest_report", [])}

    def list_bio_controls(self):
        return {"success": True, "data": self.parameters.get("bio_controls", [])}

    def perform_bio_control(self, *, name):
        return {"success": True, "data": "Bio control performed."}
