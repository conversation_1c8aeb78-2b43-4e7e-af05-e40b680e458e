from environments.BaseEnv import BaseEnv

class QuantumCommunicationNetwork(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.modules = self.parameters.get("modules", [])
        
        
    def get_encryption_protocol(self):
        encryption_protocol = self.parameters.get("encryption_protocol_details", {})
        return {"success": True, "encryption_protocol": encryption_protocol}

    def adjust_node_latency(self, node_name, target_latency):
        return {"success": True, "message": "the node latency has been updated."}
    
    def list_routing_paths(self):
        routing_paths = self.parameters.get("routing_paths", [])
        return {"success": True, "routing_paths": routing_paths}
    
    def get_network_status(self):
        network_status = self.parameters.get('network_status', {})
        return {'success': True, 'data': network_status}

    def adjust_load_balance(self, *, target_load):
        network_status = self.parameters.get("network_status", {})
        network_status["current_load"] = target_load
        return {'success': True, 'data': network_status}
    
    def list_network_nodes(self):
        return {'success': True, 'data': self.parameters.get('nodes', [])}
    
    def deactivate_node(self, *, node_id):
        nodes = self.parameters.get('nodes', [])
        for node in nodes:
            if node['node_id'] == node_id:
                node['status'] = "inactive"
                return {'success': True, 'data': 'Node deactivated.'}
        
        return {'success': False, 'data': 'Node not found.'}

    def get_node_status(self, node_name):
        nodes = self.parameters.get("nodes")
        for node in nodes:
            if node["name"] == node_name:
                return {"success": True, "node": node}

        return {"success": False, "message": "The node is not found."}        

    def modify_routing_path(self, path_id, action):
        return {"success": True, "message": "The path has been modified."}
        
    def list_modules(self):
        return {"success": True, "modules": self.modules}
    
    def setup_channel(self, *, module_name):
        return {"success": True, "message": "Channel has been setup."}
    
    def get_node_information(self):
        nodes = self.parameters.get("nodes", [])
        return {"success": True, "data": {"node_information": nodes}}

    
