from environments.BaseEnv import BaseEnv


class SmartCityTrafficFlow(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.traffic_data = self.parameters.get("traffic_data", {})

    def get_traffic_data(self):
        return {"success": True, "data": {"traffic_data": self.traffic_data}}

    def adjust_traffic_lights(self, street_name, new_light_setting):
        return {"success": True, "data": {"message": "The light has been reset."}}
