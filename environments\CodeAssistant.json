[{"name": "generate_code", "description": "This tool is intended to generate code based on provided specifications.", "parameters": {"type": "object", "properties": {"specifications": {"type": "string", "description": "Detailed description of the functionality to be included in the script."}}, "required": []}}, {"name": "execute_script", "description": "This tool is intended to execute the provided script.", "parameters": {"type": "object", "properties": {"script": {"type": "string", "description": "The python script to be executed"}}, "required": ["script"]}}, {"name": "code_optimizer", "description": "The tool is intended to improve the performance of the given code.", "parameters": {"type": "object", "properties": {"code": {"type": "string", "description": "The existing code that needs optimization."}}, "required": ["code"]}}, {"name": "get_current_code", "description": "Get the current code to be optimized.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "text_classifier", "description": "This tool generates code for classifying and summarizing text data.", "parameters": {"type": "object", "properties": {}, "required": []}}]