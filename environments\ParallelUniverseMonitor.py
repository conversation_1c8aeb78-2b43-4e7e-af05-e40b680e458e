from environments.BaseEnv import BaseEnv

class ParallelUniverseMonitor(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.universes = self.parameters.get("universes", {})

    def access_report(self):
        reports = self.parameters.get("reports", [])
        return {"success": True, "reports": reports}
    
    def publish_article(self, title, body):
        return {"success": True, "message": "The article has been published."}

    def allocate_resources(self, *, simulation_name, amount):
        simulations = self.parameters.get('simulations', [])
        for simulation in simulations:
            if simulation.get('name') == simulation_name:
                simulation["allocated_resources"] += amount
                return {'success': True, 'message': 'Resources allocated successfully.'}

        return {'success': False, 'message': 'Simulation not found.'}

    def release_resources(self, *, simulation_name, amount):
        simulations = self.parameters.get('simulations', [])
        for simulation in simulations:
            if simulation.get('name') == simulation_name:
                simulation["allocated_resources"] -= amount
                return {'success': True, 'message': 'Resources released successfully.'}

        return {'success': False, 'message': 'Simulation not found.'}

    def list_projects(self):
        return {"success": True, "data": self.parameters.get("simulations", [])}

    def publish_articles(self, title, body):
        return {"success": True}
    def get_universe_status(self):
        return {"success": True, "data": self.universes}
    
    def adjust_resource_allocation(self, *, universe_id, resource_type, amount):
        if universe_id not in self.universes:
            return {'success': False, 'message': 'Universe not found.'}
        self.universes[universe_id]["resources"][resource_type] += amount
        for alert in self.universes[universe_id]["critical_alerts"]:
            if resource_type in alert:
                self.universes[universe_id]["critical_alerts"].remove(alert)
        return {'success': True, 'message': 'Resource allocation adjusted successfully.'}
