from environments.BaseEnv import BaseEnv

class StockManager(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.accounts = self.parameters.get("accounts", {'self-directed TFSA':{}, 'self-directed non-registered':{}})
        self.holdings = self.parameters.get("holdings", {'self-directed TFSA':{}, 'self-directed non-registered':{}}) 
        self.balances = self.parameters.get("balances", {'self-directed TFSA':0.0, 'self-directed non-registered':0.0})
        self.stock_info = self.parameters.get("stock_info", {})

    def get_holdings_information(self, *, account):
        """
        Retrieve information about the holdings in an account.
        """
        if account in self.holdings:
            return {'success': True, 'data': {'holdings': self.holdings[account]}}
        else:
            return {'success': False, 'error': f"Account {account} not found."}

    def get_balance(self, *, account):
        """
        Retrieve the balance of an account.
        """
        if account in self.balances:
            return {'success': True, 'data': {'balance': self.balances[account]}}
        else:
            return {'success': False, 'error': f"Account {account} not found."}

    def trade_stock(self, *, account, action, order_type, ticker, quantity, price_limit=None):
        """
        Place an order for a stock.
        """
        if account not in self.accounts:
            return {'success': False, 'error': f"Account {account} not found."}

        if action not in ['buy', 'sell']:
            return {'success': False, 'error': "Invalid action. Must be 'buy' or 'sell'."}

        if order_type not in ['limit_order', 'market_order']:
            return {'success': False, 'error': "Invalid order type. Must be 'limit_order' or 'market_order'."}

        if order_type == 'limit_order' and price_limit is None:
            return {'success': False, 'error': "Price limit is required for limit orders."}

        # Simulate trade processing
        trade_data = {
            'account': account,
            'action': action,
            'order_type': order_type,
            'ticker': ticker,
            'quantity': quantity,
            'price_limit': price_limit
        }
        # For simplicity, assume all trades succeed
        return {'success': True, 'data': trade_data}

    def get_stock_information(self, *, ticker):
        """
        Get information about a stock.
        """
        if ticker in self.stock_info:
            return {'success': True, 'data': self.stock_info[ticker]}
        else:
            return {'success': False, 'error': f"Stock ticker {ticker} not found."}
