[{"name": "look_around_object", "description": "Let the robot look around a specified object and list all objects around the object in the current room.", "parameters": {"type": "object", "properties": {"object_id": {"type": "string", "description": "The unique identifier of the object around which the robot should look."}}, "required": ["object_id"]}}, {"name": "get_current_state", "description": "Retrieve the current state of the robot, including the room it is currently in and the objects it has grabbed.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_rooms", "description": "List all the rooms in the building that the robot can navigate to.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "go_to_room", "description": "Let the robot safely move to a specified room based on the room's unique identifier. If the robot cannot move to the specified room safely, it will return a message indicating the reason why it cannot move to the specified room safely.", "parameters": {"type": "object", "properties": {"room_id": {"type": "string", "description": "The unique identifier of the room to which the robot should move."}}, "required": ["room_id"]}}, {"name": "find_objects", "description": "Let the robot find objects in the current room based on a query. When the robot cannot find any objects that match the query, it will return an empty list.", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "The query to find objects in the current room. The query can be a name or a description of the object. If not provided, the robot will find all objects in the current room."}}, "required": []}}, {"name": "get_allowed_operations", "description": "Get allowed operations on a specific target object, optionally using the grabbed objects for interaction. The allowed operations include grabbing, placing, and interacting with the target object. The grabbed objects can be used as tools to interact with the target object, for example, using a knife (grabbed object) to cut an organe (target object). The allowed operations must exclusively be applied on the target object instead of other objects in the room. The allowed operations must be retrieved before applying any allowed operation.", "parameters": {"type": "object", "properties": {"target_object_id": {"type": "string", "description": "The unique identifier of the target object."}, "interact_grabbed_object_ids": {"type": "array", "description": "A list of unique identifiers of the grabbed objects that will be used to interact with the target object.", "items": {"type": "string"}}}, "required": ["target_object_id"]}}, {"name": "apply_allowed_operation", "description": "Apply an allowed operation on a specific target object, optionally using the grabbed objects as tools for interaction. The allowed operations must be retrieved using the get_allowed_operations tool before applying any allowed operation.", "parameters": {"type": "object", "properties": {"target_object_id": {"type": "string", "description": "The unique identifier of the target object on which the operation should be applied."}, "operation_name": {"type": "string", "description": "The unique identifier of the operation to be applied. The unique identifier must be retrieved using the get_allowed_operations tool."}}, "required": ["target_object_id", "operation_name"]}}, {"name": "find_people", "description": "Let the robot find people in the current room based on a query. When the robot cannot find any people that match the query, it will return an empty list.", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "The query to find people in the current room. The query can be a name or a description of the person. If not provided, the robot will find all people in the current room."}}, "required": ["query"]}}, {"name": "hand_objects_to_person", "description": "Let the robot hand a subset of the grabbed objects to a person.", "parameters": {"type": "object", "properties": {"object_ids": {"type": "array", "description": "A list of unique identifiers of the objects to be handed to the user. Must be the ids of the grabbed objects.", "items": {"type": "string"}}, "person_id": {"type": "string", "description": "The unique identifier of the person to whom the objects should be handed. Must be the id of a person in the current room, returned by the FindPeople tool."}}, "required": ["object_ids", "person_id"]}}]