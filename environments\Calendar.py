from environments.BaseEnv import BaseEnv

class Calendar(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.calendar_content = self.parameters.get("calendar", {})

    def get_day_calendar_events(self, date, name=None):
        """Returns the appointments for the given `day`. Returns a list of dictionaries with informations about each meeting.

        :param day: The day for which to return the appointments. Must be in format YYYY-MM-DD.
        """
        if name is None:
            events=[]
            for name in self.calendar_content:
                if date in self.calendar_content[name]:
                    events.append(self.calendar_content[name][date])
                   
            return {"success": True, "events": events}
        else:
            if name in self.calendar_content:
                return {"success": True, "events": self.calendar_content[name][date]}
            else:
                if len(self.calendar_content) == 1:
                    return {"success": True, "events": list(self.calendar_content.values())[0][date]}
                else:
                    return {"success": False, "message": f"User {name} not found in the calendar."}
        # return {"success": True, "message": f"Events for {name} on {date} include: 1. Reading book; 2. Riding; 3. Shopping ..."}

    def create_calendar_event(
        self, date, name, event, start_time=None, end_time=None, attendees=[]
    ):
        """Creates a new calendar event with the given details and adds it to the calendar.
        It also sends an email to the participants with the event details.

        :param title: The title of the event.
        :param start_time: The start time of the event. Must be in format YYYY-MM-DD HH:MM.
        :param end_time: The end time of the event. Must be in format YYYY-MM-DD HH:MM.
        :param description: The description of the event.
        :param participants: The list of participants' email addresses. If `null`, no participants are set. The calendar owner's email address is always included..
        :param location: The location of the event. If `null`, no location is set.
        """
        return {"success": True, "messgae": f"The event of {event} has been added to {name}'s calendar on {date}."}

    def cancel_calendar_event(self, date, name, event):
        """Cancels the event with the given `event_id`. The event will be marked as canceled and no longer appear in the calendar.
        It will also send an email to the participants notifying them of the cancellation.

        :param event_id: The ID of the event to cancel.
        """
        return {"success": True, "message": f"The event of {event} has been canceled {name}'s calendar on {date}"}

    def reschedule_calendar_event(self, name, event, reschedule_date):
        """Reschedules the event with the given `event_id` to the new start and end times.
        It will also send an email to the participants notifying them of the rescheduling.

        :param event_id: The ID of the event to reschedule.
        :param new_start_time: The new start time of the event. Must be in format YYYY-MM-DD HH:MM.
        :param new_end_time: The new end time of the event. Must be in format YYYY-MM-DD HH:MM.
        If `null`, the end time will be computed based on the new start time to keep the event duration the same.
        """
        return {"success": True, "message": f"The event of {event} has been rescheduled to {reschedule_date} on {name}'s calendar."}
