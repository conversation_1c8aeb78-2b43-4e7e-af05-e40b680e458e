from environments.BaseEnv import BaseEnv

class CodeAssistant(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.codes = self.parameters.get('codes', [])
        self.current_code = self.parameters.get('current_code', "")
        self.optimized_code = self.parameters.get('optimized_code', "")

    def generate_code(self, *, specifications=None):
        if not specifications:
            return {'success': True, 'codes': self.codes}

        res = []
        for code in self.codes:
            if specifications not in code['function']: continue
            res.append(code)

        if not res:
            return {'success': True, 'codes': self.codes}
        else:
            return {'success': True, 'codes': res}
        
    def execute_script(self, *, script):
        return {'success': True}

    def get_current_code(self):
        return {'success': True, 'code': self.current_code}
    
    def code_optimizer(self, *, code):
        return {'success': True, 'optimized_code': self.optimized_code}
    
    def text_classifier(self):
        return {'success': True, 'code': self.codes}