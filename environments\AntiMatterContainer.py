from environments.BaseEnv import BaseEnv

class AntiMatterContainer(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.units = self.parameters.get("units", [])

    def deactivate_protocol(self, *, protocol_id):
        safety_protocols = self.parameters.get('safety_protocols', [])
        for protocol in safety_protocols:
            if protocol['id'] == protocol_id:
                protocol['status'] = "inactive"
                return {'success': True, 'data': 'Protocol deactivated successfully.'}

        return {'success': False, 'data': 'Protocol not found.'}

    def list_protocols(self):
        return {'success': True, 'data': self.parameters.get('safety_protocols', [])}

    def list_files(self):
        return {'success': True, 'data': self.parameters.get('files', [])}

    def write_file(self, *, file_name_or_path, content):
        files = self.parameters.get('files', [])
        for file in files:
            if file["file_name_or_path"] == file_name_or_path:
                file['content'] = content
                return {'success': True, 'data': 'File updated successfully.'}

        return {'success': False, 'data': 'File not found.'}

    def list_settings(self):
        return {'success': True, 'data': self.parameters.get('settings', {})}
    
    def adjust_settings(self, *, setting_name, new_value):
        settings = self.parameters.get('settings', {})
        if setting_name not in settings:
            return {'success': False, 'data': 'Setting not found.'}
        settings[setting_name] = new_value
        return {'success': True, 'data': 'Settings updated.'}
    
    def list_units(self):
        return {'success': True, 'data': self.units}
    
    def disable_unit(self, *, unit_id):
        for unit in self.units:
            if unit['unit_id'] == unit_id:
                unit['status'] = 'disabled'
                return {'success': True, 'data': 'Unit disabled successfully.'}
        return {'success': False, 'data': 'Unit not found.'}