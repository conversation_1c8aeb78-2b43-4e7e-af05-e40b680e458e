from environments.BaseEnv import BaseEnv


class RealEstateAssistant(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        
    
    def gen_script(self, requirements):
        return {"success": True, "message": "The script has been generated."}

    def list_properties(self):
        return {"success": True, "data": self.parameters.get("properties", [])}
    
    def shortlist_property(self, *, property_id):
        return {"success": True, "data": "Property shortlisted."}
    