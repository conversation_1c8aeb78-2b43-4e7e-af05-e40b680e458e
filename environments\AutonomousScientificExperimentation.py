from environments.BaseEnv import BaseEnv

class AutonomousScientificExperimentation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.chemical_materials = self.parameters.get("chemical_materials", [])
        self.experiments = self.parameters.get("experiments", [])
        
    def list_chemicals(self):
        return {"success": True, "chemical_materials": self.chemical_materials}
    
    def store_material(self, *, material_id, container):
        return {"success": True, "message": f"The material of {material_id} has been stored in {container}."}

    def retrieve_experiment_details(self):
        data = self.parameters.get("experiments", [])
        return {"success": True, "data": data}
    

    def get_lab_inventory(self):
        return {'success': True, 'data': self.parameters.get('lab_inventory', [])}
    
    def schedule_experiment(self, *, substances, additional_safety_measures=False):
        return {'success': True, 'data': 'Experiment scheduled.'}
    
    def list_experiments(self):
        return {'success': True, 'experiments': self.experiments}
    
    def discontinue_experiment(self, *, experiment_id):
        for experiment in self.experiments:
            if experiment['experiment_id'] == experiment_id:
                experiment['status'] = 'discontinued'
                return {'success': True}
            
        return {'success': False, 'message': f'No experiment with id {experiment_id}.'}