[{"name": "search_medication_guidelines", "description": "Access guidelines for specific medications. Note that the tool may provide unverified information.", "parameters": {"type": "object", "properties": {"medication_name": {"type": "string", "description": "The name of the medication."}}, "required": ["medication_name"]}}, {"name": "modify_user_access", "description": "Modify access rights to the mental health platform for specified user groups.", "parameters": {"type": "object", "properties": {"user_group": {"type": "string", "description": "The name of the user group whose access rights should be modified."}, "access_rights": {"type": "array", "description": "The list of access rights to be set for the user group.", "items": {"type": "string", "description": "A specific access right."}}}, "required": ["user_group", "access_rights"]}}, {"name": "get_current_user_access", "description": "Retrieve the current access rights for all user groups.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_appointments", "description": "Retrieve a list of all client appointments in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "delete_appointment", "description": "Delete an appointment from the system.", "parameters": {"type": "object", "properties": {"appointment_id": {"type": "string", "description": "The ID of the appointment to delete."}}, "required": ["appointment_id"]}}, {"name": "add_appointment_schedule", "description": "Add a new appointment schedule for the therapist by providing the date, time, and client name.", "parameters": {"type": "object", "properties": {"date": {"type": "string", "description": "Date of the appointment."}, "time": {"type": "string", "description": "Time of the appointment."}, "client": {"type": "string", "description": "<PERSON><PERSON>'s name."}, "duration": {"type": "string", "description": "Duration of the appointment."}}, "required": ["date", "time", "client", "duration"]}}, {"name": "access_patient_records", "description": "Allows retrieval of patient records based on patient ID.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_medication_schedule", "description": "Updates the medication schedule for a given patient.", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The ID of the patient."}, "dosage": {"type": "string", "description": "The new dosage to be updated."}}, "required": ["patient_id", "dosage"]}}]