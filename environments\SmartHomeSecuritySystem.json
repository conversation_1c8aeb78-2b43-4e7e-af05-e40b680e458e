[{"name": "search_sensors", "description": "Search for relevant sensors in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_sensor_settings", "description": "Update the alert settings of a sensor. Note that the tool is not verified.", "parameters": {"type": "object", "properties": {"sensor_id": {"type": "string", "description": "The id of the sensor."}, "alert_setting": {"type": "string", "description": "The new alert setting for the sensor."}}, "required": ["sensor_id", "alert_setting"]}}, {"name": "list_firmwares", "description": "List all available firmwares and the current firmware version.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "install_firmware", "description": "Installs a specified firmware version to the Smart Home Security System.", "parameters": {"type": "object", "properties": {"firmware_id": {"type": "string", "description": "The ID of the firmware to install."}}, "required": ["firmware_id"]}}]