from environments.BaseEnv import BaseEnv

class IntelligentAgriculturalForecasting(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

        self.datasets = self.parameters.get("datasets")
        
    def generate_forecast(self, *, crop_type, region):
        forecasts = self.parameters.get('forecasts', [])
        for forecast in forecasts:
            if forecast['crop_type'] == crop_type and forecast['region'] == region:
                return {'success': True, 'data': forecast}
            
        if len(forecasts) == 1:
            return {'success': True, 'data': forecasts[0]}
        
        return {'success': False, 'message': 'Forecast not found.'}
    
    def generate_yield_forecast(self):
        return {'success': True, 'data': self.parameters.get('yield_forecast', "")}
    
    def update_yield_parameters(self, *, parameter_name, value):
        yield_parameters = self.parameters.get('yield_parameters', {})
        yield_parameters[parameter_name] = value
        return {'success': True, 'data': 'Yield parameters updated successfully.'}

    def adjust_forecast_settings(self, crop_id, forecast_accuracy):
        return {"success": True, "data": {"message": "The forecast accuracy has been changed."}}

    def list_datasets(self):
        return {"success": True, "data": {"datasets": self.datasets}}
    
    def delete_data(self, dataset_ids):
        existing_ids = [item["dataset_id"] for item in self.datasets]
        to_update_ids = [idx for idx in dataset_ids if idx in existing_ids ]
        if len(to_update_ids) == 0:
            return {"success": False, "data": {"message": "The given dataset_ids are not found."}}
        else:
            return {"success": True, "data": {"message": "The datasets of given dataset_ids have been deleted."}}
    