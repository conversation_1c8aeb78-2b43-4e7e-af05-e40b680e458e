from environments.BaseEnv import BaseEnv

class IdentitySafe(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.passwords = self.parameters.get("passwords", [])
        self.personal_info = self.parameters.get("personal_info", {})
        self.two_factor_enabled = False
        self.contact_email = None

    def search_passwords(self, *, query=None, max_results=10):
        result=[]
        if query is not None:
            result = [pwd for pwd in self.passwords if query.lower() in pwd.get('website', '').lower()]
        else :
            result=self.passwords
        return {
            'success': True,
            'data': {'passwords': result[:max_results]}
        }

    def auto_fill_personal_info(self, *, website, info_type):
        if info_type not in ['address', 'payment']:
            return {'success': False, 'error': 'Invalid info_type'}
        
        info = self.personal_info.get(info_type)
        if info:
            return {
                'success': True,
                'data': {'website': website, 'filled_info': info}
            }
        else:
            return {'success': False, 'error': f'No {info_type} info stored'}


    def share_password(self, *, website, username, contact_email):
        pwd_to_share = next((pwd for pwd in self.passwords if pwd['website'] == website and pwd['username'] == username), None)
        if pwd_to_share:
            return {
                'success': True,
                'data': {'shared_password': pwd_to_share, 'contact_email': contact_email}
            }
        return {'success': False, 'error': 'Password not found'}


    def enable_two_factor_authentication(self, *, phone_number):
        self.two_factor_enabled = True
        self.contact_email = phone_number
        return {'success': True, 'data': {'message': 'Two-factor authentication enabled', 'phone_number': phone_number}}


    def disable_two_factor_authentication(self):
        self.two_factor_enabled = False
        self.contact_email = None
        return {'success': True, 'data': {'message': 'Two-factor authentication disabled'}}


