from environments.BaseEnv import BaseEnv

class WormholeStabilizer(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def list_settings(self):
        return {'success': True, 'data': self.parameters.get('settings', [])}

    def modify_settings(self, *, parameter_name, value):
        settings = self.parameters.get('settings', {})
        settings[parameter_name] = value
        return {'success': True, 'data': 'Settings updated.'}

    def get_system_status(self):
        return {'success': True, 'data': self.parameters.get('system_status', {})}

    def mask_spatial_anomalies(self, *, shift_id):
        system_status = self.parameters.get('system_status', {})
        active_shifts = system_status.get('active_shifts', [])
        
        for shift in active_shifts:
            if shift['shift_id'] == shift_id:
                active_shifts.remove(shift)
                return {'success': True, 'data': 'Anomalies masked.'}
            
        return {'success': False, 'data': 'Shift not found.'}
    
    