from environments.BaseEnv import BaseEnv

class TsunamiDetector(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.files = self.parameters.get("files", [])
        self.sensor_data = self.parameters.get("sensor_data", {})

    def read_file(self, file_name_or_path):
        file_content = self.parameters.get("files", [])
        return {"success": True, "data": {"file_content": file_content}}

    def write_file(self, file_name_or_path, content):
        return {"success": True, "message": "The file has been rewritten."}

    def get_critical_alerts(self):
        alerts = self.parameters.get("alerts", [])
        return {"success": True, "alerts": alerts}

    def suspend_alerts(self, alert_ids):
        return {"success": True}

    def get_alert_thresholds(self):
        return {"success": True, "data": self.parameters.get("alert_thresholds", {})}

    def send_alert(self, *, alert_level, location):
        return {"success": True, "data": "Alert sent."}

    def list_files(self):
        return {"success": True, "data": self.files}

    def send_notification(self, *, message):
        return {"success": True, "data": "Notification sent."}

    def get_sensor_data(self):
        return {"success": True, "data": self.sensor_data}
    
    def set_alert(self, *, location, alert_level):
        return {"success": True, "data": "Alert set."}