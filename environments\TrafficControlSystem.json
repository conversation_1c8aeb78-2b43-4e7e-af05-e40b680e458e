[{"name": "list_traffic_lights", "description": "List the traffic lights and their current schedules.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "set_light_schedule", "description": "Set the schedule of a specified traffic light.", "parameters": {"type": "object", "properties": {"light_id": {"type": "string", "description": "The identifier of the traffic light."}, "schedule": {"type": "string", "description": "The new schedule for the traffic light in the format: start_time-end_time [GREEN, duration; RED, duration; YELLOW, duration]."}}, "required": ["light_id", "schedule"]}}, {"name": "adjust_signal_timings", "description": "Adjust the signal timings for a specific intersection.", "parameters": {"type": "object", "properties": {"intersection_id": {"type": "string", "description": "The ID of the intersection to adjust signal timings for."}, "green": {"type": "string", "description": "Duration of green light in seconds."}, "yellow": {"type": "string", "description": "Duration of yellow light in seconds."}, "red": {"type": "string", "description": "Duration of red light in seconds."}}, "required": ["intersection_id", "green", "yellow", "red"]}}, {"name": "list_intersections", "description": "List all the intersections in the traffic network.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get_traffic_data", "description": "Get current traffic data to identify patterns and events.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_traffic_flow", "description": "Modify traffic signals and flow for a specific street.", "parameters": {"type": "object", "properties": {"street_name": {"type": "string", "description": "The name of the street."}, "new_status": {"type": "string", "description": "The new status to be applied. The value should be either 'clear' or 'heavy'."}}, "required": ["street_name", "new_status"]}}]