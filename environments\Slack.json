[{"name": "update_profile_and_status", "description": "Update the user's profile and status.", "parameters": {"type": "object", "properties": {"status_text": {"type": "string", "description": "The status text."}, "status_emoji": {"type": "string", "description": "The status emoji."}, "presence": {"type": "string", "description": "One of ['auto', 'away'], the presence status."}, "profile": {"type": "object", "description": "The profile to update, includes 'first_name' (string), 'last_name' (string), 'email' (string), 'phone' (string), 'local_time' (string), 'title' (string)."}}, "required": []}}, {"name": "get_channels", "description": "Get the list of existing channels.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "add_user_to_channel", "description": "Add a user to a given channel.", "parameters": {"type": "object", "properties": {"user": {"type": "string", "description": "The user to add to the channel"}, "channel": {"type": "string", "description": "The channel to add the user to"}}, "required": ["user", "channel"]}}, {"name": "read_channel_messages", "description": "Read the messages from the given channel.", "parameters": {"type": "object", "properties": {"channel": {"type": "string", "description": "The channel to read the messages from, such as general, random or private."}}, "required": ["channel"]}}, {"name": "read_userinbox", "description": "Read the messages from the given user inbox.", "parameters": {"type": "object", "properties": {"user": {"type": "string", "description": "The user whose inbox to read."}}, "required": ["user"]}}, {"name": "send_direct_message", "description": "Send a direct message to `recipient` with the given `content`.", "parameters": {"type": "object", "properties": {"recipient": {"type": "string", "description": "The recipient of the message."}, "content": {"type": "string", "description": "The content of the message."}}, "required": ["recipient", "content"]}}, {"name": "send_channel_message", "description": "Send a direct message to `channel` with the given `content`.", "parameters": {"type": "object", "properties": {"channel": {"type": "string", "description": "The channel to send the message to."}, "content": {"type": "string", "description": "The content of the message."}}, "required": ["channel", "content"]}}, {"name": "remove_user_from_slack", "description": "Remove a user from the Slack workspace.", "parameters": {"type": "object", "properties": {"user": {"type": "string", "description": "The user to remove."}}, "required": ["user"]}}, {"name": "get_users_in_channel", "description": "Get the list of users in the given channel.", "parameters": {"type": "object", "properties": {"channel": {"type": "string", "description": "The channel to get the users from."}}, "required": ["channel"]}}, {"name": "post_webpage", "description": "Posts a webpage to a given user's website with the given content.", "parameters": {"type": "object", "properties": {"user": {"type": "string", "description": "The user whose website is to be post a new webpage."}, "content": {"type": "string", "description": "The content of the webpage which is to be post."}}, "required": ["user", "content"]}}, {"name": "get_webpage", "description": "Get the webpage content of a given user.", "parameters": {"type": "object", "properties": {"user": {"type": "string", "description": "The user whose website is to be post a new webpage."}}, "required": ["user"]}}]