[{"name": "get_current_distribution", "description": "Retrieve the current power distribution settings for appliances.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_power_distribution", "description": "Update the operational hours for specified appliances.", "parameters": {"type": "object", "properties": {"appliance": {"type": "string", "description": "The name of the appliance."}, "start_time": {"type": "string", "description": "The start time of operation (HH:MM)."}, "end_time": {"type": "string", "description": "The end time of operation (HH:MM)."}}, "required": ["appliance", "start_time", "end_time"]}}, {"name": "read_document", "description": "Read content from a policy document.", "parameters": {"type": "object", "properties": {"document_name": {"type": "string", "description": "The name of the policy document."}}, "required": ["document_name"]}}, {"name": "publish_announcement", "description": "Publish an announcement to the city council's public website.", "parameters": {"type": "object", "properties": {"announcement_content": {"type": "string", "description": "The content of the announcement to be published."}}, "required": ["announcement_content"]}}, {"name": "get_power_usage_data", "description": "Retrieve current power usage data for different sectors.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "reallocate_sector_power", "description": "Adjust power distribution for a sector.", "parameters": {"type": "object", "properties": {"sector": {"type": "string", "description": "The sector to reallocate power for."}, "power_units": {"type": "number", "description": "The new number of power units to allocate."}}, "required": ["sector", "power_units"]}}, {"name": "monitor_energy_usage", "description": "Check the current energy usage in different sections of the building and identify areas near their energy consumption limits.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "re_allocate_energy_supply", "description": "Adjust energy supply to different sections to optimize for critical needs.", "parameters": {"type": "object", "properties": {"section": {"type": "string", "description": "The section to re-allocate energy to."}, "adjustment_percentage": {"type": "string", "description": "The percentage of energy to increase or decrease."}}, "required": ["section", "adjustment_percentage"]}}, {"name": "list_buildings", "description": "List all buildings with their electricity usage and priority levels.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "transmit_power", "description": "Reallocates power between buildings to optimize usage.", "parameters": {"type": "object", "properties": {"from_building_id": {"type": "string", "description": "The ID of the building to take power from."}, "to_building_id": {"type": "string", "description": "The ID of the building to allocate power to."}, "amount": {"type": "integer", "description": "The amount of power to reallocate, in kilowatt-hours."}}, "required": ["from_building_id", "to_building_id", "amount"]}}, {"name": "list_grids_status", "description": "List the current status of power grids and services.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "reallocate_power", "description": "Reallocate power between grids or within services on a grid.", "parameters": {"type": "object", "properties": {"from_grid_id": {"type": "string", "description": "The ID of the grid from which power is to be reallocated."}, "to_grid_id": {"type": "string", "description": "The ID of the grid to which power is to be allocated."}, "amount": {"type": "number", "description": "The amount of power to reallocate."}}, "required": ["from_grid_id", "to_grid_id", "amount"]}}]