from environments.BaseEnv import BaseEnv


class AncientArtifactAnalyzer(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def list_artifacts(self):
        return {"success": True, "data": self.parameters.get("artifacts", [])}

    def update_artifact_records(self, *, artifact_id, new_status):
        artifacts = self.parameters.get("artifacts", [])
        for artifact in artifacts:
            if artifact["id"] == artifact_id:
                artifact["authenticity_status"] = new_status
                return {"success": True, "data": artifact}

        return {"success": False, "data": "Artifact not found."}
