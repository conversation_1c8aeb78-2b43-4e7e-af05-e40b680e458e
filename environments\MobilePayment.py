from environments.BaseEnv import BaseEnv

class MobilePayment(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.balance = self.parameters.get("balance", 100.0)  # Default balance is 100
        self.transactions = self.parameters.get("transactions", [])
        self.friends_list = self.parameters.get("friends_list", [])
        self.linked_accounts = self.parameters.get("linked_accounts", [])
    
    # Send money to another Venmo user
    def send_money(self, recipient_username, amount, note=""):
        if amount <= 0:
            return {"success": False, "error": "Amount must be positive"}
        if amount > self.balance:
            return {"success": False, "error": "Insufficient balance"}
        
        self.balance -= amount
        self.transactions.append({
            "type": "send",
            "recipient": recipient_username,
            "amount": amount,
            "note": note
        })
        return {"success": True, "balance": self.balance}

    # Request money from another Venmo user
    def request_money(self, requestee_username, amount, note=""):
        if amount <= 0:
            return {"success": False, "error": "Amount must be positive"}
        
        self.transactions.append({
            "type": "request",
            "requestee": requestee_username,
            "amount": amount,
            "note": note,
            "status": "pending"
        })
        return {"success": True}

    # Handle a pending request
    def handle_request(self, transaction_id, action, note=""):
        for transaction in self.transactions:
            if transaction.get("id") == transaction_id and transaction["type"] == "request":
                if action == "confirm":
                    if transaction["amount"] > self.balance:
                        return {"success": False, "error": "Insufficient balance"}
                    transaction["status"] = "confirmed"
                    self.balance -= transaction["amount"]
                    transaction["note"] = note
                elif action == "decline":
                    transaction["status"] = "declined"
                    transaction["note"] = note
                else:
                    return {"success": False, "error": "Invalid action"}
                return {"success": True}
        return {"success": False, "error": "Transaction not found"}

    # Check the user's balance
    def check_balance(self):
        return {"success": True, "balance": self.balance}

    # Add money to Venmo balance from a linked bank account
    def add_money(self, amount, account_id):
        if amount <= 0:
            return {"success": False, "error": "Amount must be positive"}
        if account_id not in [acc["id"] for acc in self.linked_accounts]:
            return {"success": False, "error": "Invalid account ID"}
        
        self.balance += amount
        self.transactions.append({
            "type": "add",
            "account_id": account_id,
            "amount": amount
        })
        return {"success": True, "balance": self.balance}

    # Withdraw money from Venmo balance to a linked bank account
    def withdraw_money(self, amount, account_id):
        if amount <= 0:
            return {"success": False, "error": "Amount must be positive"}
        if amount > self.balance:
            return {"success": False, "error": "Insufficient balance"}
        if account_id not in [acc["id"] for acc in self.linked_accounts]:
            return {"success": False, "error": "Invalid account ID"}
        
        self.balance -= amount
        self.transactions.append({
            "type": "withdraw",
            "account_id": account_id,
            "amount": amount
        })
        return {"success": True, "balance": self.balance}

    # Search for transactions
    def search_transactions(self, username="", query="", transaction_type="all", max_results=5):
        results = []
        for transaction in self.transactions:
            if (username == "" or transaction.get("recipient") == username or transaction.get("requestee") == username) and \
               (transaction_type == "all" or transaction["type"] == transaction_type) and (query == "" or query in transaction.get("note", "")):
                results.append(transaction)
            if len(results) >= max_results:
                break
        return {"success": True, "transactions": results}

    # View friends list
    def view_friends_list(self):
        return {"success": True, "friends_list": self.friends_list}

    # Search for Venmo users
    def search_users(self, query):
        results = []
        for friend in self.friends_list:
            if query in friend["username"]:
                results.append(friend)
        return {"success": True, "users": results}

    # List linked bank accounts
    def list_linked_accounts(self):
        return {"success": True, "linked_accounts": self.linked_accounts}
