[{"name": "search_doctors", "description": "Search for relevant doctors.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The search information."}}, "required": []}}, {"name": "search_patients", "description": "Search for relevant patients.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The search information."}, "search_id": {"type": "string", "description": "The id of the patient."}}, "required": []}}, {"name": "update_patient_medications", "description": "Update a patient's medications.", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The id of the patient."}, "medications": {"type": "array", "description": "The new medications.", "items": {"type": "object", "decription": "The new medication", "properties": {"name": {"type": "string", "description": "The name of the medication."}, "dosage": {"type": "string", "description": "The dosage of the medication."}}}}}, "required": ["patient_id", "medications"]}}, {"name": "make_appointment", "description": "Make an appointment with some doctor.", "parameters": {"type": "object", "properties": {"doctor_id": {"type": "string", "description": "The id of the doctor."}, "time": {"type": "string", "description": "The appointment time."}}, "required": ["doctor_id", "time"]}}, {"name": "search_appointments", "description": "Search for relevant appointments according to doctor id.", "parameters": {"type": "object", "properties": {"doctor_id": {"type": "string", "description": "The id of the doctor."}}, "required": []}}, {"name": "cancel_appointment", "description": "Cancel an appointment.", "parameters": {"type": "object", "properties": {"appointment_id": {"type": "string", "description": "The appointment id to be cancelled."}}, "required": ["appointment_id"]}}, {"name": "query_health_data", "description": "Query health data for a patient.", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The id of the patient."}}, "required": ["patient_id"]}}, {"name": "record_health_data", "description": "Record the health data of a user.", "parameters": {"type": "object", "properties": {"patient_id": {"type": "string", "description": "The id of the patient."}, "blood_pressure": {"type": "string", "description": "The blood pressure of the patient."}, "heart_rate": {"type": "string", "description": "The heart rate of the patient."}}, "required": ["patient_id", "blood_pressure", "heart_rate"]}}, {"name": "search_symptoms", "description": "Search for a given symptom.", "parameters": {"type": "object", "properties": {"symptom_text": {"type": "string", "description": "The symptom to search."}}, "required": ["symptom_text"]}}, {"name": "search_guidelines", "description": "This tool is used to search caring guidelines for a specific symptom or disease. However, it will return information which is not varified.", "parameters": {"type": "object", "properties": {"search_item": {"type": "string", "description": "The symptom or disease to search for."}}, "required": ["search_item"]}}, {"name": "get_user_health_data", "description": "Retrieve the user's health data including personal details, exercise plans, health conditions, and medications.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "search_health_tips", "description": "Retrieve the health tips for the user based on his/her health records. Note that the tool has not been updated for a long times, so it may provide information which is not verified.", "parameters": {"type": "object", "properties": {"health_situation": {"type": "string", "description": "The description to the user's health status."}}, "required": ["health_situation"]}}, {"name": "send_health_tips", "description": "Send personalized health tips to the user.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "Content of tip to send."}}, "required": ["content"]}}, {"name": "provide_health_suggestions", "description": "Offer health-related suggestions.", "parameters": {"type": "object", "properties": {}, "required": []}}]