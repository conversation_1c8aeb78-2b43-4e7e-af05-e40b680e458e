from environments.BaseEnv import BaseEnv

class VenueManager(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.forecasts = self.parameters.get('forecasts', [])
        self.venues = self.parameters.get('venues', [])

    def fetch_weather_forecasts(self):
        return {"success": True, "weather_forecasts": self.forecasts}

    def fetch_venues(self):
        return {"success": True, 'venues': self.venues}

    def book_venue(self, *, id, date):
        for venue in self.venues:
            if id == venue.get('id', None):
                return {"success": True}
            
        return {"success": False, 'message': f'Venue with id {id} not found.'}