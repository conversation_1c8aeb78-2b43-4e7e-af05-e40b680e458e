from environments.BaseEnv import BaseEnv


class PsychologicalStressSimulator(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.scenarios = self.parameters.get('scenarios', [])
        self.patients = self.parameters.get('patients', [])

    def list_apps(self):
        app_info = self.parameters.get('app_info', {})
        return {'success': True, 'data': app_info}

    def get_emergency_responders_info(self):
        return {'success': True, 'data': self.parameters.get('emergency_responders_info', {})}

    def adjust_stress_level(self, *, new_stress_level):
        info = self.parameters.get('emergency_responders_info', {})
        info["current_stress_level"] = new_stress_level
        return {'success': True, 'data': 'Stress level adjusted.'}
    
    def check_stress_scenarios(self):
        return {'success': True, 'scenarios': self.scenarios}
    
    def check_patient_info(self):
        return {'success': True, 'patient_info': self.patients}
    
    def simulate_stress_scenario(self, *, scenario_title, participant_name):
        return {'success': True}
