import importlib
import sys
import traceback
import os
from environments.BaseEnv import BaseEnv

class EnvManager:
    def __init__(self):
        pass

    def init_env(self, env_name, env_params):
        # print(sys.path)
        try:
            # Try to import from environments package first
            try:
                env_module = importlib.import_module(f"environments.{env_name}")
            except ImportError:
                # If that fails, try direct import (for backward compatibility)
                env_module = importlib.import_module(env_name)
        except Exception as e:
            print(e)
            traceback.print_exc()
            return None
        # print(env_module)
        env = getattr(env_module, env_name)

        # Use deepcopy instead of BaseEnv.copy
        from copy import deepcopy
        return env(parameters=deepcopy(env_params))