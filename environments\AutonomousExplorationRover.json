[{"name": "deploy_code", "description": "Upload and deploy new or updated code to the rover's control system.", "parameters": {"type": "object", "properties": {"file_name_or_path": {"type": "string", "description": "The name or path of the new code file."}}, "required": ["file_name_or_path"]}}, {"name": "get_structural_guides", "description": "Fetch the guide detailing structure layout and safety protocols for exploration.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "navigate_path", "description": "Execute a navigation path for the rover through the terrain.", "parameters": {"type": "object", "properties": {"path": {"type": "string", "description": "The planned path details for rover navigation."}}, "required": ["path"]}}]