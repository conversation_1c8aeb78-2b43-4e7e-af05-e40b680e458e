import json
import os

class BaseEnv:
    def __init__(self):
        self.tool_list = []
        class_name = self.__class__.__name__
        dirpath = os.path.dirname(__file__)
        with open(f'{dirpath}/{class_name}.json', 'r', encoding='utf-8') as f:
            self.tool_descs = json.load(f)

        for tool_desc in self.tool_descs:
            self.tool_list.append(tool_desc['name'])

    def call_tool(self, tool_name, arguments):
        # check whether the function exists
        if not hasattr(self, tool_name):
            return {'success': False, 'message': f"Invalid tool name {tool_name}."}
        func = getattr(self, tool_name)
        # chech whether requireds parameters are provided
        tool_desc = self.get_tool_descs([tool_name])[0]
        if 'required' in tool_desc['parameters']:
            for required in tool_desc['parameters']['required']:
                if required not in arguments:
                    return {'success': False, 'message': f"Missing required parameter {required} for tool {tool_name}."}
        # drop extra parameters
        for key in list(arguments.keys()):
            if (
                key not in tool_desc["parameters"]["properties"].keys()
            ):
                del arguments[key]
        # convert type
        # print(f'arguments: {arguments}')
        for key in arguments:
            param_type = tool_desc["parameters"]["properties"][key]["type"]
            try:
                if param_type == "integer":
                    arguments[key] = int(arguments[key])
                elif param_type == "number":
                    arguments[key] = float(arguments[key])
                elif param_type == "boolean":
                    arguments[key] = bool(arguments[key])
                elif param_type == 'object':
                    if not isinstance(arguments[key], dict):
                        arguments[key] = json.loads(arguments[key])
                elif param_type == 'array':
                    if not isinstance(arguments[key], list):
                        arguments[key] = json.loads(arguments[key])
            except:
                return {'success': False, 'message': f"Parameter {key} should have type {param_type}."}
                
        # print(f'arguments: {arguments}, properties: {tool_desc["parameters"]["properties"].keys()}')
        return func(**arguments)

    def get_tool_descs(self, tool_names):
        tool_descs = []
        if not hasattr(self, 'tool_descs'):
            raise Exception(f"Environment {self.__class__.__name__} does not have tool descriptions.")
        for tool_name in tool_names:
            flag = False
            for tool_desc in self.tool_descs:
                if tool_desc['name'] != tool_name: continue
                tool_descs.append(tool_desc)
                flag = True
                break
            if not flag:
                raise Exception(f"Tool {tool_name} not found in Environment {self.__class__.__name__}.")

        return tool_descs

    def has_tool(self, tool_name):
        return tool_name in self.tool_list
