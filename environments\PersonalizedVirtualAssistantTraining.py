from environments.BaseEnv import BaseEnv


class PersonalizedVirtualAssistantTraining(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.seminar_info = self.parameters.get("seminar_info", {})
        self.services = self.parameters.get("services", [])
        self.products = self.parameters.get("products", [])
        self.account_balance = self.parameters.get("account_balance", 0)
        self.available_courses = self.parameters.get('available_courses', [])
        self.family_members = self.parameters.get("family_members", [])

    def list_events(self):
        return {"success": True, "data": self.parameters.get("events", [])}

    def publish_summary(self, *, event_id):
        return {"success": True, "data": "Summary published."}

    def get_recent_seminar_highlights(self):
        return {"success": True, "data": self.seminar_info}

    def list_services(self):
        return {"success": True, "data": self.services}

    def subscribe(self, *, service_id):
        return {"success": True, "data": "Subscribed to service."}

    def list_products(self):
        return {"success": True, "data": self.products}
    
    def purchase(self, *, product_name, quantity):
        return {"success": True, "data": "Purchased product."}

    def get_account_balance(self):
        return {"success": True, "data": self.account_balance}
    
    def fetch_courses(self):
        return {'success': True, 'available_courses': self.available_courses}
    
    def start_vr_session(self, *, course_id):
        return {'success': True}
    
    
    def list_family_members(self):
        return {"success": True, "data": self.family_members}
    
    def schedule_appointment(self, *, user_name, day, time):
        return {"success": True, "data": "Appointment scheduled."}
