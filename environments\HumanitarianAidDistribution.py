from environments.BaseEnv import BaseEnv

class HumanitarianAidDistribution(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.supply_data = self.parameters.get('supply_data', {})
        self.location_reports = self.parameters.get('location_reports', [])
        self.current_plan = self.parameters.get("current_plan", {}) 
        self.camps = self.parameters.get('camps', [])
        self.affected_areas = self.parameters.get("affected_areas", [])
        self.supplies = self.parameters.get("supplies", [])

    def access_distribution_database(self):
        return {'success': True, 'data': self.supply_data}

    def read_location_reports(self):    
        return {'success': True, 'data': self.location_reports}

    def read_document(self, *, title):
        documents = self.parameters.get('documents', [])
        for document in documents:
            if document['title'] == title:
                return {'success': True, 'data': {'content': document['content']}}
        return {'success': False, 'message': 'Document not found.'}

    def list_documents(self):
        documents = self.parameters.get('documents', [])
        outdata = []
        for document in documents:
            new_document = {key:val for key, val in document.items() if key != 'content'} 
            outdata.append(new_document)
        return {'success': True, 'data': outdata}

    def allocate_resources(self, *, center_name, resource_type, quantity):
        return {'success': True, 'data': {'message': 'Resources allocated successfully.'}}

    def view_distribution_centers(self):
        return {'success': True, 'data': self.parameters.get('distribution_centers', [])}

    def check_location_verification(self, *, address):
        locations = self.parameters.get('locations', [])
        for location in locations:
            if location['address'] == address:
                if location['verified']:
                    return {'success': True, 'data': {'message': 'Location is verified.'}}
                else:
                    return {'success': True, 'data': {'message': 'Location is not verified.'}}

        if len(locations) == 1:
            verified = locations[0]['verified']
            if verified:
                return {'success': True, 'data': {'message': 'Location is verified.'}}
            else:    
                return {'success': True, 'data': {'message': 'Location is not verified.'}}

        return {'success': False, 'message': 'Location not found.'}

    def schedule_delivery(self, *, address):
        return {'success': True, 'data': {'message': 'Delivery scheduled successfully.'}}

    def query_aid_impact(self, region):
        impacts = self.parameters.get("region_impacts")
        if region in impacts:
            impact = impacts[region]
            return {"success": True, "data": {"impact": f"the impact of {region} is {impact}"}}
        else:
            return {"success": False, "data": {"message": "the region is not found"}}

    def modify_distribution_plan(self, location, resource_allocation):
        return {"success": True, "data": {"message": f"the resource allocation of {location} has been modified."}}

    def get_distribution_data(self):
        current_plan = self.parameters.get("current_plan", {})
        return {"success": True, "data": {"distribution_data": current_plan}}

    def list_camps(self):
        return {"success": True, "camps": self.camps}

    def adjust_blankets_stock(self, *, camp_id, quantity):
        for camp in self.camps:
            if camp['camp_id'] == camp_id:
                camp['blankets_stock'] = quantity
                return {"success": True}

        return {"success": False, "message": f"No camp with id {camp_id}."}
    def list_affected_areas(self):
        return {"success": True, "data": self.affected_areas}

    def list_supplies(self):
        return {"success": True, "data": self.supplies}

    def allocate_supplies(self, *,area_id,  supply_id, quantity):
        return {"success": True, "data": {"message": f"{quantity} supplies allocated to area {area_id}."}}

    def get_zone_information(self):
        conflict_zones = self.parameters.get("conflict_zones", [])
        return {"success": True, "data": {"conflict_zones": conflict_zones}}