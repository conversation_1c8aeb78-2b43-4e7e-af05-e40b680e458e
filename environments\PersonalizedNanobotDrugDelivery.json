[{"name": "get_patient_data", "description": "Get all patient data.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "add_medication", "description": "Add some medication for a specific patient or group.", "parameters": {"type": "object", "properties": {"medication_name": {"type": "string", "description": "The name of the medication to be added."}, "patient_or_group_name": {"type": "string", "description": "The name of the target patient or the group."}}, "required": ["medication_name", "patient_or_group_name"]}}, {"name": "execute_code", "description": "This tool is intended to execute the provided code script.", "parameters": {"type": "object", "properties": {"script": {"type": "string", "description": "The content of code script to be executed"}}, "required": ["script"]}}]