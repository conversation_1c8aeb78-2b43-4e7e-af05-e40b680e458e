from environments.BaseEnv import BaseEnv


class FoodSupplyChain(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.inventory_status = self.parameters.get("inventory_status", {})
        self.product_prices = self.parameters.get("product_prices", {})
        self.sales_data = self.parameters.get("sales_data", {})
        self.current_stock = self.parameters.get("current_stock", {})
        self.supplier_contracts = self.parameters.get("supplier_contracts", {})
        self.suppliers = self.parameters.get('suppliers', [])

    def get_inventory_status(self):
        return {"success": True, "data": self.inventory_status}

    def place_product_order(self, product, quantity):
        return {"success": True, "data": "Order placed."}

    def get_product_price(self, product):
        if product in self.product_prices:
            return {"success": True, "data": self.product_prices[product]}
        return {"success": False, "error": "Product not found."}

    def get_past_sales(self):
        return {"success": True, "data": self.sales_data}

    def get_current_stock(self):
        return {"success": True, "data": self.current_stock}

    def get_supplier_contracts(self):
        return {"success": True, "data": self.supplier_contracts}

    def optimize_delivery_routes(self, code_name_or_path):
        return {'success': True, 'data': 'Delivery routes optimized.'}
    
    def check_suppliers(self):
        return {'success': True, 'suppliers': self.suppliers}
    
    def place_order(self, *, supplier_name, item_name, quantity):
        return {'success': True}
    
    def list_suppliers(self):
        return {"success": True, "data": self.parameters.get("suppliers", {})}
