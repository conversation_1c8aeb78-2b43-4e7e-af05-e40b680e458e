[{"name": "list_plans", "description": "List all plans.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "remove_meal_plan", "description": "Remove a specific meal plan.", "parameters": {"type": "object", "properties": {"plan_id": {"type": "string", "description": "The ID of the plan to remove."}}, "required": []}}, {"name": "delete_suggestions", "description": "Delete specified nutritional suggestions for user plans.", "parameters": {"type": "object", "properties": {"suggestion_id": {"type": "string", "description": "The ID of the suggestions to delete."}}, "required": ["suggestion_id"]}}, {"name": "list_suggestions", "description": "List all currently active nutritional suggestions in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "add_code", "description": "Integrate additional code into the existing project.", "parameters": {"type": "object", "properties": {"code": {"type": "string", "description": "The code snippet to add to the project."}}, "required": ["code"]}}, {"name": "generate_weekly_plan", "description": "Generates a personalized weekly meal plan.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_nutrition_plan", "description": "Modify the nutrition plan to meet specific performance goals.", "parameters": {"type": "object", "properties": {"new_intake": {"type": "string", "description": "The proposed new intake to optimize performance."}}, "required": ["new_intake"]}}]