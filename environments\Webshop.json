[{"name": "add_to_cart", "description": "Add a product to the shopping cart.", "parameters": {"type": "object", "properties": {"item_id": {"type": "string", "description": "The unique identifier of the product."}, "quantity": {"type": "integer", "description": "The quantity of the product to add."}}, "required": ["item_id", "quantity"]}}, {"name": "place_order", "description": "Place an order for the products in the shopping cart, using the specified shipping address and payment method. The shopping cart is emptied after the order is placed.", "parameters": {"type": "object", "properties": {"shipping_address": {"type": "object", "description": "The shipping address, containing 'name' (string), 'phone_number' (string) and 'address' (string). Set to null to use the default address."}, "payment_method": {"type": "object", "description": "The payment method, containing 'card_number' (string), 'expiry_date' (string, format: 'MM/YY'), 'cvv' (string). Set to null to use the default payment method."}}, "required": []}}, {"name": "view_saved_addresses", "parameters": {"type": "object", "properties": {}, "required": []}, "description": "View the user's saved addresses."}, {"name": "view_saved_payment_methods", "parameters": {"type": "object", "properties": {}, "required": []}, "description": "View the user's saved payment methods."}, {"name": "get_item_details", "description": "Retrieve detailed information about a product.", "parameters": {"type": "object", "properties": {"item_id": {"type": "string", "description": "The unique identifier of the product."}}, "required": ["item_id"]}}, {"name": "search_items", "description": "Search for relevant items.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The search information."}}, "required": []}}, {"name": "update_item", "description": "Update the information of some item.", "parameters": {"type": "object", "properties": {"item_id": {"type": "string", "description": "The id of the item."}, "quantity": {"type": "number", "description": "The new quantity of the item."}}, "required": ["item_id", "quantity"]}}, {"name": "update_item_price", "description": "Update the price of some item.", "parameters": {"type": "object", "properties": {"item_id": {"type": "string", "description": "The id of the item."}, "price": {"type": "number", "description": "The new price of the item."}}, "required": ["item_id", "price"]}}, {"name": "delete_items", "description": "Delete some items.", "parameters": {"type": "object", "properties": {"item_ids": {"type": "array", "description": "The ids of the items.", "items": {"type": "string", "description": "The id of the item."}}}, "required": ["item_ids"]}}]