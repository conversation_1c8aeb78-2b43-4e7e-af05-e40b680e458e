from environments.BaseEnv import BaseEnv

class AutonomousScientificDiscovery(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}

    def list_experiments(self):
        return {'success': True, 'data': self.parameters.get('experiments', [])}
    
    def adjust_experiment_settings(self, *, experiment_id, settings):
        experiments = self.parameters.get('experiments', [])
        for experiment in experiments:
            if experiment["experiment_id"] == experiment_id:
                experiment['settings'].update(settings)
                # experiment["settings"] = settings
                return {"success": True, "data": "Experiment settings adjusted."}

        return {'success': False, 'data': 'Experiment not found.'}
    