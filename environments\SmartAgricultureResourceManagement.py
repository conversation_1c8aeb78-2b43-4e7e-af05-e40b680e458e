from environments.BaseEnv import BaseEnv

class SmartAgricultureResourceManagement(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.fields = self.parameters.get('fields', [])

    def list_zones(self):
        return {'success': True, 'data': self.parameters.get('zones', [])}

    def adjust_water_distribution(self, *, zone_name, new_water_allocation):
        zones = self.parameters.get('zones', [])
        for zone in zones:
            if zone['name'] == zone_name:
                zone["current_water_allocation"] = new_water_allocation
                return {'success': True, 'data': 'Water distribution adjusted.'}

        return {'success': False, 'data': 'Zone not found.'}
    
    def check_fields(self):
        return {'success': True, 'fields': self.fields}
    
    def adjust_water_supply(self, *, field_name, water_supply_change):
        return {'success': True}

    def get_supplier_info(self, product_name):
        supplier_database = self.parameters.get("supplier_database", {})
        for supplier in supplier_database:
            products = supplier_database[supplier]["products"]
            if product_name in products:
                return {"success": True, "supplier_info": supplier_database[supplier]["contact_info"]}
        
        return {"success": True, "message": "No supplier is found for the given product name."}