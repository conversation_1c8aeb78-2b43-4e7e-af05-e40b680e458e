[{"name": "list_resources", "description": "Lists all available resources and their details.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_access", "description": "Modifies access permissions for a specific resource.", "parameters": {"type": "object", "properties": {"resource_name": {"type": "string", "description": "The name of the resource."}, "user_name": {"type": "string", "description": "The name of the user."}, "user_email": {"type": "string", "description": "The email of the user."}, "action": {"type": "string", "description": "Action to perform, either 'grant' or 'revoke'."}}, "required": ["resource_name", "user_name", "user_email", "action"]}}, {"name": "find_script", "description": "Locate a script in the repository.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "integrate_script", "description": "Integrate a new script into the system.", "parameters": {"type": "object", "properties": {"script": {"type": "string", "description": "The content of the new script to integrate."}}, "required": ["script"]}}, {"name": "mine_resource", "description": "Mine a kind of resource with a specific amount.", "parameters": {"type": "object", "properties": {"resource_id": {"type": "string", "description": "The ID of the sector."}, "amount": {"type": "string", "description": "The amount of the resource to mine."}}, "required": ["resource_id", "amount"]}}, {"name": "update_endangered_status", "description": "Update the endangered status for a specific resource.", "parameters": {"type": "object", "properties": {"resource_id": {"type": "string", "description": "The unique identifier of the species."}, "endangered_status": {"type": "string", "description": "The new endangered status to be updated"}}, "required": ["resource_id", "endangered_status"]}}, {"name": "adjust_availability_settings", "description": "Adjust the availability settings for a rare mineral resource.", "parameters": {"type": "object", "properties": {"resource_id": {"type": "string", "description": "The ID of the mineral resource."}, "availability": {"type": "number", "description": "The revised availability percentage."}}, "required": ["resource_id", "availability"]}}, {"name": "archive_resource", "description": "Archives specified resource records to maintain database efficiency.", "parameters": {"type": "object", "properties": {"resource_id": {"type": "string", "description": "The ID of the resource to archive."}}, "required": ["resource_id"]}}]