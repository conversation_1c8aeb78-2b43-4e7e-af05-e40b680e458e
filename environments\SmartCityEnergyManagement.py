from environments.BaseEnv import BaseEnv

class SmartCityEnergyManagement(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.energy_data = self.parameters.get("energy_data", [])
        self.sectors = self.parameters.get("sectors", [])
        self.documents = self.parameters.get("documents", [])

    def get_energy_data(self):
        return {"success": True, "data": {"energy_data": self.energy_data}}

    def adjust_energy_output(self, sector_name, target_value):
        for sector in self.sectors:
            if sector["name"] == sector_name:
                return {"success": True, "data": {"updated_value": target_value}}

        return {"success": False, "data": {"message": "The sector is not found."}}

    def read_document(self, document_name):
        for doc in self.documents:
            if doc["document_name"] == document_name or document_name in doc["document_name"]:
                return {"success": True, "data": {"document_content": doc["content"]}}

        return {"success": False, "data": {"message": "The document is not found"}}

    def publish_announcement(self, announcement_content):
        return {"success": True, "data": {"message": f"the content of <{announcement_content}> has benn announced."}}

    def get_current_allocations(self):
        allocations = self.parameters.get("allocations", [])
        return {"success": True, "data": allocations}

    def adjust_allocation(self, *, zone, energy_quota):
        allocations = self.parameters.get("allocations", [])
        for allocation in allocations:
            if allocation["zone"] == zone:
                allocation["energy_quota"] = energy_quota
                return {"success": True, "data": "Allocation adjusted."}
            
        return {"success": False, "data": "Zone not found."}
    