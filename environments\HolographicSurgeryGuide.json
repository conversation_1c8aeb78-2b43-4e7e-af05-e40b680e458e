[{"name": "list_instruments", "description": "List available surgical instruments.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_instrument_availability", "description": "Update the number of available units of a specific instrument.", "parameters": {"type": "object", "properties": {"instrument_id": {"type": "string", "description": "The ID of the instrument."}, "availability": {"type": "number", "description": "The new availability of the instrument."}}, "required": ["instrument_id", "availability"]}}, {"name": "delete_patient_data", "description": "Remove any non-essential patient records from the system without compromising ongoing surgery information.", "parameters": {"type": "object", "properties": {"record_id": {"type": "string"}}, "required": ["record_id"]}}, {"name": "list_patient_data", "description": "List all patient records stored in the system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_firmwares", "description": "Retrieve a list of available surgical firmwares and their status.", "parameters": {"type": "object", "properties": {}}, "required": []}, {"name": "update_firmware", "description": "Apply an update package to a given surgical firmware.", "parameters": {"type": "object", "properties": {"firmware_name": {"type": "string", "description": "Name of the firmware to update."}, "update_package": {"type": "string", "description": "The update package file for the firmware."}}, "required": ["firmware_name", "update_package"]}}]