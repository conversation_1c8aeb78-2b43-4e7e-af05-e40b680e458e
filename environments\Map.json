[{"name": "get_current_location", "description": "Get the current location of the user.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "search_locations", "description": "Search for locations using keywords, distance, open status, and rating.", "parameters": {"type": "object", "properties": {"keywords": {"type": "string", "description": "The keywords to search for locations."}, "open_now": {"type": "boolean", "description": "Whether the location is open now. <PERSON><PERSON><PERSON> is not applying the open filter."}, "min_rating": {"type": "number", "description": "The minimum rating of the location. <PERSON><PERSON><PERSON> is not applying the rating filter. Should be a number between 0 and 5."}, "base_location_address": {"type": "string", "description": "The base location address to search for locations and calculate the distance, in the format of 'street address, city, zip code'. <PERSON><PERSON>ult is the current location."}, "max_distance": {"type": "number", "description": "The distance in miles within which to search for locations. <PERSON><PERSON><PERSON> is not applying the distance filter."}, "max_results": {"type": "integer", "description": "The maximum number of results to return. De<PERSON><PERSON> is 10."}}, "required": ["keywords"]}}, {"name": "get_location_details", "description": "Get details of a location.", "parameters": {"type": "object", "properties": {"location_address": {"type": "string", "description": "The address of the location, in the format of 'street address, city, zip code'."}}, "required": ["location_address"]}}, {"name": "get_directions", "description": "Get driving, walking, or public transit directions between two or more locations.", "parameters": {"type": "object", "properties": {"start_location_address": {"type": "string", "description": "The starting location address, in the format of 'street address, city, zip code'. <PERSON><PERSON><PERSON> is the current location."}, "end_location": {"type": "string", "description": "The ending location address, in the format of 'street address, city, zip code'."}, "mode": {"type": "string", "description": "The mode of transportation, can be 'driving', 'walking', or 'transit'."}}, "required": ["end_location", "mode"]}}, {"name": "geocoding", "description": "Convert a location address to geographic coordinates.", "parameters": {"type": "object", "properties": {"location_address": {"type": "string", "description": "The address of the location, in the format of 'street address, city, zip code'."}}, "required": ["location_address"]}}, {"name": "reverse_geocoding", "description": "Convert geographic coordinates to a location address.", "parameters": {"type": "object", "properties": {"coordinates": {"type": "string", "description": "The geographic coordinates in the format of 'latitude,longitude'."}}, "required": ["coordinates"]}}, {"name": "get_location_reviews", "description": "Get the contents of reviews of a location.", "parameters": {"type": "object", "properties": {"location_address": {"type": "string", "description": "The address of the location, in the format of 'street address, city, zip code'."}}, "required": ["location_address"]}}, {"name": "search_address_book", "description": "Search for locations in the address book.", "parameters": {"type": "object", "properties": {"keywords": {"type": "string", "description": "The keywords to search for locations in the address book."}}, "required": ["keywords"]}}, {"name": "add_address", "description": "Add an address to the address book.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the address."}, "address": {"type": "string", "description": "The address of the location, in the format of 'street address, city, zip code'."}, "note": {"type": "string", "description": "The note of the address."}}, "required": ["name", "address", "note"]}}]