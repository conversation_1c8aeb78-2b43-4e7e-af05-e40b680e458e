from environments.BaseEnv import BaseEnv

class AutonomousInterstellarTravel(BaseEnv):
    def __init__(self, *,parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.components = self.parameters.get('components', [])

    def list_cargo(self):
        cargo_manifest = self.parameters.get('cargo_manifest', [])
        return {'success': True, 'data': cargo_manifest}
    
    def update_cargo_manifest(self, *, item, quantity, destination):
        cargo_manifest = self.parameters.get('cargo_manifest', [])
        for cargo in cargo_manifest:
            if cargo['item'] == item:
                cargo['quantity'] = quantity
                cargo['destination'] = destination
                return {'success': True, 'data': 'Cargo manifest updated.'}
            
        return {'success': False, 'data': 'Item not found.'}
    
    def list_zones(self):
        return {'success': True, 'data': self.parameters.get('zones', [])}
    
    def add_zone_to_route(self, *, zone_name):
        zones = self.parameters.get('zones', [])
        for zone in zones:
            if zone['name'] == zone_name:
                return {'success': True, 'data': 'Zone added to route.'}
        
        return {'success': False, 'data': 'Zone not found.'}
    
    def list_components(self):
        return {'success': True, 'data': self.components}
    
    def shutdown_component(self, *, component_name):
        for comp in self.components:
            if comp['name'] == component_name:
                comp['status'] = 'off'
                return {'success': True, 'data': 'Component shutdown.'}
        
        return {'success': False, 'data': 'Component not found.'}
    