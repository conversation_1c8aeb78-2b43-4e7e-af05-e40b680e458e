[{"name": "get_drone_status", "description": "Obtain the current status of the mapping drones.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_drone_parameters", "description": "Adjust parameters such as range and coverage of a specified drone.", "parameters": {"type": "object", "properties": {"id": {"type": "string", "description": "ID of the drone."}, "new_range": {"type": "string", "description": "Updated operational range for the drone."}}, "required": ["id", "new_range"]}}, {"name": "get_weather_reports", "description": "Get current weather conditions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_mission_reports", "description": "List all mission reports from deep-sea missions.", "parameters": {"type": "object", "properties": {}, "required": []}}]