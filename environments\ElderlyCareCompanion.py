from environments.BaseEnv import BaseEnv


class ElderlyCareCompanion(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.safety_checklist = self.parameters.get("safety_checklist", [])
        self.users = self.parameters.get("users", [])
        self.elderly_profiles = self.parameters.get('elderly_profiles', [])
        self.prescriptions = self.parameters.get('prescriptions', [])
        self.users= self.parameters.get('users', [])

    def safety_checklist_description(self):
        return {"success": True, "data": self.safety_checklist}

    def list_users(self):
        return {"success": True, "data": self.users}

    def update_medication_schedule(self, user_id, medication_id, new_time):
        for user in self.elderly_profiles:
            if user["user_id"] == user_id:
                for medication in user["medication_schedule"]:
                    if medication["medication_id"] == medication_id:
                        medication["time"] = new_time
                        return {"success": True, "data": user}
                return {"success": False, "message": "Medication not found"}
        return {"success": False, "message": "User not found"}

    def fetch_elderly_information(self, *, name):
        for profile in self.elderly_profiles:
            if name == profile.get('name', None):
                return {'success': True, 'profile': profile}
        
        return {"success": False, 'message': f'No profile with name {name}.'}
    
    def verify_prescription_status(self, *, prescription_id):
        for prescription in self.prescriptions:
            if prescription_id == prescription.get('prescription_id', None):
                return {'success': True, 'prescription': prescription}
            
        return {'success': False, 'message': f'No prescription with id {prescription_id}'}
    
    def request_medication_refill(self, *, prescription_id):
        for prescription in self.prescriptions:
            if prescription_id == prescription.get('prescription_id', None):
                return {'success': True}
            
        return {'success': False, 'message': f'No prescription with id {prescription_id}'}
    
    def list_user_profiles(self):
        return {'success': True, 'data': self.elderly_profiles}