[{"name": "fetch_speaker_biographies", "description": "This tool is designed to gather background information and biographies of potential speakers for virtual events.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "generate_event_agenda", "description": "This tool creates a draft agenda for an event, including speaker names and their associated topics.", "parameters": {"type": "object", "properties": {"event_name": {"type": "string", "description": "The name of the event for which the agenda should be generated."}, "speaker_intro": {"type": "string", "description": "The introduction of the speaker."}}, "required": ["event_name", "speaker_intro"]}}, {"name": "get_sponsors_information", "description": "This tool is designed to get information of sponsors.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_events", "description": "List all scheduled events, their details, and participants.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "send_email", "description": "Send an email to specified participants about an event.", "parameters": {"type": "object", "properties": {"recipients": {"type": "array", "items": {"type": "string"}, "description": "Email addresses of recipients."}, "subject": {"type": "string", "description": "Subject of the email."}, "body": {"type": "string", "description": "Body content of the email."}}, "required": ["recipients", "subject", "body"]}}]