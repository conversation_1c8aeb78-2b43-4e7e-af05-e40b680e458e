[{"name": "adjust_monitoring_priority", "description": "Adjust the monitoring priority for a satellite.", "parameters": {"type": "object", "properties": {"satellite_id": {"type": "string", "description": "The ID of the satellite."}, "monitoring_priority": {"type": "number", "description": "The revised monitoring priority percentage."}}, "required": ["satellite_id", "monitoring_priority"]}}, {"name": "list_satellites", "description": "List the information of all satellites.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "retrieve_dataset", "description": "Retrieve a dataset for analysis. Requires the dataset name.", "parameters": {"type": "object", "properties": {"dataset_name": {"type": "string", "description": "The name of the dataset to retrieve."}}, "required": ["dataset_name"]}}]