from environments.BaseEnv import BaseEnv

class IdentityVerification(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.articles = self.parameters.get('articles', [])
        self.certificates = self.parameters.get('certificates', [])

    def verify_identity(self, *, profile_link):
        return {'success': True, 'data': {'message': 'The identity in the profile link has been verified to be true.'}}

    def verify_identity_name_account(self, *, name, account_number):
        for identity in self.parameters['identities']:
            if identity['name'] == name or identity['account_number'] == account_number:
                if identity['verified']:
                    return {'success': True, 'data': {'message': f'The identity has been verified to be true.'}}
                else:
                    return {'success': True, 'data': {'message': f'The identity could not be verified.'}}

        return {'success': False, 'data': {'message': 'The identity could not be found.'}}

    def list_identity_records(self):
        identity_records = self.parameters.get("identity_records", [])
        return {'success': True, 'data': identity_records}
    
    def update_identity_record(self, *, user_id, verified):
        identity_records = self.parameters.get("identity_records", [])
        for identity_record in identity_records:
            if identity_record.get("user_id") == user_id:
                identity_record["verified"] = verified
                return {'success': True, 'data': 'Identity record updated.'}
        return {'success': False, 'data': 'Identity record not found.'}
    
    def fetch_article(self, *, title):
        for article in self.articles:
            if title == article.get('title', None):
                return {'success': True, 'article': article}

        return {'success': False, 'message': f"No article with the title {title}."}
    
    def verify_article(self, *, title):
        for certificate in self.certificates:
            if title == certificate.get('title', None):
                return {'success': True, 'certificate': certificate}
            
        return {'success': False, 'message': f'No certificate with the title {title}.'}

    def list_verification_requests(self, max_results=5):
        verification_requests = self.parameters.get("verification_requests", [])
        return {"success": True, "verification_requests": verification_requests}
