[{"name": "access_database", "description": "Access specified database to retrieve climate strategies and research.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "email_report", "description": "Email the generated report to specified contacts.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content of the report."}, "recipients": {"type": "array", "items": {"type": "string", "description": "Email address of the recipient."}}}, "required": ["content", "recipients"]}}, {"name": "read_emission_data", "description": "Read emission data to produce insights and assessment on environmental impact.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "generate_carbon_report", "description": "Generates a carbon report based on provided emission data and chosen metrics.", "parameters": {"type": "object", "properties": {"company_name": {"type": "string", "description": "The name of the company for which to generate the carbon report."}, "metric_preferences": {"type": "string", "description": "Preferences on which metrics or factors to emphasize in the carbon report."}}, "required": ["company_name", "metric_preferences"]}}, {"name": "generate_report", "description": "The tool generates a report on climate change mitigation strategies.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "review_projects", "description": "Reviews the current details of all projects.", "parameters": {"type": "object", "properties": {}, "required": []}}]