from environments.BaseEnv import BaseEnv

class AutonomousWildlifePreservation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.notifications = self.parameters.get('notifications', [])
        self.food_supplies = self.parameters.get('food_supplies', {})
        self.food_requirements = self.parameters.get('food_requirements', {})
        self.current_weather = self.parameters.get('current_weather', {})
        self.animal_habitats = self.parameters.get('animal_habitats', {})
        
    def post_update(self, *, content):
        return {'success': True, 'data': 'Update posted successfully.'}
    
    def get_population_data(self, *, source):
        current_population_data = self.parameters.get('current_population_data', [])
        for d in current_population_data:
            if d['source'] == source:
                return {'success': True, 'data': d}
        
        if len(current_population_data) == 1:
            return {'success': True, 'data': current_population_data[0]}
        
        return {'success': False, 'data': 'Source not found.'}
    
    def list_urgent_notifications(self):
        return {'success': True, 'notifications': self.notifications}
    
    def deactivate_notification(self, *, notification_id):
        for notification in self.notifications:
            if notification['notification_id'] == notification_id:
                notification['active'] = False
                return {'success': True}
            
        return {'success': False, 'message': f"No notification with id {notification_id}."}
    
    def get_food_supplies(self):
        return {'success': True, 'food_supplies': self.food_supplies}
    
    def get_food_requirements(self):
        return {'success': True, 'food_requirements': self.food_requirements}
    
    def update_food_supplies(self, *, animal_type, quantity):
        if animal_type in self.food_supplies:
            self.food_supplies[animal_type] = quantity
            return {'success': True}
        
        return {'success': False, 'message': f"Animal {animal_type} not found."}
    def access_weather_data(self):
        return {'success': True, 'data': self.current_weather}
    
    def list_animal_habitats(self):
        return {'success': True, 'data': self.animal_habitats}
    
    def modify_environmental_controls(self, *, zone, control, value):
        return {'success': True, 'data': f'{control} in zone {zone} set to {value}.'}
