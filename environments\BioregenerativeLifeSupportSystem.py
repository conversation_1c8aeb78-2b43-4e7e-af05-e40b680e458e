from environments.BaseEnv import BaseEnv

class BioregenerativeLifeSupportSystem(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.oxygen_levels = self.parameters.get('oxygen_levels', [])
        self.systems = self.parameters.get('systems', [])
        self.atmospheric_conditions = self.parameters.get('atmospheric_conditions', {})
        self.habitat_zones = self.parameters.get('habitat_zones', [])
        self.system_status = self.parameters.get('system_status', {})

    def list_greenhouses(self):
        greenhouses = self.parameters.get('greenhouses', [])
        return {'success': True, 'data': greenhouses}

    def adjust_light_exposure(self, *, greenhouse_id, light_exposure_hours):
        greenhouses = self.parameters.get('greenhouses', [])
        for greenhouse in greenhouses:
            if greenhouse["greenhouse_id"] == greenhouse_id:
                greenhouse['light_exposure_hours'] = light_exposure_hours
                return {'success': True, 'data': 'Light exposure adjusted successfully.'}
            
        return {'success': False, 'message': 'Greenhouse not found.'}
    
    def list_resources(self):
        return {'success': True, 'data': self.parameters.get('resources', [])}
    
    def adjust_resource(self, *, resource_name, new_replenishment_rate):
        resources = self.parameters.get('resources', [])
        for resource in resources:
            if resource["resource_name"] == resource_name:
                resource['replenishment_rate'] = new_replenishment_rate
                return {'success': True, 'data': 'Resource replenishment rate adjusted.'}
            
        return {'success': False, 'message': 'Resource not found.'}
    
    def check_oxygen_levels(self):
        return {'success': True, 'oxygen_levels': self.oxygen_levels}
    
    def adjust_oxygen_levels(self, *, percentage):
        return {'success': True}
    def list_systems(self):
        return {'success': True, 'data': self.systems}
    
    def get_atmospheric_conditions(self):
        return {'success': True, 'data': self.atmospheric_conditions}
    
    def adjust_system_setting(self, *, system_id, new_setting):
        for system in self.systems:
            if system['system_id'] == system_id:
                system['current_setting'] = new_setting
                return {'success': True, 'data': 'System settings adjusted.'}
            
        return {'success': False, 'message': 'System not found.'}
    
    
    def list_habitat_zones(self):
        return {'success': True, 'zones': self.habitat_zones}
    
    def adjust_oxygen_concentration(self, *, zone_id, oxygen_concentration):
        for zone in self.habitat_zones:
            if zone['zone_id'] == zone_id:
                zone['current_oxygen_concentration'] = oxygen_concentration
                return {'success': True}
            
        return {'success': False, 'message': f"No zone with id {zone_id}."}
    def get_system_status(self):
        return {'success': True, 'data': self.system_status}
    
    def adjust_system_parameter(self, *, parameter, value):
        self.system_status[parameter] = value
        return {'success': True, 'data': 'System parameter adjusted.'}
