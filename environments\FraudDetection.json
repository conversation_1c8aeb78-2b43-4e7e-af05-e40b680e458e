[{"name": "list_flagged_transactions", "description": "Retrieve a list of current flagged transactions for review.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "remove_duplicate_entries", "description": "Remove any duplicate transactions to clear up space in records.", "parameters": {"type": "object", "properties": {"transaction_id": {"type": "string", "description": "The ID of the transaction to be marked as duplicate."}, "keep_one": {"type": "boolean", "description": "Flag to indicate whether to keep one of the transaction instance (set to false by default)."}}, "required": ["transaction_id"]}}]