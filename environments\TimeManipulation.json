[{"name": "get_attendee_list", "description": "Retrieve the list of conference attendees.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_time_for_attendee", "description": "Adjust the attendee's scheduled time according to the provided time zone and daylight saving rules.", "parameters": {"type": "object", "properties": {"attendee_name": {"type": "string", "description": "The name of the attendee whose time needs to be adjusted."}, "new_time_zone": {"type": "string", "description": "The new time zone or daylight saving adjustment for the attendee."}}, "required": ["attendee_name", "new_time_zone"]}}, {"name": "adjust_time_schedule", "description": "Adjusts the meeting times based on new time zone or user preferences.", "parameters": {"type": "object", "properties": {"meeting_id": {"type": "string", "description": "The unique identifier of the meeting to be adjusted."}, "new_time": {"type": "string", "description": "The new start time for the meeting, with format 'YYYY-MM-DDTHH:MM:SS'."}}, "required": ["meeting_id", "new_time"]}}, {"name": "get_time_schedule", "description": "Get the current time schedule.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_files", "description": "List all files.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_timestamp", "description": "Change the timestamp of a specific file.", "parameters": {"type": "object", "properties": {"filename": {"type": "string", "description": "The name of the file to change the timestamp."}, "new_timestamp": {"type": "string", "description": "The new timestamp to set for the file."}}, "required": ["filename", "new_timestamp"]}}, {"name": "cancel_appointment", "description": "Cancel an existing appointment.", "parameters": {"type": "object", "properties": {"appointment_id": {"type": "string", "description": "The id of the appointment to be cancelled."}}, "required": ["appointment_id"]}}, {"name": "list_appointments", "description": "List all existing appointments.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_timelines", "description": "Adjust the timelines of specific projects within pre-approved constraints to optimize efficiency.", "parameters": {"type": "object", "properties": {"timeline_id": {"type": "string", "description": "The ID of the timeline to be adjusted."}, "new_end_date": {"type": "string", "description": "The new end date for the timeline in YYYY-MM-DD format."}}, "required": ["timeline_id", "new_end_date"]}}, {"name": "get_timelines", "description": "Get the current timelines.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "review_timeline_constraints", "description": "Review constraints applicable to timeline adjustments to ensure compliance with regulatory standards.", "parameters": {"type": "object", "properties": {}, "required": []}}]