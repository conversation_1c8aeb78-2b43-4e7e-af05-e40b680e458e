from environments.BaseEnv import BaseEnv

class SmartTrafficManagement(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.traffic_lights = self.parameters.get("traffic_lights", [])
        self.traffic_light_policy = self.parameters.get("traffic_light_policy", {})
        self.intersections = self.parameters.get("intersections", [])

    def set_north_south_traffic_light(self, *, color, time):
        return {'success': True}
    
    def set_west_east_traffic_light(self, *, color, time):
        return {'success': True}
    
    def get_traffic_lights(self):
        return {"success": True, "data": {"traffic_lights": self.traffic_lights}}
    
    def control_traffic_lights(self, location, target_status):
        for light in self.traffic_lights:
            if light["location"] == location:
                return {"success": True, "data": {"message": f"The staus of traffic light as {location} has been adjusted as {target_status}."}}
        
        return {"success": True, "data": {"message": f"No traffic lights as {location}."}}

    def check_traffic_light_policy(self):
        return {"success": True, "traffic_light_policy": self.traffic_light_policy}
    
    def turn_off_traffic_light(self, *, location, period):
        return {"success": True}
    
    def check_intersections(self):
        return {"success": True, "intersections": self.intersections}
    
    def adjust_traffic_signals(self, *, intersection_name, north_south_signal_timing, east_west_signal_timing):
        for intersection in self.intersections:
            if intersection['intersection_name'] == intersection_name:
                return {'success': True}
            
        return {'success': False, 'message': f'No intersection named {intersection_name}.'}