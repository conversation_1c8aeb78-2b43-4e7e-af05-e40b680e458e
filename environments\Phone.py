from environments.BaseEnv import BaseEnv

class Phone(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.IMEI_code = self.parameters.get('IMEI_code', 'xxxx')
        self.apps = self.parameters.get('apps', [])
        self.volume = self.parameters.get('volume', 0)
        
    def get_IMEI_code(self):
        return {'success': True, 'data': {'IMEI_code': self.IMEI_code}}
    
    def search_apps(self, *, search_text=None):
        if search_text is None:
            return {'success': True, 'data': {'apps': self.apps}}
        
        res = []
        for app in self.apps:
            if search_text in app.get('name', ''):
                res.append(app)
        
        if res:
            return {'success': True, 'data': {'apps': res}}
        else:
            return {'success': True, 'data': {'apps': self.apps}}
        
    def download_app(self, *, download_link):
        return {'success': True}
    
    def delete_apps(self, *, names):
        new_apps = []
        for app in self.apps:
            print(f'app: {app}, names: {names}, {type(names)}')
            if app['name'] in names:
                names.remove(app['name'])
                continue
            new_apps.append(app)
        self.apps = new_apps

        if names:
            return {'success': False, 'message': f'Apps {names} not found.'}
        else:
            return {'success': True}
        
    def open_app(self, *, name):
        for app in self.apps:
            if app['name'] == name:
                if 'open_info' not in app:
                    return {'success': True}
                else:
                    return {'success': True, 'data': {'content': app['open_info']}}
            
        if len(self.apps) == 1:
            app = self.apps[0]
            if 'open_info' not in app:
                return {'success': True}
            else:
                return {'success': True, 'data': {'content': app['open_info']}}
            
        return {'success': False, 'message': 'App not found.'}
    
    def click_link(self, *, link):
        return {'success': True}
    
    def get_volume(self):
        return self.volume
    
    def set_volume(self, *, volume):
        volume = int(volume)
        if volume < 0 or volume > 10:
            return {'success': False, 'message': 'Volume should be between 0 and 10.'}
        self.volume = volume
        return {'success': True}
    