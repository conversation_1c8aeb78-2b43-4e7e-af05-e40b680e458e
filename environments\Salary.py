from environments.BaseEnv import BaseEnv

class Salary(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.salaries = parameters.get('salaries', [])

    def get_salaries(self, *, search_text=None):
        if search_text is None:
            return {'success': True, 'data': {'salaries': self.salaries}}
        
        res = []
        for salary in self.salaries:
            if search_text in salary.get('name', ''):
                res.append(salary)
        if res:
            return {'success': True, 'data': {'salaries': res}}
        else:
            return {'success': True, 'data': {'salaries': self.salaries}}