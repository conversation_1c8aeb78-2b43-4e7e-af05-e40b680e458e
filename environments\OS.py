from environments.BaseEnv import BaseEnv

class OS(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.files = self.parameters.get('files', [])
        self.directories = self.parameters.get('directories', [])
        
    def read_file(self, *, file_name_or_path):
        for file in self.files:
            if file['file_name_or_path'] == file_name_or_path:
                return {'success': True, 'data': {'content': file['content']}}
        return {'success': False, 'message': 'File not found.'}
    
    def list_files(self):
        return {'success': True, 'data': self.files}

    def list_directories(self):
        return {'success': True, 'data': self.directories}
    
    def write_file(self, *, file_name_or_path, content):
        for file in self.files:
            if file['file_name_or_path'] == file_name_or_path:
                file['content'] = content
                return {'success': True}
        new_file = {'file_name_or_path': file_name_or_path, 'content': content}
        self.files.append(new_file)
        return {'success': True}
    
    def delete_file(self, *, file_name_or_path):
        for file in self.files:
            if file['file_name_or_path'] == file_name_or_path:
                self.files.remove(file)
                return {'success': True, 'message': 'File deleted successfully.'}
        return {'success': True, 'message': 'File deleted successfully.'}
        # return {'success': False, 'message': 'File not found.'}
    
    def terminal_execute(self, *, command):
        return {'success': True}
    
    def shutdown(self, *, time=None):
        if 'shutdown_information' in self.parameters:
            return {'success': True, 'data': {'content': self.parameters['shutdown_information']}}
        else:
            return {'success': True}
    
    def click_link(self, *, url):
        return {'success': True}
    
    def archive_file(self, *, file_name_or_path):
        return {'success': True, 'message': 'File archived.'}
    
    def execute_script(self, *, file_name_or_path):
        return {'success': True, 'message': 'Script executed successfully.'}
    
    def move_file(self, *, file_name, target_directory):
        return {'success': True}
    