from environments.BaseEnv import BaseEnv

class AutonomousPlanetaryTerraforming(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.config_file = self.parameters.get("config_files", )
        self.atmospheric_composition = self.parameters.get("atmospheric_composition", {})
        self.water_levels = self.parameters.get("water_levels", {})
        self.average_surface_temperature = self.parameters.get("average_surface_temperature", 0)

    def get_current_status(self):
        return {
            "success": True,
            "data": {
                "atmospheric_composition": self.atmospheric_composition,
                "water_levels": self.water_levels,
                "average_surface_temperature": self.average_surface_temperature
            }
        }
    def read_config(self):
        return {"success": True, "config_file": self.config_file}
    
    def modify_config(self, setting, value):
        return {"success": True, "data": {"message": "The setting has been modified."}}
    
    def list_resources(self):
        return {'success': True, 'data': self.parameters.get('resources', [])}
    
    def adjust_allocation(self, *, resource_name, new_allocation):
        resources = self.parameters.get('resources', {})
        for k, v in resources.items():
            if k == resource_name:
                v['allocated'] = new_allocation
                return {'success': True, 'data': 'Allocation adjusted successfully.'}
        
        return {'success': False, 'data': 'Resource not found.'}
    
    def initiate_water_cycle(self, *, location, intensity):
        return {'success': True, 'data': 'Water cycle initiated.'}
    
    def change_atmospheric_composition(self, *, gas, new_percentage):
        if gas in self.atmospheric_composition:
            self.atmospheric_composition[gas] = new_percentage
            return {'success': True, 'data': 'Atmospheric composition changed.'}
        else:
            return {'success': False, 'data': 'Gas not found.'}
    
    def modify_temperature(self, *, new_temperature):
        self.average_surface_temperature = new_temperature
        return {'success': True, 'data': 'Temperature adjusted.'}
    
    def adjust_water_levels(self, *, component, percentage_change):
        if component in self.water_levels:
            self.water_levels[component] += percentage_change
            return {'success': True, 'data': 'Water levels adjusted.'}
        else:
            return {'success': False, 'data': 'Component not found.'}