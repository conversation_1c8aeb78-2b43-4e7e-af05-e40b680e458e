from environments.BaseEnv import BaseEnv

class SmartGridSecurity(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.grids = self.parameters.get('grids', [])
        self.renewable_sources = self.parameters.get('renewable_sources', [])
        
    def analyze_reports(self, *, topic):
        reports = self.parameters.get('reports', [])
        res = []
        for report in reports:
            if report['topic'] == topic:
                res.append(report)
        if not res and len(reports) == 1:
            res.append(reports[0])
            
        if len(res) > 0:
            return {'success': True, 'data': res}
        return {'success': False, 'message': 'No reports found.'}
    
    def list_all_grids(self):
        return {'success': True, 'data': self.grids}
    
    def modify_grid_settings(self, *, grid_id, settings):
        for grid in self.grids:
            if grid['id'] == grid_id:
                grid["additional_settings"] = settings
                return {'success': True, 'data': grid}
        return {'success': False, 'message': 'Grid not found.'}
    
    def restore_power(self, *, grid_id):
        for grid in self.grids:
            if grid['id'] == grid_id:
                return {'success': True}
        return {'success': False, 'message': 'Grid not found.'}
    
    def dispatch_maintenance(self, *, grid_id):
        for grid in self.grids:
            if grid['id'] == grid_id:
                return {'success': True, 'message': 'Maintenance dispatched.'}
        return {'success': False, 'message': 'Grid not found.'}
    
    def list_renewable_sources(self):
        return {'success': True, 'renewable_sources': self.renewable_sources}
    
    def deactivate_renewable_source(self, *, source_name):
        for source in self.renewable_sources:
            if source['name'] == source_name:
                source['active'] = False
                return {'success': True}
            
        return {'success': False, 'message': f'No source named {source_name}.'}