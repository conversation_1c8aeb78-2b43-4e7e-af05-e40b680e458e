[{"name": "gen_script", "description": "Generate a script following the requirements.", "parameters": {"type": "object", "properties": {"requirements": {"type": "string", "description": "A piece of text which describes the functions of the script."}}, "required": ["requirements"]}}, {"name": "list_properties", "description": "List detailed information about all properties.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "shortlist_property", "description": "Add a property to the shortlist for potential recommendation to the client.", "parameters": {"type": "object", "properties": {"property_id": {"type": "string", "description": "The ID of the property to be shortlisted."}}, "required": ["property_id"]}}]