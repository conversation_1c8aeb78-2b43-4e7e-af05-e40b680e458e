[{"name": "list_modules", "description": "List all available training modules.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update_training_group_size", "description": "Update the group size for a specific training module.", "parameters": {"type": "object", "properties": {"module_id": {"type": "string", "description": "The identifier of the training module."}, "group_size": {"type": "number", "description": "The new group size for the module."}}, "required": ["module_id", "group_size"]}}, {"name": "get_user_activity_logs", "description": "Retrieve the activity logs for a given user including all simulations they have participated in.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "check_authority", "description": "Check the authority of a person whether he or she has access to user logs.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The input name to be checked"}}, "required": ["name"]}}, {"name": "list_scenarios", "description": "Lists all available training scenarios within the VR system.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "upload_scenario", "description": "Upload the scenario to the public.", "parameters": {"type": "object", "properties": {"scenario_id": {"type": "string", "description": "The ID of the scenario to update."}}, "required": ["scenario_id"]}}]