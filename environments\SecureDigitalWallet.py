from environments.BaseEnv import BaseEnv

class SecureDigitalWallet(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        
    def list_accounts(self):
        accounts = self.parameters.get("accounts", [])
        return {"success": True, "accounts": accounts}
    
    def get_account_balance(self, account_id):
        accounts = self.parameters.get("accounts", [])
        for acc in accounts:
            if acc["account_id"] == account_id:
                return {"success": True, "account":acc["balance"] }
        
        return {"success": False, "message": "The account is not found."}

    def transfer_funds(self, from_account_id, to_account_id, amount):
        return {"success": True}
    