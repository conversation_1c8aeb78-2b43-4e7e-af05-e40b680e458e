[{"name": "get_all_therapy_session_details", "description": "Retrieve the details of all therapy session, including facilitator, topics discussed, and confidentiality notice.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list_sessions", "description": "List all therapy sessions.", "parameters": {"type": "object", "properties": {}}}, {"name": "list_notes", "description": "Lists all notes available for patients.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_note_content", "description": "Modify the content of a note with specific ID.", "parameters": {"type": "object", "properties": {"note_id": {"type": "string", "description": "The ID of the note whose content is to be modified."}, "new_content": {"type": "string", "description": "The new content to be modified."}}, "required": ["note_id", "new_content"]}}]