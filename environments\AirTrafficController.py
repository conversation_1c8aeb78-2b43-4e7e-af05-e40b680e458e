from environments.BaseEnv import BaseEnv


class AirTrafficController(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.flights = self.parameters.get("flights", [])

    def list_flight_info(self):
        return {"success": True, "data": self.flights}

    def reroute_flight(self, flight_id, new_path):
        for flight in self.flights:
            if flight["flight_id"] == flight_id:
                flight["current_path"] = new_path
                flight["new_path_suggestion"] = None
                return {"success": True, "message": "Flight rerouted"}
        return {"success": False, "message": "Flight not found"}
