[{"name": "unlock_door", "parameters": {"type": "object", "properties": {}, "required": []}, "description": "Unlocks the door."}, {"name": "add_guest", "description": "Adds a guest to the system.", "parameters": {"type": "object", "properties": {"guest_name": {"type": "string", "description": "Name of the guest to add."}, "guest_email": {"type": "string", "description": "Email address of the guest to add."}}, "required": ["guest_name", "guest_email"]}}, {"name": "delete_guest", "description": "Deletes a guest from the system.", "parameters": {"type": "object", "properties": {"guest_ids": {"type": "array", "description": "An array of guest IDs to delete.", "items": {"type": "string"}}}, "required": ["guest_ids"]}}, {"name": "check_lock_status", "description": "Retrieves the current lock status of the door.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "view_access_history", "description": "Retrieves access history.", "parameters": {"type": "object", "properties": {"start_time": {"type": "string", "description": "Start time of the history period in YYYY-MM-DD HH:mm format."}, "end_time": {"type": "string", "description": "End time of the history period in YYYY-MM-DD HH:mm format."}}, "required": []}}, {"name": "grant_access", "description": "Grant access to home for somebody.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the person."}, "email": {"type": "string", "description": "The email of the person."}, "period": {"type": "string", "description": "The effective period of the access."}, "permanent": {"type": "boolean", "description": "Whether the granted access is permanent."}}, "required": ["name", "period", "permanent"]}}, {"name": "remove_access", "description": "Remove access to home for somebody.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the person."}, "email": {"type": "string", "description": "The email of the person."}}, "required": ["name"]}}, {"name": "search_guests", "description": "Search recorded guests.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The information of the person."}}, "required": ["search_text"]}}, {"name": "search_devices", "description": "Search all relevant devices.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The information of the device."}}, "required": []}}, {"name": "turn_off_devices", "description": "Turn off specified devices.", "parameters": {"type": "object", "properties": {"names": {"type": "array", "description": "The names of the devices.", "items": {"type": "string", "description": "the name of the device."}}}, "required": ["names"]}}, {"name": "get_all_objects", "description": "Get all objects.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "search_objects", "description": "Search all relevant objects.", "parameters": {"type": "object", "properties": {"search_text": {"type": "string", "description": "The information of the object."}}, "required": []}}, {"name": "get_allowed_operations", "description": "Get allowed operations for an object.", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the object."}}, "required": ["name"]}}, {"name": "perform_operation", "description": "Perform an operation for an object.", "parameters": {"type": "object", "properties": {"id": {"type": "string", "description": "The id of the operation."}}, "required": ["id"]}}, {"name": "activate_alarm", "description": "Activate high decibel alarm.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "contact_owner", "description": "Contact the homeowner.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "set_reminder", "description": "Sets a reminder for a specified date and time.", "parameters": {"type": "object", "properties": {"reminder_text": {"type": "string", "description": "The text of the reminder."}, "date_time": {"type": "string", "description": "The date and time for the reminder, in the format 'YYYY-MM-DD HH:MM'."}}, "required": ["reminder_text", "date_time"]}}, {"name": "query_aid_impact", "description": "Retrieve information about humanitarian aid impacts and challenges in a specified conflict zone.", "parameters": {"type": "object", "properties": {"region": {"type": "string", "description": "The conflict zone region. Example: 'Region A'."}}, "required": ["region"]}}]