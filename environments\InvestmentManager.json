[{"name": "get_holdings_information", "description": "Retrieve information about the holdings in an account that belongs to the User.", "parameters": {"type": "object", "properties": {"account": {"type": "string", "description": "The account type, one of ['non-registered', 'TFSA', 'RRSP']."}}, "required": ["account"]}}, {"name": "list_transactions", "description": "Lists the transactions for a particular account.", "parameters": {"type": "object", "properties": {"account": {"type": "string", "description": "The account type, one of ['non-registered', 'TFSA', 'RRSP']."}, "start_date": {"type": "string", "description": "The start date of the transactions in the format 'yyyy-mm-dd'. <PERSON><PERSON><PERSON> is the start of the account."}, "end_date": {"type": "string", "description": "The end date of the transactions in the format 'yyyy-mm-dd'. <PERSON><PERSON><PERSON> is today."}, "max_results": {"type": "integer", "description": "The maximum number of results to return, default is 10, must be between 1 and 50."}}, "required": ["account"]}}, {"name": "search_investments", "description": "Search available investments by keyword.", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "The keyword to search for."}, "type": {"type": "string", "description": "The type of investment to search for, e.g. 'mutual_fund', 'ETF', 'GIC'."}, "max_results": {"type": "integer", "description": "The maximum number of results to return, default is 5, maximum is 10."}}, "required": ["query", "type"]}}, {"name": "make_transaction", "description": "Buy investment options or sell holdings.", "parameters": {"type": "object", "properties": {"account": {"type": "string", "description": "The account type, one of ['non-registered', 'TFSA', 'RRSP']."}, "action": {"type": "string", "description": "Whether to buy or sell the mutual fund. One of ['buy', 'sell']"}, "name": {"type": "string", "description": "The name of the investment option to buy or the holding to sell."}, "amount": {"type": "number", "description": "The total amount to be purchased, in USD."}}, "required": ["account", "action", "name", "amount"]}}]