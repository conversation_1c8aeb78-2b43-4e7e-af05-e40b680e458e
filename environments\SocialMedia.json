[{"name": "get_user_profile", "description": "Retrieve the user's own profile or another user's profile information by their ID.", "parameters": {"type": "object", "properties": {"user_id": {"type": "string", "description": "The unique identifier of the user to retrieve the profile information for. If not provided, return own profile."}}, "required": ["user_id"]}}, {"name": "search_posts", "description": "Search for posts by keyword.", "parameters": {"type": "object", "properties": {"keyword": {"type": "string", "description": "The keyword to search for."}, "max_results": {"type": "integer", "description": "The maximum number of search results to return. Default value is 10."}}, "required": ["keyword"]}}, {"name": "post", "description": "Post something to the platform.", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "The content of the post."}}, "required": ["content"]}}, {"name": "read_post", "description": "Read a post on the platform.", "parameters": {"type": "object", "properties": {"post_name_or_path": {"type": "string", "description": "The name or path of the post."}}, "required": ["post_name_or_path"]}}, {"name": "reply_to_post", "description": "Reply to a post by its ID.", "parameters": {"type": "object", "properties": {"post_id": {"type": "string", "description": "The unique identifier of the post to reply to."}, "content": {"type": "string", "description": "The content of the reply, max length 280 characters."}}, "required": ["post_id", "content"]}}]