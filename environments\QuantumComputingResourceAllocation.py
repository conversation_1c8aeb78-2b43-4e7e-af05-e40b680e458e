from environments.BaseEnv import BaseEnv

class QuantumComputingResourceAllocation(BaseEnv):
    def __init__(self, *,parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.tasks = self.parameters.get('tasks', [])

    def list_project_allocations(self):
        projects = self.parameters.get('projects', [])
        return {'success': True, 'data': projects}

    def list_resources(self):
        return {'success': True, 'data': self.parameters.get('resources', {})}

    def list_tasks(self):
        return {'success': True, 'data': self.parameters.get('tasks', [])}

    def allocate_resources(self, *, task_id, quantum_cores):
        resources = self.parameters.get('resources', {})
        tasks = self.parameters.get('tasks', [])
        if quantum_cores > resources['quantum_cores']:
            return {'success': False, 'data': 'Insufficient quantum cores.'}

        for task in tasks:
            if task['id'] == task_id:
                task["required_cores"] = max(0, task["required_cores"] - quantum_cores)
                resources['quantum_cores'] -= quantum_cores
                return {'success': True, 'data': 'Resources allocated.'}

        return {'success': False, 'data': 'Task ID not found.'}
    
    def list_current_tasks(self):
        return {'success': True, 'tasks': self.tasks}
    
    def pause_task(self, *, task_id):
        for task in self.tasks:
            if task['task_id'] == task_id:
                task['status'] = 'paused'
                return {'success': True}
            
        return {'success': False, 'message': f'No task with id {task_id}.'}