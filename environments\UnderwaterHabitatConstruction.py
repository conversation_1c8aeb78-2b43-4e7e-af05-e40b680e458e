from environments.BaseEnv import BaseEnv


class UnderwaterHabitatConstruction(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.current_plan = self.parameters.get("current_plan", {})
        self.projects = self.parameters.get("projects", [])

    def get_current_plan(self):
        return {"success": True, "data": self.current_plan}
    
    def update_schedule(self, *, materials):
        self.current_plan['materials'].append(materials)
        return {"success": True, "data": self.current_plan}
    
    def list_projects(self):
        return {"success": True, "data": self.projects}
    
    def adjust_resource_allocation(self, *, project_name, resource_allocation):
        for project in self.projects:
            if project['name'] == project_name:
                project['resources'] = resource_allocation
                return {"success": True, "data": project}
        return {"success": False, "message": "Project not found"}