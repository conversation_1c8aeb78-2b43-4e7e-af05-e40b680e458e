[{"name": "read_config", "description": "Read the configuration settings from a file.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "modify_config", "description": "Modify a specific configuration setting in a configuration file.", "parameters": {"type": "object", "properties": {"setting": {"type": "string", "description": "The configuration setting to modify."}, "value": {"type": "string", "description": "The new value for the configuration setting."}}, "required": ["setting", "value"]}}, {"name": "list_resources", "description": "List all current resource allocations and sources.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "adjust_allocation", "description": "Adjust the allocation of specified resources. Ensure changes do not exceed resource capacity.", "parameters": {"type": "object", "properties": {"resource_name": {"type": "string", "description": "The name of the resource (e.g., water, energy, materials)."}, "new_allocation": {"type": "string", "description": "The new allocation amount for the resource."}}, "required": ["resource_name", "new_allocation"]}}, {"name": "initiate_water_cycle", "description": "Start the processes required to circulate and clean water for use.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "Geographical location on the planet where the cycle is to be initiated"}, "intensity": {"type": "string", "description": "The operational capacity at which the water cycle should run"}}, "required": ["location", "intensity"]}}, {"name": "change_atmospheric_composition", "description": "Adjust the percentages of gases in the planetary atmosphere.", "parameters": {"type": "object", "properties": {"gas": {"type": "string", "description": "Name of the gas to be adjusted."}, "new_percentage": {"type": "number", "description": "The new percentage composition of the gas."}}, "required": ["gas", "new_percentage"]}}, {"name": "adjust_water_levels", "description": "Modify the distribution of water on the planet.", "parameters": {"type": "object", "properties": {"component": {"type": "string", "description": "The water component to adjust (oceans, ice_caps, rivers)."}, "percentage_change": {"type": "number", "description": "The percentage change to apply."}}, "required": ["component", "percentage_change"]}}, {"name": "modify_temperature", "description": "Change the average surface temperature of the planet.", "parameters": {"type": "object", "properties": {"new_temperature": {"type": "number", "description": "The new average surface temperature."}}, "required": ["new_temperature"]}}, {"name": "get_current_status", "description": "Retrieve the current status of the planetary terraforming process.", "parameters": {"type": "object", "properties": {}, "required": []}}]