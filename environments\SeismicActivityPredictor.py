from environments.BaseEnv import BaseEnv

class SeismicActivityPredictor(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.areas = self.parameters.get("areas", [])
        self.datasets = self.parameters.get('datasets', [])
        self.tools = self.parameters.get('tools', [])
        
    def predict_seismic_activity(self):
        return self.parameters.get('predict_result', "Everything is fine.")
    
    def list_areas(self):
        return self.areas
    
    def update_risk_categories(self, *, area_name, new_risk_category):
        for area in self.areas:
            if area['area_name'] == area_name:
                area['current_risk_category'] = new_risk_category
                return {"success": True}
            
        return {"success": False, "message": "Area not found."}
    
    def list_datasets(self):
        return {'success': True, 'datasets': self.datasets}
    
    def delete_dataset(self, *, name):
        for dataset in self.datasets:
            if dataset['name'] == name:
                return {'success': True}
            
        return {'success': False, 'message': f"No dataset named {name}."}
    
    def list_tools(self):
        return {'success': True, 'tools': self.tools}
    
    def deactivate_tool(self, *, tool_name):
        for tool in self.tools:
            if tool['name'] == tool_name:
                tool['active'] = False
                return {'success': True}
            
        return {'success': False, 'message': f"No tool named {tool_name}."}
