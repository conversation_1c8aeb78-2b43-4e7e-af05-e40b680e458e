from environments.BaseEnv import BaseEnv

class AutonomousNavigation(BaseEnv):
    def __init__(self, *, parameters=None):
        super().__init__()
        self.parameters = parameters if parameters is not None else {}
        self.navigation_paths = self.parameters.get('navigation_paths', [])

    def get_navigation_path(self, *, start, end):
        for path in self.navigation_paths:
            if start == path.get("start", None) and end == path.get("end", None):
                return {'success': True, 'navigation_path': path}
            
        return {'success': False, 'message': f'Navigation path not found from {start} to {end}.'}